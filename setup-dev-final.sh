#!/bin/bash

# CryptoYield Development Environment Setup (Final Working Version)
# G<PERSON><PERSON><PERSON> quyết vấn đề MongoDB version compatibility

set -e

echo "🚀 Thiết lập môi trường phát triển CryptoYield (Phiên bản hoạt động)..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[SETUP]${NC} $1"
}

# Step 1: Kiểm tra prerequisites
print_header "Kiểm tra prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker chưa được cài đặt. Vui lòng cài đặt Docker và thử lại."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose chưa được cài đặt. Vui lòng cài đặt Docker Compose và thử lại."
    exit 1
fi

print_success "Docker và Docker Compose đã sẵn sàng"

# Step 2: Dừng tất cả services hiện tại
print_header "Dừng tất cả services hiện tại..."
docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
docker-compose -f docker-compose.dev-simple.yml down 2>/dev/null || true
docker-compose -f docker-compose.dev-working.yml down 2>/dev/null || true
docker-compose -f docker-compose.mongo.yml down 2>/dev/null || true

# Step 3: Xóa MongoDB volumes cũ để tránh version conflict
print_header "Xóa MongoDB volumes cũ để tránh version conflict..."
docker volume rm cryptoyield_mongodb_data 2>/dev/null || true
docker volume rm cryptoyield_mongodb_config 2>/dev/null || true
docker volume rm mongodb_data 2>/dev/null || true
docker volume rm mongodb_config 2>/dev/null || true

print_success "Đã xóa volumes cũ"

# Step 4: Build backend development image
print_header "Build backend development image..."
docker-compose -f docker-compose.dev-working.yml build backend

# Step 5: Khởi động services
print_header "Khởi động development services..."
docker-compose -f docker-compose.dev-working.yml up -d

# Step 6: Chờ MongoDB khởi động
print_header "Chờ MongoDB khởi động..."
timeout=180
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.dev-working.yml ps mongodb | grep -q "healthy"; then
        print_success "MongoDB đã sẵn sàng"
        break
    fi
    
    # Kiểm tra logs để xem có lỗi không
    if docker ps | grep -q "cryptoyield-mongodb-dev.*Restarting"; then
        print_warning "MongoDB đang restart... kiểm tra logs..."
        docker logs cryptoyield-mongodb-dev --tail 5 2>/dev/null || true
    fi
    
    print_status "Chờ MongoDB... ($elapsed/$timeout giây)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

if [ $elapsed -ge $timeout ]; then
    print_error "MongoDB không thể khởi động trong $timeout giây"
    print_status "Kiểm tra MongoDB logs..."
    docker logs cryptoyield-mongodb-dev --tail 20
    exit 1
fi

# Step 7: Khởi tạo replica set
print_header "Khởi tạo MongoDB replica set..."
sleep 15

docker exec cryptoyield-mongodb-dev mongosh --eval "
try {
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set đã được khởi tạo: ' + status.set);
    }
} catch (e) {
    if (e.message.includes('no replset config')) {
        print('🔧 Khởi tạo replica set...');
        rs.initiate({
            _id: 'rs0',
            members: [{ _id: 0, host: 'mongodb:27017' }]
        });
        print('✅ Replica set đã được khởi tạo thành công');
    } else {
        print('⚠️ Trạng thái replica set: ' + e.message);
    }
}
" 2>/dev/null || print_warning "Replica set có thể cần thêm thời gian để khởi tạo"

# Step 8: Chờ replica set ổn định
print_status "Chờ replica set ổn định..."
sleep 30

# Step 9: Kiểm tra services
print_header "Kiểm tra services..."

# Test MongoDB
if docker exec cryptoyield-mongodb-dev mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
    print_success "✅ MongoDB có thể truy cập"
else
    print_warning "⚠️ MongoDB có thể vẫn đang khởi tạo"
fi

# Test Redis
if docker exec cryptoyield-redis-dev redis-cli ping 2>/dev/null | grep -q "PONG"; then
    print_success "✅ Redis có thể truy cập"
else
    print_warning "⚠️ Redis có vấn đề kết nối"
fi

# Test Backend
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/health 2>/dev/null || echo "000")
if [[ "$backend_health" == "200" ]]; then
    print_success "✅ Backend có thể truy cập"
else
    print_warning "⚠️ Backend có thể vẫn đang khởi động (HTTP $backend_health)"
fi

# Test Mongo Express
mongo_express_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
if [[ "$mongo_express_health" == "200" || "$mongo_express_health" == "401" ]]; then
    print_success "✅ Mongo Express có thể truy cập"
else
    print_warning "⚠️ Mongo Express có thể vẫn đang khởi động (HTTP $mongo_express_health)"
fi

# Step 10: Kiểm tra khả năng transaction
print_header "Kiểm tra khả năng transaction..."
docker exec cryptoyield-mongodb-dev mongosh --eval "
try {
    const session = db.getMongo().startSession();
    session.startTransaction();
    
    const testDb = session.getDatabase('cryptoyield_dev_test');
    testDb.transactionTest.insertOne({
        test: 'dev_environment_setup_final',
        timestamp: new Date(),
        environment: 'development',
        version: 'working'
    }, {session: session});
    
    session.commitTransaction();
    session.endSession();
    
    print('✅ Test transaction thành công');
} catch (e) {
    print('❌ Test transaction thất bại: ' + e.message);
}
" 2>/dev/null || print_warning "Transaction test có thể cần thêm thời gian"

# Step 11: Hiển thị thông tin cuối cùng
echo ""
print_success "🎉 Thiết lập môi trường phát triển hoàn tất!"
echo ""
echo "📋 Thông tin Services:"
echo "  🖥️  Backend API: http://localhost:5000"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🔗 Chi tiết kết nối:"
echo "  📝 MongoDB URI: mongodb://localhost:27017/cryptoyield?replicaSet=rs0"
echo "  🔑 Redis URI: redis://localhost:6379"
echo ""
echo "📁 Tính năng phát triển:"
echo "  ✅ Live code editing (./backend/src được mount như volume)"
echo "  ✅ Hot-reload được bật (nodemon + ts-node)"
echo "  ✅ Hỗ trợ transaction (MongoDB replica set)"
echo "  ✅ Giao diện quản trị database (Mongo Express)"
echo "  ✅ Networking phù hợp giữa các services"
echo ""

# Hiển thị trạng thái container
print_header "Trạng thái container cuối cùng:"
docker-compose -f docker-compose.dev-working.yml ps

echo ""
print_success "🚀 Môi trường phát triển đã sẵn sàng!"
echo ""
echo "📝 Các bước tiếp theo:"
echo "  1. Chỉnh sửa code backend trong ./backend/src/ - thay đổi sẽ được phản ánh ngay lập tức"
echo "  2. Xem logs: docker-compose -f docker-compose.dev-working.yml logs -f backend"
echo "  3. Truy cập database: http://localhost:8081"
echo "  4. Test API: curl http://localhost:5000/api/health"
echo ""
echo "🔧 Lệnh phát triển:"
echo "  • Khởi động: docker-compose -f docker-compose.dev-working.yml up -d"
echo "  • Dừng: docker-compose -f docker-compose.dev-working.yml down"
echo "  • Logs: docker-compose -f docker-compose.dev-working.yml logs -f backend"
echo "  • Shell: docker exec -it cryptoyield-backend-dev /bin/bash"
echo ""
print_success "Chúc bạn code vui vẻ! 🎯"
