import mongoose from 'mongoose';
import { connectDB } from '../config/database';
import InvestmentPackage from '../models/investmentPackageModel';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import PaymentHistory from '../models/paymentHistoryModel';
import interestCalculationService from '../services/interestCalculationService';
import cronService from '../services/cronService';
import { logger } from '../utils/logger';

/**
 * Test script for the enhanced interest calculation system
 */
class InterestCalculationTest {
  async run() {
    try {
      logger.info('🧪 Starting Interest Calculation Test...');

      // Connect to database
      await connectDB();
      logger.info('✅ Connected to database');

      // Test 1: Check active packages
      await this.testActivePackages();

      // Test 2: Test single package calculation
      await this.testSinglePackageCalculation();

      // Test 3: Test batch calculation
      await this.testBatchCalculation();

      // Test 4: Test manual calculation
      await this.testManualCalculation();

      // Test 5: Verify database records
      await this.verifyDatabaseRecords();

      logger.info('✅ All tests completed successfully!');

    } catch (error: any) {
      logger.error('❌ Test failed:', {
        error: error.message,
        stack: error.stack
      });
    } finally {
      await mongoose.disconnect();
      logger.info('🔌 Disconnected from database');
    }
  }

  async testActivePackages() {
    logger.info('🔍 Test 1: Checking active packages...');
    
    const activePackages = await InvestmentPackage.getActivePackages();
    logger.info(`📦 Found ${activePackages.length} active packages`);

    if (activePackages.length > 0) {
      const pkg = activePackages[0];
      logger.info('📋 Sample package details:', {
        id: pkg._id,
        userId: pkg.userId,
        amount: pkg.amount,
        currency: pkg.currency,
        status: pkg.status,
        activeDays: pkg.activeDays,
        totalEarned: pkg.totalEarned,
        lastCalculatedAt: pkg.lastCalculatedAt
      });
    }
  }

  async testSinglePackageCalculation() {
    logger.info('🔍 Test 2: Testing single package calculation...');
    
    const activePackages = await InvestmentPackage.getActivePackages();
    
    if (activePackages.length === 0) {
      logger.warn('⚠️ No active packages found for single calculation test');
      return;
    }

    const pkg = activePackages[0];
    const beforeState = {
      totalEarned: pkg.totalEarned,
      activeDays: pkg.activeDays,
      lastCalculatedAt: pkg.lastCalculatedAt
    };

    logger.info('📊 Package state before calculation:', beforeState);

    // Calculate interest for single package
    const result = await interestCalculationService.calculatePackageInterest(pkg, null as any);
    
    logger.info('📈 Calculation result:', {
      success: result.success,
      dailyInterest: result.dailyInterest,
      previousBalance: result.previousBalance,
      newBalance: result.newBalance,
      transactionId: result.transactionId,
      paymentHistoryId: result.paymentHistoryId,
      error: result.error
    });
  }

  async testBatchCalculation() {
    logger.info('🔍 Test 3: Testing batch calculation...');
    
    const summary = await interestCalculationService.processAllActivePackages();
    
    logger.info('📊 Batch calculation summary:', {
      totalPackages: summary.totalPackages,
      successfulCalculations: summary.successfulCalculations,
      failedCalculations: summary.failedCalculations,
      totalInterestPaid: summary.totalInterestPaid,
      errorCount: summary.errors.length,
      duration: summary.duration
    });

    if (summary.errors.length > 0) {
      logger.warn('⚠️ Errors during batch calculation:', summary.errors);
    }
  }

  async testManualCalculation() {
    logger.info('🔍 Test 4: Testing manual calculation...');
    
    const result = await cronService.calculateInterestManually();
    
    logger.info('📊 Manual calculation result:', {
      success: result.success,
      processed: result.processed,
      totalInterest: result.totalInterest,
      errorCount: result.errors.length
    });

    if (result.errors.length > 0) {
      logger.warn('⚠️ Errors during manual calculation:', result.errors);
    }
  }

  async verifyDatabaseRecords() {
    logger.info('🔍 Test 5: Verifying database records...');
    
    // Check recent transactions
    const recentTransactions = await Transaction.find({
      type: 'interest',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    }).limit(5);

    logger.info(`💳 Found ${recentTransactions.length} recent interest transactions`);

    // Check recent payment history
    const recentPayments = await PaymentHistory.find({
      paymentType: 'interest',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    }).limit(5);

    logger.info(`📋 Found ${recentPayments.length} recent interest payment records`);

    // Check wallet balances
    const walletsWithInterest = await Wallet.find({
      'assets.interestBalance': { $gt: 0 }
    }).limit(3);

    logger.info(`💰 Found ${walletsWithInterest.length} wallets with interest balance`);

    if (walletsWithInterest.length > 0) {
      const wallet = walletsWithInterest[0];
      const interestAssets = wallet.assets.filter(a => a.interestBalance > 0);
      
      logger.info('💰 Sample wallet interest balances:', {
        userId: wallet.userId,
        totalInterestEarned: wallet.totalInterestEarned,
        interestAssets: interestAssets.map(a => ({
          symbol: a.symbol,
          interestBalance: a.interestBalance
        }))
      });
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new InterestCalculationTest();
  test.run().catch(console.error);
}

export default InterestCalculationTest;
