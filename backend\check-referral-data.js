const mongoose = require('mongoose');

// Simple connection
mongoose.connect('mongodb://localhost:27017/cryptoyield')
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

// Simple check function
const checkReferralData = async () => {
  try {
    console.log('🔍 CHECKING REFERRAL DATA...\n');

    // 1. Check users with referrerId
    const users = await mongoose.connection.db.collection('users').find({
      referrerId: { $exists: true, $ne: null }
    }).toArray();

    console.log(`👥 Users with referrers: ${users.length}`);
    users.forEach((user, i) => {
      console.log(`   ${i+1}. ${user.firstName} ${user.lastName} (${user.email})`);
      console.log(`      ReferrerID: ${user.referrerId}`);
    });

    if (users.length === 0) {
      console.log('❌ NO USERS WITH REFERRERS FOUND!');
      console.log('   This is the main reason commission is not working.');
      console.log('   Users need to be registered with referral codes.\n');
    }

    // 2. Check existing referral commissions
    const commissions = await mongoose.connection.db.collection('referralcommissions').find({}).toArray();
    console.log(`\n💰 Existing referral commissions: ${commissions.length}`);
    commissions.forEach((comm, i) => {
      console.log(`   ${i+1}. Amount: ${comm.amount} ${comm.currency}`);
      console.log(`      Referrer: ${comm.referrerId}`);
      console.log(`      Referred: ${comm.referredId}`);
      console.log(`      Status: ${comm.status}`);
    });

    // 3. Check recent deposits
    const deposits = await mongoose.connection.db.collection('transactions').find({
      type: 'deposit',
      status: 'approved'
    }).sort({ createdAt: -1 }).limit(5).toArray();

    console.log(`\n📊 Recent approved deposits: ${deposits.length}`);
    deposits.forEach((deposit, i) => {
      console.log(`   ${i+1}. User: ${deposit.userId}`);
      console.log(`      Amount: ${deposit.amount} ${deposit.asset}`);
      console.log(`      Status: ${deposit.status}`);
      console.log(`      Date: ${deposit.createdAt}`);
    });

    // 4. Check if any user from deposits has referrer
    if (deposits.length > 0 && users.length > 0) {
      console.log(`\n🔍 Checking if depositing users have referrers...`);
      for (const deposit of deposits) {
        const userWithReferrer = users.find(u => u._id.toString() === deposit.userId.toString());
        if (userWithReferrer) {
          console.log(`✅ User ${userWithReferrer.firstName} ${userWithReferrer.lastName} has referrer and made deposit`);
          console.log(`   Deposit: ${deposit.amount} ${deposit.asset}`);
          console.log(`   Referrer: ${userWithReferrer.referrerId}`);
          
          // Check if commission exists for this pair
          const existingComm = commissions.find(c => 
            c.referrerId.toString() === userWithReferrer.referrerId.toString() &&
            c.referredId.toString() === userWithReferrer._id.toString()
          );
          
          if (existingComm) {
            console.log(`   ❌ Commission already exists: ${existingComm.amount} ${existingComm.currency}`);
          } else {
            console.log(`   ✅ No commission exists - should create new one`);
          }
        }
      }
    }

    // 5. Summary
    console.log(`\n📋 SUMMARY:`);
    console.log(`   Users with referrers: ${users.length}`);
    console.log(`   Existing commissions: ${commissions.length}`);
    console.log(`   Recent deposits: ${deposits.length}`);
    
    if (users.length === 0) {
      console.log(`\n🚨 MAIN ISSUE: No users have referrers!`);
      console.log(`   Solution: Ensure users register with referral codes`);
    } else if (commissions.length >= users.length) {
      console.log(`\n⚠️  POSSIBLE ISSUE: All users already have commissions`);
      console.log(`   Commission only paid once per user (first deposit)`);
    } else {
      console.log(`\n✅ System should work - check logs during deposit approval`);
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

// Run after connection
setTimeout(checkReferralData, 1000);
