import { createContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { socketService } from '../services/socket-service';

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  walletAddress?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  kycVerified: boolean;
  twoFactorEnabled: boolean;
  token: string;
  referralCode?: string;
  referralCount?: number;
  referralEarnings?: number;
  marketingConsent?: boolean;
  isAdmin?: boolean;
  isImpersonating?: boolean;
  impersonatedBy?: {
    adminId: string;
    adminEmail: string;
    adminName: string;
  };
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password?: string, userData?: User) => Promise<User>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
}

interface AuthProviderProps {
  children: ReactNode;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username?: string;
  birthDate?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  referralCode?: string;
  marketingConsent?: boolean;
}

// Create context
export const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: false,
  error: null,
  login: async () => ({} as User),
  register: async () => {},
  logout: () => {},
  updateProfile: async () => {},
  clearError: () => {},
  refreshUser: async () => {},
});

// API base URL - Use environment variable with fallback
const API_URL = import.meta.env.VITE_API_URL || '/api';
console.log('🔧 AuthContext API_URL:', API_URL);
console.log('🔧 Environment:', import.meta.env.NODE_ENV);
console.log('🔧 VITE_API_URL:', import.meta.env.VITE_API_URL);

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { t } = useTranslation('auth');
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize auth state from localStorage and set up cookie-based auth
  useEffect(() => {
    console.log('🚀 AuthContext: Initializing authentication state...');

    // Set axios to include credentials in all requests
    axios.defaults.withCredentials = true;
    console.log(t('info.axiosConfigured'));

    // Check both localStorage and sessionStorage for user data
    const storedUser = localStorage.getItem('user');
    const sessionUser = sessionStorage.getItem('user');
    const loginTime = sessionStorage.getItem('loginTime');

    console.log('📦 AuthContext: Checking stored user data:', {
      hasStoredUser: !!storedUser,
      hasSessionUser: !!sessionUser,
      storedUserLength: storedUser?.length || 0,
      sessionUserLength: sessionUser?.length || 0,
      loginTime: loginTime ? new Date(parseInt(loginTime)).toISOString() : 'none'
    });

    // Prefer sessionStorage if available and recent (within 24 hours)
    let userDataSource = storedUser;
    if (sessionUser && loginTime) {
      const loginAge = Date.now() - parseInt(loginTime);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      if (loginAge < maxAge) {
        userDataSource = sessionUser;
        console.log('📦 AuthContext: Using sessionStorage data (more recent)');
      } else {
        console.log('📦 AuthContext: SessionStorage data too old, using localStorage');
      }
    }

    if (userDataSource) {
      try {
        const userData = JSON.parse(userDataSource);
        console.log('📋 AuthContext: Parsed user data:', {
          hasId: !!userData._id,
          email: userData.email,
          hasToken: !!userData.token
        });

        // Always restore user data if it exists and is valid
        if (userData && userData._id) {
          setUser(userData);
          console.log('✅ AuthContext: User loaded from storage:', userData.email);

          // Verify token validity with backend in a more robust way
          console.log('🔄 AuthContext: Verifying token with backend...');

          const verifyToken = async () => {
            try {
              // First try to refresh token
              const refreshResponse = await axios.post(`${API_URL}/users/refresh-token`, {}, {
                withCredentials: true,
                timeout: 8000 // 8 second timeout
              });

              console.log('✅ AuthContext: Token refresh response:', refreshResponse.status);
              if (refreshResponse.data.status === 'success') {
                // Token is valid, update user data if needed
                const refreshedUserData = refreshResponse.data.data || refreshResponse.data.user || refreshResponse.data;
                const updatedUser = { ...userData, ...refreshedUserData };
                setUser(updatedUser);
                localStorage.setItem('user', JSON.stringify(updatedUser));
                sessionStorage.setItem('user', JSON.stringify(updatedUser));
                console.log('✅ AuthContext: Token refreshed and user data updated');
                return;
              }
            } catch (refreshError: any) {
              console.warn('⚠️ AuthContext: Token refresh failed:', refreshError.response?.status || refreshError.message);

              // If refresh fails, try to get user profile to verify session
              try {
                console.log('🔄 AuthContext: Trying profile endpoint as fallback...');
                const profileResponse = await axios.get(`${API_URL}/users/profile`, {
                  withCredentials: true,
                  timeout: 8000
                });

                if (profileResponse.data) {
                  console.log('✅ AuthContext: Profile verification successful');
                  const profileData = profileResponse.data.data || profileResponse.data;
                  const updatedUser = { ...userData, ...profileData };
                  setUser(updatedUser);
                  localStorage.setItem('user', JSON.stringify(updatedUser));
                  sessionStorage.setItem('user', JSON.stringify(updatedUser));
                  return;
                }
              } catch (profileError: any) {
                console.warn('⚠️ AuthContext: Profile verification failed:', profileError.response?.status || profileError.message);

                // Only clear user data if it's a definitive auth failure (401/403)
                if (refreshError.response?.status === 401 || refreshError.response?.status === 403 ||
                    profileError.response?.status === 401 || profileError.response?.status === 403) {
                  console.log('❌ AuthContext: Authentication failed, clearing user data');
                  setUser(null);
                  localStorage.removeItem('user');
                  localStorage.removeItem('adminToken');
                  sessionStorage.removeItem('user');
                  sessionStorage.removeItem('loginTime');
                } else {
                  // Network error or other issue - keep user data
                  console.log('🔄 AuthContext: Network/server error, keeping user data for now');
                }
              }
            }
          };

          // Run verification in background
          verifyToken();
        } else {
          // Invalid user data structure
          localStorage.removeItem('user');
          console.log('❌ AuthContext: Invalid user data structure, cleared localStorage');
        }
      } catch (error) {
        console.error('❌ AuthContext: Error parsing user data from localStorage:', error);
        localStorage.removeItem('user');
      }
    } else {
      console.log('📭 AuthContext: No stored user data found');
    }

    console.log('🏁 AuthContext: Initialization complete, setting loading to false');
    setLoading(false);
  }, [API_URL]);

  // Set up axios interceptors for cookie-based authentication and token refresh
  useEffect(() => {
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        // Ensure withCredentials is set for all requests
        config.withCredentials = true;

        // No need to set Authorization header with cookie-based auth
        // The cookie will be sent automatically

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // If we get a 401 and haven't already tried to refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Try to refresh the token
            const refreshResponse = await axios.post(`${API_URL}/users/refresh-token`, {}, {
              withCredentials: true
            });

            if (refreshResponse.data.status === 'success') {
              // Token refreshed successfully, retry the original request
              return axios(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, user needs to log in again
            console.log('Token refresh failed, redirecting to login');
            setUser(null);
            localStorage.removeItem('user');
            localStorage.removeItem('adminToken');

            // Only redirect if not already on login page
            if (window.location.pathname !== '/login' && window.location.pathname !== '/admin/login') {
              window.location.href = '/login';
            }
          }
        }

        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [API_URL]);

  // Login user - optimized with useCallback
  // Support both normal login and impersonation login
  const login = useCallback(async (email: string, password: string = '', userData?: User): Promise<User> => {
    // If userData is provided, this is an impersonation login
    if (userData) {
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      sessionStorage.setItem('user', JSON.stringify(userData));
      sessionStorage.setItem('loginTime', Date.now().toString());

      // Store impersonation info if available
      if (userData.impersonatedBy) {
        localStorage.setItem('impersonationAdminInfo', JSON.stringify({
          adminId: userData.impersonatedBy.adminId,
          adminEmail: userData.impersonatedBy.adminEmail,
          adminName: userData.impersonatedBy.adminName
        }));
      }

      return userData;
    }
    try {
      setLoading(true);
      setError(null);

      // Clear any existing user data to prevent auto-login issues
      localStorage.removeItem('user');
      localStorage.removeItem('adminToken');

      // Call the real API with multiple fallback approaches
      console.log('🚀 Attempting login to:', `${API_URL}/users/login`);
      console.log('🔧 Full API URL:', `${window.location.origin}${API_URL}/users/login`);

      let response;
      let lastError;

      // Approach 1: Try with configured API_URL (should work with proxy)
      try {
        console.log('📤 Attempt 1: Using configured API_URL...');
        response = await axios.post(`${API_URL}/users/login`, {
          email,
          password,
        }, {
          withCredentials: true,
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }
        });
        console.log('✅ Approach 1 successful!');
      } catch (error1) {
        console.warn('⚠️ Approach 1 failed:', error1);
        lastError = error1;

        // Approach 2: Try with direct fetch to proxy
        try {
          console.log('📤 Attempt 2: Using direct fetch to proxy...');
          const fetchResponse = await fetch('/api/users/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({ email, password }),
          });

          if (!fetchResponse.ok) {
            throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
          }

          const data = await fetchResponse.json();
          // Convert fetch response to axios-like format
          response = {
            data: data,
            status: fetchResponse.status,
            statusText: fetchResponse.statusText,
            headers: fetchResponse.headers,
          };
          console.log('✅ Approach 2 successful!');
        } catch (error2) {
          console.warn('⚠️ Approach 2 failed:', error2);
          lastError = error2;

          // Approach 3: Try direct connection to backend
          try {
            console.log('📤 Attempt 3: Direct backend connection...');
            response = await axios.post('http://localhost:5002/api/users/login', {
              email,
              password,
            }, {
              withCredentials: true,
              timeout: 10000,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              }
            });
            console.log('✅ Approach 3 successful!');
          } catch (error3) {
            console.error('❌ All approaches failed!');
            console.error('Error 1 (API_URL):', error1);
            console.error('Error 2 (Fetch):', error2);
            console.error('Error 3 (Direct):', error3);
            throw lastError; // Throw the last error
          }
        }
      }

      const userData = response.data.data || response.data;

      // User data from API response
      const userDataWithToken = {
        ...userData
      };

      setUser(userDataWithToken);

      // Store user data in both localStorage and sessionStorage for better persistence
      localStorage.setItem('user', JSON.stringify(userDataWithToken));
      sessionStorage.setItem('user', JSON.stringify(userDataWithToken));
      sessionStorage.setItem('loginTime', Date.now().toString());

      console.log(t('success.loginSuccess'));

      // Set axios to include credentials in all requests
      axios.defaults.withCredentials = true;

      // Connect to WebSocket - TEMPORARILY DISABLED FOR DEBUGGING
      try {
        console.log(t('info.websocketSkipped'));
        // socketService.connect().catch(error => {
        //   console.error('Failed to establish WebSocket connection:', error);
        // });
        // console.log(t('info.websocketConnected'));
      } catch (wsError) {
        console.error('Failed to establish WebSocket connection:', wsError);
        // Continue anyway, WebSocket is not critical for basic functionality
      }

      return userDataWithToken;
    } catch (err: any) {
      console.error("Login error:", err);
      console.error("Error details:", {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        code: err.code
      });

      // Handle different types of errors with comprehensive error mapping
      let errorMessage = t('errors.loginFailed');
      let errorType = 'general';

      // Network errors
      if (err.code === 'NETWORK_ERROR' || err.message?.includes('Network Error') || err.code === 'ERR_NETWORK') {
        errorMessage = t('errors.networkError');
        errorType = 'network';
      }
      // Connection timeout
      else if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        errorMessage = t('errors.timeoutError', 'Connection timeout. Please try again.');
        errorType = 'timeout';
      }
      // Server errors
      else if (err.response?.status >= 500) {
        errorMessage = t('errors.serverError');
        errorType = 'server';
      }
      // Authentication errors
      else if (err.response?.status === 401) {
        errorMessage = t('errors.invalidCredentials');
        errorType = 'auth';
      }
      // Forbidden access
      else if (err.response?.status === 403) {
        errorMessage = t('errors.accessDenied', 'Access denied. Please check your permissions.');
        errorType = 'forbidden';
      }
      // Account locked/suspended
      else if (err.response?.status === 423) {
        errorMessage = t('errors.accountLocked', 'Account is temporarily locked. Please try again later.');
        errorType = 'locked';
      }
      // Too many requests
      else if (err.response?.status === 429) {
        errorMessage = t('errors.tooManyRequests', 'Too many login attempts. Please wait before trying again.');
        errorType = 'rateLimit';
      }
      // Backend validation errors
      else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
        errorType = 'validation';
      }
      // Generic error with message
      else if (err.message && !err.message.includes('Network Error')) {
        errorMessage = err.message;
        errorType = 'generic';
      }

      console.error(`Login error [${errorType}]:`, errorMessage);
      setError(errorMessage);

      // Create enhanced error object with type information
      const enhancedError = new Error(errorMessage);
      (enhancedError as any).type = errorType;
      (enhancedError as any).status = err.response?.status;
      throw enhancedError;
    } finally {
      setLoading(false);
    }
  }, [API_URL]);



  // Register user - optimized with useCallback
  const register = useCallback(async (userData: RegisterData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🚀 REGISTRATION: Starting registration process...');
      console.log('📧 Registration data:', {
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        country: userData.country,
        city: userData.city,
        hasPassword: !!userData.password,
        passwordLength: userData.password?.length,
        hasReferralCode: !!userData.referralCode
      });
      console.log('🔗 API URL:', API_URL);

      let response;
      let lastError;

      // Approach 1: Try with configured API_URL (should work with proxy)
      try {
        console.log('📤 REGISTRATION Attempt 1: Using configured API_URL...');
        response = await axios.post(`${API_URL}/users/register`, userData, {
          withCredentials: true,
          timeout: 15000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }
        });
        console.log('✅ REGISTRATION Approach 1 successful!');
      } catch (error1) {
        console.warn('⚠️ REGISTRATION Approach 1 failed:', error1);
        lastError = error1;

        // Approach 2: Try with direct fetch to proxy
        try {
          console.log('📤 REGISTRATION Attempt 2: Using direct fetch to proxy...');
          const fetchResponse = await fetch('/api/users/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(userData),
          });

          if (!fetchResponse.ok) {
            const errorData = await fetchResponse.json();
            throw new Error(errorData.message || `HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
          }

          const data = await fetchResponse.json();
          // Convert fetch response to axios-like format
          response = {
            data: data,
            status: fetchResponse.status,
            statusText: fetchResponse.statusText,
            headers: fetchResponse.headers,
          };
          console.log('✅ REGISTRATION Approach 2 successful!');
        } catch (error2) {
          console.warn('⚠️ REGISTRATION Approach 2 failed:', error2);
          lastError = error2;

          // Approach 3: Try direct connection to backend (using correct port)
          try {
            console.log('📤 REGISTRATION Attempt 3: Direct backend connection...');
            response = await axios.post('http://localhost:5000/api/users/register', userData, {
              withCredentials: true,
              timeout: 15000,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              }
            });
            console.log('✅ REGISTRATION Approach 3 successful!');
          } catch (error3) {
            console.error('❌ All REGISTRATION approaches failed!');
            console.error('Error 1 (API_URL):', error1);
            console.error('Error 2 (Fetch):', error2);
            console.error('Error 3 (Direct):', error3);
            throw lastError; // Throw the last error
          }
        }
      }

      const newUser = response.data.data || response.data;
      console.log('📥 Registration response data:', newUser);

      // User data from API response
      const newUserWithToken = {
        ...newUser
      };

      setUser(newUserWithToken);

      // Store user data in localStorage
      localStorage.setItem('user', JSON.stringify(newUserWithToken));
      console.log('✅ User data stored in localStorage');

      console.log(t('success.registrationSuccess'));

      // Set axios to include credentials in all requests
      axios.defaults.withCredentials = true;
    } catch (err: any) {
      console.error("❌ Registration error:", err);

      // Extract detailed error message from backend response
      const errorResponse = err.response?.data;
      let errorMessage = t('errors.registrationFailed');

      if (errorResponse) {
        if (errorResponse.message) {
          errorMessage = errorResponse.message;
        }

        // If there are field-specific validation errors
        if (errorResponse.errors && typeof errorResponse.errors === 'object') {
          const fieldErrors = Object.values(errorResponse.errors).filter(Boolean);
          if (fieldErrors.length > 0) {
            errorMessage = fieldErrors.join('. ');
          }
        }
      }

      console.error('❌ Final registration error message:', errorMessage);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  // Logout user - optimized with useCallback
  const logout = useCallback(async () => {
    try {
      // Call the logout API endpoint to clear cookies
      await axios.post(`${API_URL}/users/logout`, {}, {
        withCredentials: true // Important for cookies
      });
      console.log('Logout API called successfully');
    } catch (error) {
      console.error('Error during logout API call:', error);
    } finally {
      // Clear local state regardless of API success
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('adminUser');
      localStorage.removeItem('adminToken');
      localStorage.removeItem('admin_welcome_shown'); // Remove welcome toast flag
      localStorage.removeItem('impersonationAdminInfo'); // Remove impersonation data

      // Also clear sessionStorage
      sessionStorage.removeItem('user');
      sessionStorage.removeItem('loginTime');

      // Remove Authorization header from axios defaults
      delete axios.defaults.headers.common['Authorization'];

      // Close WebSocket connection
      try {
        socketService.disconnect();
        console.log(t('info.websocketClosed'));
      } catch (wsError) {
        console.error('Error closing WebSocket connection:', wsError);
      }

      console.log(t('success.logoutSuccess'));
    }
  }, [API_URL]);

  // Update user profile - optimized with useCallback
  const updateProfile = useCallback(async (userData: Partial<User>) => {
    try {
      setLoading(true);
      setError(null);

      // Call the real API
      const response = await axios.put(`${API_URL}/users/profile`, userData, {
        withCredentials: true // Important for cookies
      });

      const updatedUser = response.data.data || response.data;

      // User data from API response
      const updatedUserWithToken = {
        ...updatedUser
      };

      setUser(updatedUserWithToken);
      localStorage.setItem('user', JSON.stringify(updatedUserWithToken));
    } catch (err: any) {
      console.error("Profile update error:", err);
      setError(err.response?.data?.message || t('errors.profileUpdateFailed'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, [API_URL, user?.token]);


  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Refresh user function
  const refreshUser = useCallback(async () => {
    try {
      if (!user) return;

      const response = await axios.get(`${API_URL}/users/profile`, {
        withCredentials: true
      });

      const userData = response.data;
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  }, [user, API_URL]);

  // Optimize the context value with useMemo
  const contextValue = useMemo(() => ({
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    clearError,
    refreshUser,
  }), [user, loading, error, login, register, logout, updateProfile, clearError, refreshUser]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
