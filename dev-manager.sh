#!/bin/bash

# CryptoYield Development Environment Manager
# Quản lý môi trường phát triển Full-Stack

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${PURPLE}[DEV]${NC} $1"; }

COMPOSE_FILE="docker-compose.dev-complete.yml"

show_help() {
    echo "🚀 CryptoYield Development Environment Manager"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "📋 Main Commands:"
    echo "  setup       Thiết lập môi trường phát triển hoàn chỉnh"
    echo "  start       Khởi động tất cả services"
    echo "  stop        Dừng tất cả services"
    echo "  restart     Khởi động lại tất cả services"
    echo "  status      Hiển thị trạng thái containers"
    echo "  clean       Dọn dẹp containers và volumes"
    echo ""
    echo "🔧 Service Management:"
    echo "  start-db    Chỉ khởi động database services (MongoDB + Redis)"
    echo "  start-be    Chỉ khởi động backend service"
    echo "  start-fe    Chỉ khởi động frontend service"
    echo "  restart-be  Khởi động lại backend"
    echo "  restart-fe  Khởi động lại frontend"
    echo ""
    echo "📊 Logs & Monitoring:"
    echo "  logs        Hiển thị logs của tất cả services"
    echo "  logs-fe     Hiển thị logs của frontend"
    echo "  logs-be     Hiển thị logs của backend"
    echo "  logs-db     Hiển thị logs của database services"
    echo ""
    echo "🛠️ Development Tools:"
    echo "  shell-fe    Mở shell trong frontend container"
    echo "  shell-be    Mở shell trong backend container"
    echo "  mongo       Mở MongoDB shell"
    echo "  redis       Mở Redis CLI"
    echo ""
    echo "🧪 Testing & Health:"
    echo "  health      Kiểm tra health của tất cả services"
    echo "  test-tx     Test MongoDB transaction capability"
    echo "  urls        Hiển thị tất cả URLs để truy cập"
    echo ""
    echo "Examples:"
    echo "  $0 setup     # Thiết lập môi trường lần đầu"
    echo "  $0 start     # Khởi động tất cả services"
    echo "  $0 logs-fe   # Xem logs frontend"
    echo "  $0 health    # Kiểm tra health"
    echo ""
}

case "$1" in
    "setup")
        print_header "Thiết lập môi trường phát triển hoàn chỉnh..."
        ./setup-fullstack-dev.sh
        ;;
    
    "start")
        print_header "Khởi động tất cả services..."
        docker-compose -f $COMPOSE_FILE up -d
        print_success "Tất cả services đã được khởi động"
        print_status "Sử dụng '$0 status' để kiểm tra trạng thái"
        ;;
    
    "stop")
        print_header "Dừng tất cả services..."
        docker-compose -f $COMPOSE_FILE down
        print_success "Tất cả services đã được dừng"
        ;;
    
    "restart")
        print_header "Khởi động lại tất cả services..."
        docker-compose -f $COMPOSE_FILE restart
        print_success "Tất cả services đã được khởi động lại"
        ;;
    
    "status")
        print_header "Trạng thái containers:"
        docker-compose -f $COMPOSE_FILE ps
        ;;
    
    "clean")
        print_header "Dọn dẹp containers và volumes..."
        print_warning "Điều này sẽ xóa tất cả data. Bạn có chắc chắn? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            docker-compose -f $COMPOSE_FILE down -v
            docker system prune -f
            print_success "Dọn dẹp hoàn tất"
        else
            print_status "Hủy bỏ dọn dẹp"
        fi
        ;;
    
    "start-db")
        print_header "Khởi động database services..."
        docker-compose -f $COMPOSE_FILE up -d mongodb redis mongo-express
        print_success "Database services đã được khởi động"
        ;;
    
    "start-be")
        print_header "Khởi động backend service..."
        docker-compose -f $COMPOSE_FILE up -d backend
        print_success "Backend service đã được khởi động"
        ;;
    
    "start-fe")
        print_header "Khởi động frontend service..."
        docker-compose -f $COMPOSE_FILE up -d frontend
        print_success "Frontend service đã được khởi động"
        ;;
    
    "restart-be")
        print_header "Khởi động lại backend..."
        docker-compose -f $COMPOSE_FILE restart backend
        print_success "Backend đã được khởi động lại"
        ;;
    
    "restart-fe")
        print_header "Khởi động lại frontend..."
        docker-compose -f $COMPOSE_FILE restart frontend
        print_success "Frontend đã được khởi động lại"
        ;;
    
    "logs")
        print_header "Hiển thị logs của tất cả services..."
        docker-compose -f $COMPOSE_FILE logs -f
        ;;
    
    "logs-fe")
        print_header "Hiển thị logs của frontend..."
        docker-compose -f $COMPOSE_FILE logs -f frontend
        ;;
    
    "logs-be")
        print_header "Hiển thị logs của backend..."
        docker-compose -f $COMPOSE_FILE logs -f backend
        ;;
    
    "logs-db")
        print_header "Hiển thị logs của database services..."
        docker-compose -f $COMPOSE_FILE logs -f mongodb redis mongo-express
        ;;
    
    "shell-fe")
        print_header "Mở shell trong frontend container..."
        docker exec -it cryptoyield-frontend /bin/bash
        ;;
    
    "shell-be")
        print_header "Mở shell trong backend container..."
        docker exec -it cryptoyield-backend /bin/bash
        ;;
    
    "mongo")
        print_header "Mở MongoDB shell..."
        docker exec -it cryptoyield-mongodb mongosh
        ;;
    
    "redis")
        print_header "Mở Redis CLI..."
        docker exec -it cryptoyield-redis redis-cli
        ;;
    
    "health")
        print_header "Kiểm tra health của tất cả services..."
        echo ""
        echo "🔍 Frontend Health:"
        curl -s -o /dev/null -w "HTTP %{http_code}\n" http://localhost:3003 || echo "❌ Frontend không truy cập được"
        echo ""
        echo "🔍 Backend Health:"
        curl -s -o /dev/null -w "HTTP %{http_code}\n" http://localhost:5001 || echo "❌ Backend không truy cập được"
        echo ""
        echo "🔍 MongoDB Health:"
        docker exec cryptoyield-mongodb mongosh --eval "db.adminCommand('ping')" 2>/dev/null || echo "❌ MongoDB không truy cập được"
        echo ""
        echo "🔍 Redis Health:"
        docker exec cryptoyield-redis redis-cli ping 2>/dev/null || echo "❌ Redis không truy cập được"
        echo ""
        echo "🔍 Mongo Express Health:"
        curl -s -o /dev/null -w "HTTP %{http_code}\n" http://localhost:8081 || echo "❌ Mongo Express không truy cập được"
        ;;
    
    "test-tx")
        print_header "Test MongoDB transaction capability..."
        docker exec cryptoyield-mongodb mongosh --eval "
        const session = db.getMongo().startSession();
        session.startTransaction();
        session.getDatabase('cryptoyield_test').transactionTest.insertOne({
            test: 'dev_manager_test',
            timestamp: new Date(),
            success: true
        });
        session.commitTransaction();
        session.endSession();
        print('✅ Transaction test thành công!');
        " 2>/dev/null || print_error "Transaction test thất bại"
        ;;
    
    "urls")
        print_header "URLs để truy cập services:"
        echo ""
        echo "🌐 Frontend (React App):     http://localhost:3003"
        echo "🖥️  Backend API:             http://localhost:5001"
        echo "📊 API Documentation:       http://localhost:5001/api-docs (nếu có)"
        echo "🗄️ Mongo Express:           http://localhost:8081 (admin/admin123)"
        echo "🔴 Redis (CLI only):        localhost:6379"
        echo "🗄️ MongoDB (CLI only):      localhost:27017"
        echo ""
        echo "📝 API Base URL cho Frontend: http://localhost:5001/api"
        echo "🔌 Socket.IO URL:            http://localhost:5001"
        echo ""
        ;;
    
    "")
        show_help
        ;;
    
    *)
        print_warning "Lệnh không hợp lệ: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
