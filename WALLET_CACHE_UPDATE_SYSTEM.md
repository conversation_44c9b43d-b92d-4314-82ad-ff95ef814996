# Wallet Cache Update System

## 📋 OVERVIEW

This document describes the implementation of automatic cache clearing for wallet data when admin approves or rejects deposits/withdrawals, ensuring users see updated wallet information immediately.

## 🎯 SYSTEM OBJECTIVES

### **Cache Invalidation Triggers**
- **Deposit Approval:** Clear wallet cache when admin approves a deposit
- **Withdrawal Rejection:** Clear wallet cache when admin rejects a withdrawal (refund)
- **Referral Commission:** Clear cache for both user and referrer when commission is awarded
- **Transaction Updates:** Clear relevant cache when transaction status changes

### **Cache Types Cleared**
- **Wallet Info Cache:** User's wallet balance and asset information
- **Transaction Cache:** User's transaction history
- **Referral Cache:** Referral statistics and commission data
- **Legacy Cache:** Support for old cache key formats

## 🏗️ IMPLEMENTATION ARCHITECTURE

### **1. Cache Utility Functions**

#### **Cache Utils (`cacheUtils.ts`)**
```typescript
// Clear all wallet-related cache for a user
export const clearWalletCache = (userId: string, reason: string): void

// Clear transaction-related cache for a user  
export const clearTransactionCache = (userId: string, reason: string): void

// Clear referral-related cache for a user
export const clearReferralCache = (userId: string, reason: string): void

// Clear all user-related cache (comprehensive)
export const clearUserCache = (userId: string, reason: string): void

// Clear cache for multiple users (bulk operations)
export const clearMultipleUserCache = userIds: string[], reason: string): void

// Clear admin-related cache
export const clearAdminCache = (reason: string): void

// Clear system-wide cache (use with caution)
export const clearAllCache = (reason: string): void
```

### **2. Cache Key Patterns**

#### **Wallet Cache Keys:**
```typescript
const walletCacheKeys = [
  `wallet:info:${userId}`,           // Current wallet info
  `api:wallet:info:${userId}`,       // API wallet info
  `wallet:transactions:${userId}`,   // Wallet transactions
  `api:wallet:transactions:${userId}`, // API transactions
  `wallet:balance:${userId}`,        // Legacy balance key
  `api:wallet:balance:${userId}`,    // Legacy API balance key
  `wallet:earned:${userId}`,         // Earned balance
  `api:wallet:earned:${userId}`,     // API earned balance
  `wallet:withdrawable:${userId}`,   // Withdrawable balance
  `api:wallet:withdrawable:${userId}` // API withdrawable balance
];
```

#### **Referral Cache Keys:**
```typescript
const referralCacheKeys = [
  `referral:stats:${userId}`,        // Referral statistics
  `api:referral:stats:${userId}`,    // API referral stats
  `referral:tree:${userId}`,         // Referral tree
  `api:referral:tree:${userId}`,     // API referral tree
  `referral:commissions:${userId}`,  // Commission history
  `api:referral:commissions:${userId}` // API commission history
];
```

### **3. Integration Points**

#### **Transaction Controller (`transactionController.ts`)**

##### **Deposit Approval:**
```typescript
// After wallet is updated with deposit amount
clearWalletCache(user._id.toString(), 'deposit approval');

// After referral commission is processed
if (commissionResult.success && commissionResult.referrerId) {
  clearWalletCache(commissionResult.referrerId, 'referral commission award');
}
```

##### **Withdrawal Rejection (Refund):**
```typescript
// After amount is refunded to wallet
clearWalletCache(transaction.userId.toString(), 'withdrawal refund');
```

#### **First Deposit Commission Service (`firstDepositCommissionService.ts`)**

##### **After Commission Processing:**
```typescript
// Clear cache for user (first deposit status changed)
clearReferralCache(userId.toString(), 'first deposit commission processed');

// Clear cache for referrer (wallet and referral stats updated)
clearWalletCache(referrer._id.toString(), 'referral commission received');
clearReferralCache(referrer._id.toString(), 'referral commission received');
```

## 🔄 CACHE CLEARING FLOW

### **1. Deposit Approval Process**
```
1. Admin approves deposit
2. User wallet balance updated
3. Commission calculated and added
4. Clear user's wallet cache
5. Process referral commission (if applicable)
6. Clear referrer's wallet cache
7. Clear referrer's referral cache
8. User sees updated balance immediately
```

### **2. Withdrawal Rejection Process**
```
1. Admin rejects withdrawal
2. Amount refunded to user wallet
3. Clear user's wallet cache
4. User sees refunded amount immediately
```

### **3. Referral Commission Process**
```
1. First deposit approved
2. Referral commission calculated
3. Referrer's wallet updated
4. Commission transaction created
5. Clear referrer's wallet cache
6. Clear referrer's referral cache
7. Clear user's referral cache (first deposit status)
8. Both users see updated data immediately
```

## 📊 CACHE CLEARING DETAILS

### **Cache Keys Cleared by Function:**

#### **clearWalletCache():**
- ✅ `wallet:info:${userId}` - Main wallet information
- ✅ `api:wallet:info:${userId}` - API wallet endpoint cache
- ✅ `wallet:transactions:${userId}` - Transaction history
- ✅ `api:wallet:transactions:${userId}` - API transaction cache
- ✅ `wallet:balance:${userId}` - Legacy balance cache
- ✅ `api:wallet:balance:${userId}` - Legacy API balance cache
- ✅ `wallet:earned:${userId}` - Earned balance cache
- ✅ `wallet:withdrawable:${userId}` - Withdrawable balance cache
- ✅ **Prefix clearing:** `wallet:${userId}*` and `api:wallet:${userId}*`

#### **clearReferralCache():**
- ✅ `referral:stats:${userId}` - Referral statistics
- ✅ `api:referral:stats:${userId}` - API referral stats
- ✅ `referral:tree:${userId}` - Referral tree data
- ✅ `referral:commissions:${userId}` - Commission history
- ✅ **Prefix clearing:** `referral:${userId}*`

### **Logging and Monitoring:**

#### **Cache Clearing Logs:**
```typescript
logger.info(`Cleared wallet cache for user ${userId}`, {
  reason: 'deposit approval',
  specificKeysDeleted: 8,
  prefixKeysDeleted: 12,
  totalDeleted: 20
});
```

#### **Error Handling:**
```typescript
// Cache clearing errors don't fail the main operation
try {
  clearWalletCache(userId, reason);
} catch (cacheError) {
  logger.error(`Error clearing wallet cache for user ${userId}:`, {
    error: cacheError.message,
    reason
  });
}
```

## 🛡️ SAFETY FEATURES

### **Non-Blocking Operations:**
- Cache clearing failures don't affect main business logic
- Comprehensive error handling and logging
- Graceful degradation if cache service is unavailable

### **Comprehensive Coverage:**
- Multiple cache key patterns for backward compatibility
- Prefix-based clearing for comprehensive cleanup
- Support for both current and legacy cache formats

### **Performance Optimization:**
- Batch cache key deletion
- Efficient prefix-based invalidation
- Minimal performance impact on main operations

## 📈 BENEFITS

### **User Experience:**
- **Immediate Updates:** Users see wallet changes instantly after admin actions
- **Real-time Data:** No need to refresh or wait for cache expiration
- **Consistent State:** Wallet data always reflects latest transactions

### **System Performance:**
- **Targeted Clearing:** Only relevant cache is cleared, not entire cache
- **Efficient Operations:** Batch operations for multiple cache keys
- **Minimal Overhead:** Cache clearing is fast and non-blocking

### **Maintenance:**
- **Centralized Logic:** All cache clearing logic in utility functions
- **Easy Debugging:** Comprehensive logging for troubleshooting
- **Extensible:** Easy to add new cache types or patterns

## 🔧 CONFIGURATION

### **Cache Service Integration:**
```typescript
import { cacheService } from '../services/cacheService';
import { clearWalletCache } from '../utils/cacheUtils';

// Usage in controllers
clearWalletCache(userId, 'deposit approval');
```

### **Environment Considerations:**
- **Development:** More verbose logging for debugging
- **Production:** Optimized performance with essential logging
- **Testing:** Mock cache service for unit tests

## 🧪 TESTING

### **Test Scenarios:**
- ✅ **Deposit Approval:** Verify wallet cache is cleared
- ✅ **Withdrawal Rejection:** Verify refund cache clearing
- ✅ **Referral Commission:** Verify both user and referrer cache clearing
- ✅ **Error Handling:** Verify graceful handling of cache failures
- ✅ **Performance:** Verify minimal impact on transaction processing

### **Cache Verification:**
```typescript
// Before admin action
const cachedData = cacheService.get(`wallet:info:${userId}`);
expect(cachedData).toBeDefined();

// After admin action
const updatedCache = cacheService.get(`wallet:info:${userId}`);
expect(updatedCache).toBeUndefined(); // Cache should be cleared
```

## 🚀 DEPLOYMENT

### **Files Updated:**
- ✅ `backend/src/utils/cacheUtils.ts` - New cache utility functions
- ✅ `backend/src/controllers/transactionController.ts` - Integrated cache clearing
- ✅ `backend/src/services/firstDepositCommissionService.ts` - Added cache clearing

### **Backward Compatibility:**
- ✅ **Legacy Cache Keys:** Support for old cache key formats
- ✅ **Gradual Migration:** New cache clearing works alongside existing cache
- ✅ **No Breaking Changes:** Existing cache functionality preserved

### **Monitoring:**
- ✅ **Cache Hit Rates:** Monitor cache effectiveness after clearing
- ✅ **Performance Metrics:** Track cache clearing performance impact
- ✅ **Error Rates:** Monitor cache clearing failures

## 📝 USAGE EXAMPLES

### **Basic Wallet Cache Clearing:**
```typescript
// Clear wallet cache after balance update
clearWalletCache(userId, 'balance update');
```

### **Comprehensive User Cache Clearing:**
```typescript
// Clear all user-related cache
clearUserCache(userId, 'profile update');
```

### **Bulk Cache Clearing:**
```typescript
// Clear cache for multiple users
clearMultipleUserCache([userId1, userId2, userId3], 'bulk operation');
```

### **Admin Cache Clearing:**
```typescript
// Clear admin dashboard cache
clearAdminCache('admin data update');
```

---

## 📞 SUPPORT

### **Cache Issues:**
- Check cache service logs for clearing operations
- Verify cache keys are being generated correctly
- Monitor cache hit/miss rates after clearing

### **Performance Issues:**
- Review cache clearing frequency
- Optimize cache key patterns if needed
- Consider cache warming strategies for frequently accessed data

**System Status:** ✅ Active and Operational
**Last Updated:** 2025-01-31
**Version:** 1.0.0
