import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Badge,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  useToast,
  Spinner,
  Flex,
  Icon,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  ButtonGroup
} from '@chakra-ui/react';
import {
  FaSearch,
  FaEye,
  FaCheck,
  FaTimes,
  FaEllipsisV,
  FaSync
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';
import adminWithdrawalService, { AdminWithdrawal } from '../../services/adminWithdrawalService';
import WithdrawalDetailModal from '../../components/admin/WithdrawalDetailModal';



interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const WithdrawalManagement: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();

  // State management
  const [withdrawals, setWithdrawals] = useState<AdminWithdrawal[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Modal state
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<AdminWithdrawal | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    cryptocurrency: '',
    withdrawalType: '',
    search: ''
  });

  // Load withdrawals on component mount and filter changes
  useEffect(() => {
    loadWithdrawals();
  }, [pagination.page, filters]);

  const loadWithdrawals = async () => {
    try {
      setLoading(true);
      const response = await adminWithdrawalService.getWithdrawals({
        page: pagination.page,
        limit: pagination.limit,
        status: filters.status || undefined,
        cryptocurrency: filters.cryptocurrency || undefined,
        withdrawalType: filters.withdrawalType || undefined,
        search: filters.search || undefined
      });

      setWithdrawals(response.data.withdrawals);
      setPagination(response.data.pagination);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      // Fallback to empty array if API fails
      setWithdrawals([]);
    } finally {
      setLoading(false);
    }
  };



  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handleStatusUpdate = async (withdrawalId: string, status: string) => {
    try {
      const response = await adminWithdrawalService.updateWithdrawalStatus(withdrawalId, {
        status,
        adminNotes: `Status updated to ${status} by admin`
      });

      toast({
        title: 'Success',
        description: response.message,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      loadWithdrawals(); // Refresh the list
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleViewDetails = (withdrawal: AdminWithdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setIsDetailModalOpen(true);
  };

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedWithdrawal(null);
  };

  const handleModalStatusUpdate = () => {
    loadWithdrawals(); // Refresh the list after status update
  };

  return (
    <Box bg="#0B0E11" minH="100vh" p={6}>
      <Container maxW="7xl">
        <VStack spacing={6} align="stretch">
          {/* Header */}
          <Box>
            <Heading color="white" size="lg" mb={2}>
              Withdrawal Management
            </Heading>
            <Text color="gray.400">
              Manage and process user withdrawal requests
            </Text>
          </Box>

          {/* Filters */}
          <Box bg="gray.800" p={4} borderRadius="md" borderWidth="1px" borderColor="gray.600">
            <HStack spacing={4} w="full" flexWrap="wrap">
              <InputGroup maxW="300px">
                <InputLeftElement>
                  <Icon as={FaSearch} color="gray.400" />
                </InputLeftElement>
                <Input
                  placeholder="Search by user, email, or address..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  bg="gray.700"
                  borderColor="gray.600"
                  color="white"
                />
              </InputGroup>

              <Select
                placeholder="All Statuses"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                maxW="150px"
                bg="gray.700"
                borderColor="gray.600"
                color="white"
              >
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
              </Select>

              <Select
                placeholder="All Cryptocurrencies"
                value={filters.cryptocurrency}
                onChange={(e) => handleFilterChange('cryptocurrency', e.target.value)}
                maxW="150px"
                bg="gray.700"
                borderColor="gray.600"
                color="white"
              >
                <option value="BTC">Bitcoin</option>
                <option value="ETH">Ethereum</option>
                <option value="USDT">Tether</option>
                <option value="BNB">Binance Coin</option>
                <option value="SOL">Solana</option>
                <option value="DOGE">Dogecoin</option>
                <option value="TRX">Tron</option>
              </Select>

              <Button
                leftIcon={<FaSync />}
                onClick={loadWithdrawals}
                colorScheme="blue"
                variant="outline"
              >
                Refresh
              </Button>
            </HStack>
          </Box>

          {/* Withdrawals Table */}
          <Box bg="gray.800" borderRadius="md" borderWidth="1px" borderColor="gray.600" overflow="hidden">
            {loading ? (
              <Flex justify="center" align="center" h="200px">
                <Spinner size="lg" color="blue.500" />
              </Flex>
            ) : withdrawals.length === 0 ? (
              <Flex justify="center" align="center" h="200px">
                <Text color="gray.400">No withdrawals found</Text>
              </Flex>
            ) : (
              <Table variant="simple">
                <Thead bg="gray.700">
                  <Tr>
                    <Th color="gray.300">User</Th>
                    <Th color="gray.300">Crypto</Th>
                    <Th color="gray.300">Type</Th>
                    <Th color="gray.300">Amount</Th>
                    <Th color="gray.300">USD Value</Th>
                    <Th color="gray.300">Status</Th>
                    <Th color="gray.300">Date</Th>
                    <Th color="gray.300">Actions</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {withdrawals.map((withdrawal) => (
                    <Tr key={withdrawal.id} _hover={{ bg: "gray.700" }}>
                      <Td>
                        <VStack align="start" spacing={0}>
                          <Text color="white" fontWeight="medium">
                            {withdrawal.user?.name || 'Unknown User'}
                          </Text>
                          <Text color="gray.400" fontSize="sm">
                            {withdrawal.user?.email || 'No email'}
                          </Text>
                          {withdrawal.user?.phoneNumber && (
                            <Text color="gray.500" fontSize="xs">
                              {withdrawal.user.phoneNumber}
                            </Text>
                          )}
                          {withdrawal.user?.country && (
                            <Text color="gray.500" fontSize="xs">
                              {withdrawal.user.country}
                            </Text>
                          )}
                        </VStack>
                      </Td>
                      <Td>
                        <HStack>
                          <Icon
                            as={getCryptoIcon(withdrawal.cryptocurrency)}
                            color={getCryptoColor(withdrawal.cryptocurrency)}
                            boxSize={5}
                          />
                          <Text color="white">{withdrawal.cryptocurrency}</Text>
                        </HStack>
                      </Td>
                      <Td>
                        <Text color="white">
                          {adminWithdrawalService.formatWithdrawalType(withdrawal.withdrawalType)}
                        </Text>
                      </Td>
                      <Td>
                        <VStack align="start" spacing={0}>
                          <Text color="white">
                            {adminWithdrawalService.formatCurrency(withdrawal.amount, withdrawal.cryptocurrency)}
                          </Text>
                          <Text color="gray.400" fontSize="sm">
                            Net: {adminWithdrawalService.formatCurrency(withdrawal.netAmount, withdrawal.cryptocurrency)}
                          </Text>
                        </VStack>
                      </Td>
                      <Td>
                        <Text color="white">{adminWithdrawalService.formatUSD(withdrawal.usdValue)}</Text>
                      </Td>
                      <Td>
                        <Badge colorScheme={adminWithdrawalService.getStatusColor(withdrawal.status)}>
                          {withdrawal.status.toUpperCase()}
                        </Badge>
                      </Td>
                      <Td>
                        <Text color="white" fontSize="sm">
                          {new Date(withdrawal.createdAt).toLocaleDateString()}
                        </Text>
                      </Td>
                      <Td>
                        <Menu>
                          <MenuButton
                            as={IconButton}
                            icon={<FaEllipsisV />}
                            variant="ghost"
                            color="gray.400"
                            size="sm"
                          />
                          <MenuList bg="gray.700" borderColor="gray.600">
                            <MenuItem
                              icon={<FaEye />}
                              onClick={() => handleViewDetails(withdrawal)}
                              bg="gray.700"
                              color="white"
                              _hover={{ bg: "gray.600" }}
                            >
                              View Details
                            </MenuItem>
                            {withdrawal.status === 'pending' && (
                              <>
                                <MenuItem
                                  icon={<FaCheck />}
                                  onClick={() => handleStatusUpdate(withdrawal.id, 'approved')}
                                  bg="gray.700"
                                  color="green.400"
                                  _hover={{ bg: "gray.600" }}
                                >
                                  Approve
                                </MenuItem>
                                <MenuItem
                                  icon={<FaTimes />}
                                  onClick={() => handleStatusUpdate(withdrawal.id, 'rejected')}
                                  bg="gray.700"
                                  color="red.400"
                                  _hover={{ bg: "gray.600" }}
                                >
                                  Reject
                                </MenuItem>
                              </>
                            )}
                            {withdrawal.status === 'approved' && (
                              <MenuItem
                                icon={<FaCheck />}
                                onClick={() => handleStatusUpdate(withdrawal.id, 'completed')}
                                bg="gray.700"
                                color="blue.400"
                                _hover={{ bg: "gray.600" }}
                              >
                                Mark Completed
                              </MenuItem>
                            )}
                          </MenuList>
                        </Menu>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </Box>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <Flex justify="center" align="center" mt={6}>
              <ButtonGroup spacing={2}>
                <Button
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                  isDisabled={pagination.page === 1}
                  variant="outline"
                  borderColor="gray.600"
                  color="white"
                  _hover={{ borderColor: "#F0B90B" }}
                >
                  Previous
                </Button>

                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const pageNum = pagination.page <= 3
                    ? i + 1
                    : pagination.page >= pagination.pages - 2
                    ? pagination.pages - 4 + i
                    : pagination.page - 2 + i;

                  if (pageNum < 1 || pageNum > pagination.pages) return null;

                  return (
                    <Button
                      key={pageNum}
                      onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}
                      variant={pagination.page === pageNum ? "solid" : "outline"}
                      bg={pagination.page === pageNum ? "#F0B90B" : "transparent"}
                      color={pagination.page === pageNum ? "#0B0E11" : "white"}
                      borderColor="gray.600"
                      _hover={{ borderColor: "#F0B90B" }}
                    >
                      {pageNum}
                    </Button>
                  );
                })}

                <Button
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                  isDisabled={pagination.page === pagination.pages}
                  variant="outline"
                  borderColor="gray.600"
                  color="white"
                  _hover={{ borderColor: "#F0B90B" }}
                >
                  Next
                </Button>
              </ButtonGroup>
            </Flex>
          )}

          {/* Results Summary */}
          <Flex justify="center" mt={4}>
            <Text color="gray.400" fontSize="sm">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} withdrawals
            </Text>
          </Flex>
        </VStack>
      </Container>

      {/* Withdrawal Detail Modal */}
      <WithdrawalDetailModal
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        withdrawal={selectedWithdrawal}
        onStatusUpdate={handleModalStatusUpdate}
      />
    </Box>
  );
};

export default WithdrawalManagement;
