# 🚀 CryptoYield Development Environment

Môi trường phát triển hoàn chỉnh cho dự án CryptoYield với Frontend (React) + Backend (Node.js) + Database (MongoDB + Redis) + Admin Interface.

## 🎯 Tính năng

✅ **Frontend Development với Hot-Reload**
- React + Vite development server
- Source code được mount từ host machine
- Tự động reload khi có thay đổi code
- TypeScript support với live compilation

✅ **Backend Development với Hot-Reload**
- Node.js + TypeScript với nodemon
- Source code được mount từ host machine
- Tự động restart khi có thay đổi code
- Debug-friendly logging

✅ **Database với Transaction Support**
- MongoDB 7.0 với replica set (rs0)
- Full ACID transaction capabilities
- Redis cho caching và session management
- Mongo Express cho database administration

✅ **Networking và CORS**
- Isolated Docker network cho tất cả services
- CORS được cấu hình cho frontend-backend communication
- Service discovery giữa các containers

## 🚀 Quick Start

### 1. Thiết lập lần đầu
```bash
# Cấp quyền thực thi cho scripts
chmod +x setup-fullstack-dev.sh dev-manager.sh

# Thiết lập môi trường hoàn chỉnh
./setup-fullstack-dev.sh
```

### 2. Sử dụng Development Manager
```bash
# Xem tất cả commands có sẵn
./dev-manager.sh

# Khởi động tất cả services
./dev-manager.sh start

# Kiểm tra health
./dev-manager.sh health

# Xem logs
./dev-manager.sh logs-fe  # Frontend logs
./dev-manager.sh logs-be  # Backend logs
```

## 📋 Service Information

| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **Frontend** | http://localhost:3003 | - | React development server |
| **Backend** | http://localhost:5001 | - | API server |
| **Mongo Express** | http://localhost:8081 | admin/admin123 | Database admin |
| **MongoDB** | localhost:27017 | No auth (dev) | Database server |
| **Redis** | localhost:6379 | - | Cache & sessions |

## 🔧 Development Workflow

### Frontend Development
```bash
# Chỉnh sửa code trong ./frontend/src/
# Thay đổi sẽ được phản ánh ngay lập tức tại http://localhost:3003

# Xem logs frontend
./dev-manager.sh logs-fe

# Restart frontend nếu cần
./dev-manager.sh restart-fe

# Vào shell frontend container
./dev-manager.sh shell-fe
```

### Backend Development
```bash
# Chỉnh sửa code trong ./backend/src/
# Thay đổi sẽ được phản ánh ngay lập tức

# Xem logs backend
./dev-manager.sh logs-be

# Restart backend nếu cần
./dev-manager.sh restart-be

# Vào shell backend container
./dev-manager.sh shell-be
```

### Database Management
```bash
# Truy cập Mongo Express
# http://localhost:8081 (admin/admin123)

# Vào MongoDB shell
./dev-manager.sh mongo

# Vào Redis CLI
./dev-manager.sh redis

# Test transactions
./dev-manager.sh test-tx
```

## 🔗 API Configuration

### Frontend → Backend Communication
- **Frontend URL**: http://localhost:3003
- **Backend API**: http://localhost:5001/api
- **Socket.IO**: http://localhost:5001
- **CORS**: Đã được cấu hình cho phép frontend truy cập backend

### Environment Variables
Frontend sử dụng các biến môi trường trong `.env.development`:
```env
VITE_API_URL=http://localhost:5001/api
VITE_SOCKET_URL=http://localhost:5001
```

## 🧪 Testing & Debugging

### Health Checks
```bash
# Kiểm tra tất cả services
./dev-manager.sh health

# Kiểm tra URLs
./dev-manager.sh urls
```

### Transaction Testing
```bash
# Test MongoDB transaction capability
./dev-manager.sh test-tx
```

### Logs Monitoring
```bash
# Tất cả logs
./dev-manager.sh logs

# Logs theo service
./dev-manager.sh logs-fe   # Frontend
./dev-manager.sh logs-be   # Backend
./dev-manager.sh logs-db   # Database services
```

## 📁 File Structure

```
cryptoyield/
├── docker-compose.dev-complete.yml    # Complete development environment
├── setup-fullstack-dev.sh            # Automated setup script
├── dev-manager.sh                     # Development management tool
├── DEVELOPMENT_SETUP.md               # This documentation
├── frontend/
│   ├── Dockerfile.dev                 # Frontend development Dockerfile
│   ├── .env.development               # Frontend environment variables
│   ├── src/                          # Frontend source code (mounted)
│   └── ...
├── backend/
│   ├── Dockerfile.dev                 # Backend development Dockerfile
│   ├── src/                          # Backend source code (mounted)
│   └── ...
└── ...
```

## 🔧 Common Commands

### Service Management
```bash
./dev-manager.sh start        # Khởi động tất cả
./dev-manager.sh stop         # Dừng tất cả
./dev-manager.sh restart      # Restart tất cả
./dev-manager.sh status       # Xem trạng thái

./dev-manager.sh start-db     # Chỉ database services
./dev-manager.sh start-be     # Chỉ backend
./dev-manager.sh start-fe     # Chỉ frontend
```

### Development Tools
```bash
./dev-manager.sh shell-fe     # Frontend container shell
./dev-manager.sh shell-be     # Backend container shell
./dev-manager.sh mongo        # MongoDB shell
./dev-manager.sh redis        # Redis CLI
```

### Manual Docker Commands
```bash
# Khởi động
docker-compose -f docker-compose.dev-complete.yml up -d

# Dừng
docker-compose -f docker-compose.dev-complete.yml down

# Xem logs
docker-compose -f docker-compose.dev-complete.yml logs -f [service]

# Rebuild
docker-compose -f docker-compose.dev-complete.yml build [service]
```

## 🐛 Troubleshooting

### Port Conflicts
Nếu gặp lỗi port đã được sử dụng:
```bash
# Kiểm tra ports đang sử dụng
netstat -tulpn | grep -E "(3003|5001|27017|6379|8081)"

# Dừng services và thử lại
./dev-manager.sh stop
./dev-manager.sh start
```

### Container Issues
```bash
# Xem logs để debug
./dev-manager.sh logs

# Restart service cụ thể
./dev-manager.sh restart-fe
./dev-manager.sh restart-be

# Rebuild nếu cần
docker-compose -f docker-compose.dev-complete.yml build [service]
```

### Database Issues
```bash
# Kiểm tra MongoDB replica set
./dev-manager.sh mongo
# Trong MongoDB shell: rs.status()

# Reset database nếu cần
./dev-manager.sh clean  # ⚠️ Sẽ xóa tất cả data
./dev-manager.sh setup
```

### Hot-Reload Issues
```bash
# Restart service nếu hot-reload không hoạt động
./dev-manager.sh restart-fe  # hoặc restart-be

# Kiểm tra volume mounting
docker exec cryptoyield-frontend ls -la /app/src
docker exec cryptoyield-backend ls -la /app/src
```

## 🎯 Next Steps

1. **Bắt đầu phát triển**: Chỉnh sửa code trong `./frontend/src/` và `./backend/src/`
2. **Test API**: Sử dụng frontend để test backend APIs
3. **Database**: Sử dụng Mongo Express để quản lý database
4. **Monitor**: Sử dụng `./dev-manager.sh health` để theo dõi services

## 📞 Support

Nếu gặp vấn đề:
1. **Kiểm tra logs**: `./dev-manager.sh logs`
2. **Kiểm tra health**: `./dev-manager.sh health`
3. **Reset environment**: `./dev-manager.sh clean && ./dev-manager.sh setup`

Happy coding! 🎉
