#!/bin/bash

# Final Mongo Express Setup - Complete Solution
set -e

echo "🎯 Final Mongo Express Setup - Complete Solution"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Step 1: Stop any existing services
print_status "Stopping existing services..."
docker-compose -f docker-compose.mongo.yml down 2>/dev/null || true
docker-compose -f docker-compose.mongo-simple.yml down 2>/dev/null || true

# Step 2: Check and create keyfile if needed
print_status "Checking MongoDB keyfile..."
if [ ! -f "mongodb-keyfile/mongodb-keyfile" ]; then
    print_warning "Keyfile not found. Creating keyfile..."
    mkdir -p mongodb-keyfile
    openssl rand -base64 756 > mongodb-keyfile/mongodb-keyfile
    chmod 600 mongodb-keyfile/mongodb-keyfile
    print_success "Keyfile created successfully"
else
    print_success "Keyfile exists"
    chmod 600 mongodb-keyfile/mongodb-keyfile
fi

# Step 3: Start services with original configuration
print_status "Starting services with original configuration..."
docker-compose -f docker-compose.mongo.yml up -d

# Step 4: Wait for containers to start
print_status "Waiting for containers to start..."
sleep 30

# Step 5: Monitor MongoDB startup
print_status "Monitoring MongoDB startup..."
for i in {1..12}; do
    if docker ps | grep -q "cryptoyield-mongodb.*Up"; then
        print_success "MongoDB container is running"
        break
    elif docker ps | grep -q "cryptoyield-mongodb.*Restarting"; then
        print_warning "MongoDB is restarting... attempt $i/12"
        sleep 15
    else
        print_warning "Waiting for MongoDB... attempt $i/12"
        sleep 15
    fi
    
    if [ $i -eq 12 ]; then
        print_error "MongoDB failed to start properly"
        print_status "Checking MongoDB logs..."
        docker logs cryptoyield-mongodb --tail 10
        exit 1
    fi
done

# Step 6: Wait for MongoDB to be ready for connections
print_status "Waiting for MongoDB to accept connections..."
sleep 30

# Step 7: Initialize replica set
print_status "Initializing MongoDB replica set..."
for i in {1..5}; do
    if docker exec cryptoyield-mongodb mongosh --eval "
    try {
        const status = rs.status();
        if (status.ok === 1) {
            print('✅ Replica set already active: ' + status.set);
        }
    } catch (e) {
        if (e.message.includes('no replset config')) {
            rs.initiate({
                _id: 'rs0',
                members: [{ _id: 0, host: 'mongodb:27017' }]
            });
            print('✅ Replica set initialized successfully');
        } else {
            print('⚠️ Replica set status: ' + e.message);
        }
    }
    " 2>/dev/null; then
        print_success "MongoDB replica set is ready"
        break
    else
        print_warning "Attempt $i/5: Waiting for MongoDB to be ready..."
        sleep 15
    fi
done

# Step 8: Wait for replica set to stabilize
print_status "Waiting for replica set to stabilize..."
sleep 45

# Step 9: Test services
print_status "Testing services..."

# Test MongoDB
if docker exec cryptoyield-mongodb mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
    print_success "✅ MongoDB is accessible"
else
    print_warning "⚠️ MongoDB may still be initializing"
fi

# Test Redis
if docker exec cryptoyield-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
    print_success "✅ Redis is accessible"
else
    print_warning "⚠️ Redis connection issue"
fi

# Test Mongo Express
print_status "Testing Mongo Express connectivity..."
for i in {1..8}; do
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
    if [[ "$response" == "200" || "$response" == "401" ]]; then
        print_success "✅ Mongo Express is accessible at http://localhost:8081"
        break
    else
        print_warning "Attempt $i/8: Waiting for Mongo Express... (HTTP $response)"
        sleep 20
    fi
done

# Step 10: Final status and information
echo ""
echo "🎉 Setup completed!"
echo ""
echo "📋 Service Information:"
echo "  🌐 Mongo Express: http://localhost:8081"
echo "  🔑 Login: admin / admin123"
echo "  🗄️ MongoDB: localhost:27017 (replica set: rs0)"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🔗 MongoDB Connection String for Backend:"
echo "  **********************************************************************************************************"
echo ""

# Show container status
print_status "Final container status:"
docker-compose -f docker-compose.mongo.yml ps

# Test transaction capability
echo ""
print_status "Testing transaction capability..."
if docker exec cryptoyield-mongodb mongosh --eval "
try {
    const session = db.getMongo().startSession();
    session.startTransaction();
    
    const testDb = session.getDatabase('cryptoyield_test');
    testDb.transactionTest.insertOne({
        test: 'final_setup_test',
        timestamp: new Date(),
        success: true
    }, {session: session});
    
    session.commitTransaction();
    session.endSession();
    
    print('✅ Transaction test successful - MongoDB is ready for production!');
} catch (e) {
    print('❌ Transaction test failed: ' + e.message);
}
" 2>/dev/null; then
    print_success "✅ Transaction support verified"
else
    print_warning "⚠️ Transaction test may need more time"
fi

# Final instructions
echo ""
print_success "🎯 Your MongoDB setup is now complete!"
echo ""
echo "📝 Next steps:"
echo "  1. Access Mongo Express: http://localhost:8081 (admin/admin123)"
echo "  2. Run your backend: cd backend && npm run dev:docker"
echo "  3. Your backend will connect to MongoDB with full transaction support"
echo ""

# Check if Mongo Express is accessible
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
if [[ "$response" == "200" || "$response" == "401" ]]; then
    print_success "✅ Mongo Express is ready to use!"
else
    print_warning "⚠️ Mongo Express may need 1-2 more minutes to fully initialize"
    echo "💡 If you see connection errors, wait a moment and refresh http://localhost:8081"
fi

echo ""
print_success "🚀 Setup complete! Your CryptoYield project now has a fully functional MongoDB admin interface!"
