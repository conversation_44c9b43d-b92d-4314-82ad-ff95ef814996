import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  VStack,
  HStack,
  Icon,
  Badge,
  Button,
  Spinner,
  Center,
  Card,
  CardBody,
  Divider,
  Progress,
  useToast,
  IconButton,
  Tooltip
} from '@chakra-ui/react';
import {
  FaCoins,
  FaChartLine,
  FaCalendarAlt,
  FaClock,
  FaLock,
  FaUnlock,
  FaArrowLeft,
  FaArrowRight,
  FaEye,
  FaPercentage
} from 'react-icons/fa';
import { motion } from 'framer-motion';
import { investmentPackageService } from '../../services/api';
import { useTranslation } from 'react-i18next';

// Motion components
const MotionCard = motion(Card);
const MotionBox = motion(Box);

interface InvestmentPackage {
  _id: string;
  packageId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  accumulatedInterest: number;
  totalEarned: number;
  activeDays: number;
  totalDays: number;
  roi: number;
  createdAt: string;
  activatedAt?: string;
  interestRate: number;
  transactionId?: string;
  principalLockUntil?: string;
  principalLocked?: boolean;
}

interface HorizontalInvestmentPackagesProps {
  onViewDetails?: (packageId: string) => void;
  onCreateNew?: () => void;
}

const HorizontalInvestmentPackages: React.FC<HorizontalInvestmentPackagesProps> = ({
  onViewDetails,
  onCreateNew
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  // State management
  const [packages, setPackages] = useState<InvestmentPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";
  const successColor = "#02C076";
  const warningColor = "#F0B90B";
  const errorColor = "#F84960";

  // Fetch investment packages
  const fetchInvestmentPackages = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await investmentPackageService.getUserPackages({
        page: 1,
        limit: 50,
        status: 'active'
      });

      if (response.data && response.data.data && response.data.data.packages) {
        setPackages(response.data.data.packages);
      } else {
        setPackages([]);
      }
    } catch (error: any) {
      console.error('Error fetching investment packages:', error);
      
      if (error?.response?.status === 401) {
        setError('Authentication required. Please log in to view your investment packages.');
      } else if (error?.response?.status === 403) {
        setError('Access denied. You do not have permission to view investment packages.');
      } else {
        const errorMessage = error?.response?.data?.message || error?.message || 'Failed to load investment packages';
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchInvestmentPackages();
  }, []);

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active': return successColor;
      case 'pending': return warningColor;
      case 'completed': return primaryColor;
      case 'withdrawn': return errorColor;
      default: return secondaryTextColor;
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'active': return t('investments.status.active', 'Active');
      case 'pending': return t('investments.status.pending', 'Pending');
      case 'completed': return t('investments.status.completed', 'Completed');
      case 'withdrawn': return t('investments.status.withdrawn', 'Withdrawn');
      default: return status;
    }
  };

  const formatCurrency = (amount: number, currency: string): string => {
    const decimals = currency === 'BTC' ? 8 : currency === 'ETH' ? 6 : 4;
    return `${amount.toFixed(decimals)} ${currency}`;
  };

  const calculateDaysRemaining = (createdAt: string): number => {
    const now = new Date();
    const createdDate = new Date(createdAt);
    const lockUntilDate = new Date(createdDate.getTime() + (30 * 24 * 60 * 60 * 1000)); // 30 days from creation
    const diffTime = lockUntilDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const isPrincipalLocked = (createdAt: string): boolean => {
    return calculateDaysRemaining(createdAt) > 0;
  };

  const getProgressPercentage = (activeDays: number, totalDays: number): number => {
    return Math.min((activeDays / totalDays) * 100, 100);
  };

  // Render loading state
  if (loading) {
    return (
      <Center py={8}>
        <VStack spacing={4}>
          <Spinner size="lg" color={primaryColor} />
          <Text color={secondaryTextColor}>
            {t('investments.loading', 'Loading investment packages...')}
          </Text>
        </VStack>
      </Center>
    );
  }

  // Render error state
  if (error) {
    return (
      <Center py={8}>
        <VStack spacing={4}>
          <Icon as={FaCoins} color={errorColor} boxSize={12} />
          <Text color={errorColor} fontSize="lg" textAlign="center">
            {t('investments.error', 'Error loading investment packages')}
          </Text>
          <Text color={secondaryTextColor} fontSize="sm" textAlign="center">
            {error}
          </Text>
          <Button
            size="sm"
            bg={primaryColor}
            color={bgColor}
            _hover={{ bg: "#F8D12F" }}
            onClick={fetchInvestmentPackages}
          >
            {t('common.retry', 'Retry')}
          </Button>
        </VStack>
      </Center>
    );
  }

  // Render empty state
  if (packages.length === 0) {
    return (
      <Center py={8}>
        <VStack spacing={4}>
          <Icon as={FaCoins} color={secondaryTextColor} boxSize={16} />
          <Text color={secondaryTextColor} fontSize="lg">
            {t('investments.noPackages', 'No investment packages found')}
          </Text>
          <Text color={secondaryTextColor} fontSize="sm" textAlign="center" maxW="400px">
            {t('investments.createFirstPackage', 'Create your first investment package to start earning 1% daily interest on your cryptocurrency.')}
          </Text>
          {onCreateNew && (
            <Button
              leftIcon={<Icon as={FaCoins} />}
              bg={primaryColor}
              color={bgColor}
              _hover={{ bg: "#F8D12F" }}
              onClick={onCreateNew}
            >
              {t('investments.createPackage', 'Create New Investment')}
            </Button>
          )}
        </VStack>
      </Center>
    );
  }

  return (
    <Box position="relative">
      {/* Header */}
      <Flex justify="space-between" align="center" mb={6}>
        <HStack>
          <Icon as={FaCoins} color={primaryColor} boxSize={6} />
          <Text fontSize="xl" fontWeight="bold" color={textColor}>
            {t('investments.title', 'Investment Packages')}
          </Text>
          <Badge colorScheme="yellow" variant="solid">
            {packages.length}
          </Badge>
        </HStack>
        
        {onCreateNew && (
          <Button
            size="sm"
            leftIcon={<Icon as={FaCoins} />}
            bg={primaryColor}
            color={bgColor}
            _hover={{ bg: "#F8D12F" }}
            onClick={onCreateNew}
          >
            {t('investments.createNew', 'New Investment')}
          </Button>
        )}
      </Flex>

      {/* Scroll indicator */}
      {packages.length > 1 && (
        <Box
          position="absolute"
          top="50%"
          right={2}
          transform="translateY(-50%)"
          zIndex={2}
          display={{ base: 'none', lg: 'block' }}
          opacity={0.7}
          _hover={{ opacity: 1 }}
          transition="opacity 0.2s"
        >
          <Text fontSize="xs" color={secondaryTextColor} textAlign="center">
            ← {t('common.scroll', 'Scroll')} →
          </Text>
        </Box>
      )}

      {/* Horizontal scrolling container */}
      <Box
        className="investment-packages-horizontal"
        w="100%"
        overflowX="auto"
        overflowY="hidden"
        pb={4}
        sx={{
          // Custom scrollbar styling
          '&::-webkit-scrollbar': {
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            bg: 'rgba(43, 49, 57, 0.3)',
            borderRadius: 'full',
          },
          '&::-webkit-scrollbar-thumb': {
            bg: 'rgba(240, 185, 11, 0.6)',
            borderRadius: 'full',
            _hover: {
              bg: 'rgba(240, 185, 11, 0.8)',
            },
          },
          // Smooth scrolling
          scrollBehavior: 'smooth',
          // Prevent layout shifts
          contain: 'layout style paint',
          // Touch scrolling for mobile
          WebkitOverflowScrolling: 'touch',
          // Scroll snap for better UX
          scrollSnapType: 'x mandatory',
        }}
      >
        <Flex
          gap={{ base: 3, md: 4, lg: 6 }}
          align="stretch"
          minW="fit-content"
          w="max-content"
          px={1}
        >
          {packages.map((pkg, index) => (
            <MotionBox
              key={pkg._id}
              className="investment-package-item"
              minW={{ base: "300px", sm: "340px", md: "380px", lg: "400px" }}
              maxW={{ base: "300px", sm: "340px", md: "380px", lg: "400px" }}
              flexShrink={0}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              sx={{
                scrollSnapAlign: 'start',
                scrollSnapStop: 'normal',
              }}
            >
              <MotionCard
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                h="100%"
                _hover={{
                  transform: 'translateY(-2px)',
                  boxShadow: `0 4px 12px rgba(240, 185, 11, 0.2)`,
                  borderColor: primaryColor
                }}
                transition="all 0.2s"
              >
                <CardBody p={6}>
                  <VStack spacing={4} align="stretch" h="100%">
                    {/* Header */}
                    <Flex justify="space-between" align="center">
                      <HStack>
                        <Icon as={FaCoins} color={primaryColor} />
                        <Text fontWeight="bold" color={textColor}>
                          {pkg.currency} Package
                        </Text>
                      </HStack>
                      <Badge
                        colorScheme={pkg.status === 'active' ? 'green' : pkg.status === 'pending' ? 'yellow' : 'gray'}
                        variant="solid"
                      >
                        {getStatusText(pkg.status)}
                      </Badge>
                    </Flex>

                    {/* Package ID */}
                    <Text fontSize="xs" color={secondaryTextColor} fontFamily="mono">
                      ID: {pkg.packageId}
                    </Text>

                    {/* Amount and Earnings */}
                    <VStack spacing={3} align="stretch">
                      <Flex justify="space-between">
                        <Text color={secondaryTextColor} fontSize="sm">Principal:</Text>
                        <Text color={textColor} fontWeight="bold">
                          {formatCurrency(pkg.amount, pkg.currency)}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color={secondaryTextColor} fontSize="sm">Total Earned:</Text>
                        <Text color={successColor} fontWeight="bold">
                          {formatCurrency(pkg.totalEarned, pkg.currency)}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color={secondaryTextColor} fontSize="sm">ROI:</Text>
                        <Text color={pkg.roi > 0 ? successColor : secondaryTextColor} fontWeight="bold">
                          {pkg.roi.toFixed(2)}%
                        </Text>
                      </Flex>
                    </VStack>

                    <Divider borderColor={borderColor} />

                    {/* Interest and Progress */}
                    <VStack spacing={3} align="stretch">
                      <HStack justify="space-between">
                        <HStack>
                          <Icon as={FaPercentage} color={primaryColor} size="sm" />
                          <Text color={textColor} fontSize="sm">Daily Interest:</Text>
                        </HStack>
                        <Text color={successColor} fontWeight="bold">
                          {(pkg.interestRate * 100).toFixed(1)}%
                        </Text>
                      </HStack>

                      <HStack justify="space-between">
                        <HStack>
                          <Icon as={FaCalendarAlt} color={primaryColor} size="sm" />
                          <Text color={textColor} fontSize="sm">Active Days:</Text>
                        </HStack>
                        <Text color={textColor} fontWeight="bold">
                          {pkg.activeDays} / {pkg.totalDays}
                        </Text>
                      </HStack>

                      {/* Progress bar */}
                      <Box>
                        <Flex justify="space-between" mb={1}>
                          <Text fontSize="xs" color={secondaryTextColor}>Progress</Text>
                          <Text fontSize="xs" color={secondaryTextColor}>
                            {getProgressPercentage(pkg.activeDays, pkg.totalDays).toFixed(1)}%
                          </Text>
                        </Flex>
                        <Progress
                          value={getProgressPercentage(pkg.activeDays, pkg.totalDays)}
                          size="sm"
                          colorScheme="yellow"
                          bg="rgba(43, 49, 57, 0.5)"
                        />
                      </Box>
                    </VStack>

                    {/* 30-Day Lock Status */}
                    <Box>
                      <HStack justify="space-between" mb={2}>
                        <HStack>
                          <Icon
                            as={isPrincipalLocked(pkg.createdAt) ? FaLock : FaUnlock}
                            color={isPrincipalLocked(pkg.createdAt) ? warningColor : successColor}
                            size="sm"
                          />
                          <Text color={textColor} fontSize="sm">Principal Lock:</Text>
                        </HStack>
                        <Badge
                          colorScheme={isPrincipalLocked(pkg.createdAt) ? 'yellow' : 'green'}
                          variant="solid"
                          fontSize="xs"
                        >
                          {isPrincipalLocked(pkg.createdAt) ? 'Locked' : 'Unlocked'}
                        </Badge>
                      </HStack>

                      {isPrincipalLocked(pkg.createdAt) ? (
                        <Text fontSize="xs" color={secondaryTextColor}>
                          <Icon as={FaClock} mr={1} />
                          {calculateDaysRemaining(pkg.createdAt)} days remaining
                        </Text>
                      ) : (
                        <Text fontSize="xs" color={successColor}>
                          <Icon as={FaUnlock} mr={1} />
                          Principal available for withdrawal
                        </Text>
                      )}
                    </Box>

                    {/* Action Button */}
                    <Box mt="auto">
                      {onViewDetails && (
                        <Button
                          size="sm"
                          variant="outline"
                          borderColor={primaryColor}
                          color={primaryColor}
                          _hover={{ bg: primaryColor, color: bgColor }}
                          leftIcon={<Icon as={FaEye} />}
                          w="100%"
                          onClick={() => onViewDetails(pkg._id)}
                        >
                          {t('investments.viewDetails', 'View Details')}
                        </Button>
                      )}
                    </Box>
                  </VStack>
                </CardBody>
              </MotionCard>
            </MotionBox>
          ))}
        </Flex>
      </Box>
    </Box>
  );
};

export default HorizontalInvestmentPackages;
