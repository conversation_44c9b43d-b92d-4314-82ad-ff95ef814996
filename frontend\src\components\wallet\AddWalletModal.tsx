import React, { useState, useEffect } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  useToast,
  FormErrorMessage,
  Textarea,
  Box,
  Icon,
  Badge
} from '@chakra-ui/react';
import { FaWallet, FaInfoCircle } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { walletManagementService, SupportedCurrency } from '../../services/walletManagementService';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';

interface AddWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddWalletModal: React.FC<AddWalletModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { t } = useTranslation();
  const toast = useToast();

  const [formData, setFormData] = useState({
    currency: '',
    address: '',
    label: '',
    network: 'mainnet'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [supportedCurrencies, setSupportedCurrencies] = useState<SupportedCurrency[]>([]);
  const [loadingCurrencies, setLoadingCurrencies] = useState(true);

  // Fetch supported currencies
  useEffect(() => {
    const fetchSupportedCurrencies = async () => {
      try {
        const response = await walletManagementService.getSupportedCurrencies();
        setSupportedCurrencies(response.data);
      } catch (error) {
        console.error('Failed to fetch supported currencies:', error);
      } finally {
        setLoadingCurrencies(false);
      }
    };

    if (isOpen) {
      fetchSupportedCurrencies();
    }
  }, [isOpen]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        currency: '',
        address: '',
        label: '',
        network: 'mainnet'
      });
      setErrors({});
    }
  }, [isOpen]);

  // Update network options when currency changes
  const getNetworkOptions = () => {
    return walletManagementService.getNetworkOptions(formData.currency);
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.currency) {
      newErrors.currency = t('Currency is required');
    }

    if (!formData.address.trim()) {
      newErrors.address = t('Address is required');
    } else if (!walletManagementService.validateAddressFormat(formData.address, formData.currency)) {
      newErrors.address = t('Invalid address format for selected currency');
    }

    if (!formData.label.trim()) {
      newErrors.label = t('Label is required');
    } else if (formData.label.length > 50) {
      newErrors.label = t('Label must be 50 characters or less');
    }

    if (!formData.network) {
      newErrors.network = t('Network is required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const response = await walletManagementService.addAddress(formData);
      
      toast({
        title: t('Success'),
        description: response.message || t('Wallet address added successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Show verification info if needed
      if (response.data.verificationRequired) {
        toast({
          title: t('Verification Required'),
          description: t('Please check your email for verification code'),
          status: 'info',
          duration: 8000,
          isClosable: true,
        });
      }

      onSuccess();
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to add wallet address'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // Auto-update network when currency changes
    if (field === 'currency') {
      const networkOptions = walletManagementService.getNetworkOptions(value);
      setFormData(prev => ({
        ...prev,
        network: networkOptions[0] || 'mainnet'
      }));
    }
  };

  const selectedCurrency = supportedCurrencies.find(c => c.currency === formData.currency);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Icon as={FaWallet} color="blue.500" />
            <Text>{t('Add Wallet Address')}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Currency Selection */}
            <FormControl isInvalid={!!errors.currency}>
              <FormLabel>{t('Currency')}</FormLabel>
              <Select
                placeholder={loadingCurrencies ? t('Loading...') : t('Select currency')}
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
                disabled={loadingCurrencies}
              >
                {supportedCurrencies.map((currency) => (
                  <option key={currency.currency} value={currency.currency}>
                    {currency.currency} - {currency.name}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>{errors.currency}</FormErrorMessage>
            </FormControl>

            {/* Network Selection */}
            {formData.currency && (
              <FormControl isInvalid={!!errors.network}>
                <FormLabel>{t('Network')}</FormLabel>
                <Select
                  value={formData.network}
                  onChange={(e) => handleInputChange('network', e.target.value)}
                >
                  {getNetworkOptions().map((network) => (
                    <option key={network} value={network}>
                      {network.charAt(0).toUpperCase() + network.slice(1)}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{errors.network}</FormErrorMessage>
              </FormControl>
            )}

            {/* Address Input */}
            <FormControl isInvalid={!!errors.address}>
              <FormLabel>{t('Wallet Address')}</FormLabel>
              <Textarea
                placeholder={
                  formData.currency
                    ? walletManagementService.getAddressFormatHint(formData.currency)
                    : t('Enter wallet address')
                }
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                rows={3}
                fontFamily="mono"
                fontSize="sm"
              />
              <FormErrorMessage>{errors.address}</FormErrorMessage>
              {formData.currency && (
                <Text fontSize="xs" color="gray.600" mt={1}>
                  {t('Format')}: {walletManagementService.getAddressFormatHint(formData.currency)}
                </Text>
              )}
            </FormControl>

            {/* Label Input */}
            <FormControl isInvalid={!!errors.label}>
              <FormLabel>{t('Label')}</FormLabel>
              <Input
                placeholder={t('e.g., My Main Wallet, Exchange Wallet')}
                value={formData.label}
                onChange={(e) => handleInputChange('label', e.target.value)}
                maxLength={50}
              />
              <FormErrorMessage>{errors.label}</FormErrorMessage>
              <Text fontSize="xs" color="gray.600" mt={1}>
                {formData.label.length}/50 {t('characters')}
              </Text>
            </FormControl>

            {/* Currency Info */}
            {selectedCurrency && (
              <Box>
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <VStack align="start" spacing={2} flex={1}>
                    <Text fontWeight="semibold">
                      {selectedCurrency.name} ({selectedCurrency.currency})
                    </Text>
                    <Text fontSize="sm">
                      {t('Supported networks')}: {selectedCurrency.networks.join(', ')}
                    </Text>
                    <Text fontSize="sm">
                      {t('Address format')}: {selectedCurrency.addressFormat}
                    </Text>
                  </VStack>
                </Alert>
              </Box>
            )}

            {/* Important Notice */}
            <Alert status="warning" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={1} flex={1}>
                <Text fontWeight="semibold">{t('Important Notice')}</Text>
                <Text fontSize="sm">
                  {t('Please double-check your wallet address. Incorrect addresses may result in permanent loss of funds.')}
                </Text>
                <Text fontSize="sm">
                  {t('You will need to verify this address via email before it can be used for withdrawals.')}
                </Text>
              </VStack>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              {t('Cancel')}
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={loading}
              loadingText={t('Adding...')}
              disabled={!formData.currency || !formData.address || !formData.label}
            >
              {t('Add Address')}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AddWalletModal;
