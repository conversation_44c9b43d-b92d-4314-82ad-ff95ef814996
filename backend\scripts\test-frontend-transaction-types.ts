#!/usr/bin/env npx ts-node

/**
 * Test Script for Frontend Transaction Types Display
 * 
 * This script creates sample transactions of all types (deposit, withdrawal, interest, commission)
 * to test the frontend transaction display functionality.
 * 
 * Usage: npx ts-node scripts/test-frontend-transaction-types.ts
 */

// Load environment variables
require('dotenv').config({ path: require('path').join(__dirname, '../.env.docker') });

import mongoose from 'mongoose';

// Import models
require('../src/models/userModel');
require('../src/models/walletModel');
require('../src/models/transactionModel');

const User = require('../src/models/userModel').default;
const Wallet = require('../src/models/walletModel').default;
const Transaction = require('../src/models/transactionModel').default;

// Test configuration
const TEST_CONFIG = {
  testUser: {
    email: `frontend-test-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    firstName: 'Frontend',
    lastName: 'Test'
  },
  sampleTransactions: [
    {
      type: 'deposit',
      asset: 'USDT',
      amount: 1000,
      status: 'completed',
      description: 'Test deposit transaction'
    },
    {
      type: 'withdrawal',
      asset: 'BTC',
      amount: 0.05,
      status: 'pending',
      description: 'Test withdrawal transaction'
    },
    {
      type: 'interest',
      asset: 'ETH',
      amount: 0.1,
      status: 'completed',
      description: 'Daily interest payment'
    },
    {
      type: 'commission',
      asset: 'USDT',
      amount: 50,
      status: 'completed',
      description: 'Referral commission payment'
    },
    {
      type: 'interest',
      asset: 'BTC',
      amount: 0.001,
      status: 'completed',
      description: 'Daily interest payment'
    },
    {
      type: 'commission',
      asset: 'ETH',
      amount: 0.02,
      status: 'completed',
      description: 'Commission from referral'
    }
  ]
};

class FrontendTransactionTest {
  private testUserId: string = '';
  private testWalletId: string = '';
  private createdTransactionIds: string[] = [];

  /**
   * Connect to database
   */
  private async connectDatabase(): Promise<void> {
    try {
      const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield';
      await mongoose.connect(mongoUri);
      console.log('✅ Database connected');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  /**
   * Create test user and wallet
   */
  private async createTestUser(): Promise<void> {
    try {
      console.log('\n🔧 Creating test user...');

      // Create test user
      const user = await User.create({
        firstName: TEST_CONFIG.testUser.firstName,
        lastName: TEST_CONFIG.testUser.lastName,
        email: TEST_CONFIG.testUser.email,
        password: TEST_CONFIG.testUser.password,
        emailVerified: true,
        kycVerified: true,
        referralCode: Math.random().toString(36).substring(7).toUpperCase()
      });

      this.testUserId = user._id.toString();
      console.log(`✅ Test user created: ${this.testUserId}`);

      // Create test wallet
      const wallet = await Wallet.create({
        userId: user._id,
        assets: [
          {
            symbol: 'USDT',
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            mode: 'interest'
          },
          {
            symbol: 'BTC',
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            mode: 'interest'
          },
          {
            symbol: 'ETH',
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            mode: 'interest'
          }
        ],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });

      this.testWalletId = wallet._id.toString();
      console.log(`✅ Test wallet created: ${this.testWalletId}`);

    } catch (error: any) {
      console.error('❌ Error creating test user:', error.message);
      throw error;
    }
  }

  /**
   * Create sample transactions
   */
  private async createSampleTransactions(): Promise<void> {
    try {
      console.log('\n🔧 Creating sample transactions...');

      for (const [index, txData] of TEST_CONFIG.sampleTransactions.entries()) {
        const transaction = await Transaction.create({
          userId: new mongoose.Types.ObjectId(this.testUserId),
          walletId: new mongoose.Types.ObjectId(this.testWalletId),
          type: txData.type,
          asset: txData.asset,
          amount: txData.amount,
          status: txData.status,
          description: txData.description,
          txHash: txData.type === 'deposit' || txData.type === 'withdrawal' 
            ? `0x${Math.random().toString(16).substring(2, 66)}` 
            : undefined,
          metadata: {
            testTransaction: true,
            testIndex: index,
            createdForFrontendTest: true
          }
        });

        this.createdTransactionIds.push(transaction._id.toString());
        
        console.log(`✅ Created ${txData.type} transaction: ${txData.amount} ${txData.asset} (${txData.status})`);
      }

      console.log(`✅ Created ${this.createdTransactionIds.length} sample transactions`);

    } catch (error: any) {
      console.error('❌ Error creating sample transactions:', error.message);
      throw error;
    }
  }

  /**
   * Display test results
   */
  private async displayTestResults(): Promise<void> {
    try {
      console.log('\n📊 FRONTEND TRANSACTION TEST RESULTS');
      console.log('=====================================');

      // Get all created transactions
      const transactions = await Transaction.find({
        _id: { $in: this.createdTransactionIds.map(id => new mongoose.Types.ObjectId(id)) }
      }).sort({ createdAt: -1 });

      console.log(`\n📦 Created ${transactions.length} test transactions:`);
      console.log('');

      transactions.forEach((tx, index) => {
        const statusIcon = tx.status === 'completed' ? '✅' : tx.status === 'pending' ? '⏳' : '❌';
        const typeIcon = {
          'deposit': '⬇️',
          'withdrawal': '⬆️', 
          'interest': '💰',
          'commission': '🎯'
        }[tx.type] || '📄';

        console.log(`${index + 1}. ${typeIcon} ${tx.type.toUpperCase()}`);
        console.log(`   Amount: ${tx.amount} ${tx.asset}`);
        console.log(`   Status: ${statusIcon} ${tx.status}`);
        console.log(`   Description: ${tx.description}`);
        console.log(`   ID: ${tx._id}`);
        console.log(`   Created: ${tx.createdAt.toISOString()}`);
        console.log('');
      });

      // Display API endpoint information
      console.log('🔗 API ENDPOINT INFORMATION');
      console.log('============================');
      console.log('To test frontend transaction display, use these API endpoints:');
      console.log('');
      console.log('1. Get all transactions:');
      console.log('   GET /api/transactions');
      console.log('');
      console.log('2. Get transactions by type:');
      console.log('   GET /api/transactions?type=deposit');
      console.log('   GET /api/transactions?type=withdrawal');
      console.log('   GET /api/transactions?type=interest');
      console.log('   GET /api/transactions?type=commission');
      console.log('');
      console.log('3. Get user transactions (with auth):');
      console.log(`   GET /api/transactions?userId=${this.testUserId}`);
      console.log('');

      // Display frontend testing instructions
      console.log('🖥️  FRONTEND TESTING INSTRUCTIONS');
      console.log('==================================');
      console.log('1. Login with test user credentials:');
      console.log(`   Email: ${TEST_CONFIG.testUser.email}`);
      console.log(`   Password: ${TEST_CONFIG.testUser.password}`);
      console.log('');
      console.log('2. Navigate to Transaction History page');
      console.log('3. Verify all transaction types are displayed correctly:');
      console.log('   ✅ Deposits (should show with + sign and green color)');
      console.log('   ✅ Withdrawals (should show with - sign and red color)');
      console.log('   ✅ Interest (should show with + sign and green color)');
      console.log('   ✅ Commission (should show with + sign and green color)');
      console.log('');
      console.log('4. Test filtering by transaction type');
      console.log('5. Verify transaction status badges display correctly');
      console.log('6. Check that transaction descriptions are shown');

    } catch (error: any) {
      console.error('❌ Error displaying test results:', error.message);
    }
  }

  /**
   * Cleanup test data
   */
  private async cleanupTestData(): Promise<void> {
    try {
      console.log('\n🧹 Cleaning up test data...');

      // Delete transactions
      if (this.createdTransactionIds.length > 0) {
        const deletedTransactions = await Transaction.deleteMany({
          _id: { $in: this.createdTransactionIds.map(id => new mongoose.Types.ObjectId(id)) }
        });
        console.log(`✅ Deleted ${deletedTransactions.deletedCount} test transactions`);
      }

      // Delete wallet
      if (this.testWalletId) {
        await Wallet.deleteOne({ _id: new mongoose.Types.ObjectId(this.testWalletId) });
        console.log(`✅ Deleted test wallet`);
      }

      // Delete user
      if (this.testUserId) {
        await User.deleteOne({ _id: new mongoose.Types.ObjectId(this.testUserId) });
        console.log(`✅ Deleted test user`);
      }

    } catch (error: any) {
      console.error('❌ Error during cleanup:', error.message);
    }
  }

  /**
   * Run the complete test
   */
  async runTest(): Promise<void> {
    console.log('🧪 FRONTEND TRANSACTION TYPES TEST');
    console.log('===================================');
    console.log('This test creates sample transactions to verify frontend display\n');

    try {
      await this.connectDatabase();
      await this.createTestUser();
      await this.createSampleTransactions();
      await this.displayTestResults();

      console.log('\n🎉 Frontend transaction test completed successfully!');
      console.log('You can now test the frontend transaction display functionality.');

    } catch (error: any) {
      console.error('\n💥 Test failed:', error.message);
    } finally {
      // Ask user if they want to cleanup
      console.log('\n❓ Do you want to cleanup test data? (The test user and transactions will be deleted)');
      console.log('   - Press Ctrl+C to keep test data for frontend testing');
      console.log('   - Wait 10 seconds for automatic cleanup');

      // Auto cleanup after 10 seconds
      setTimeout(async () => {
        await this.cleanupTestData();
        await mongoose.connection.close();
        console.log('✅ Database connection closed');
        process.exit(0);
      }, 10000);
    }
  }
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  const test = new FrontendTransactionTest();
  await test.runTest();
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Test interrupted. Test data preserved for frontend testing.');
  try {
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Execute
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
