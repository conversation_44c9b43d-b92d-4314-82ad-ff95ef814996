// Simple crypto utilities without React dependencies
export const getCryptoColor = (currency: string): string => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return '#F7931A'; // Bitcoin orange
    case 'ETH':
      return '#627EEA'; // Ethereum blue
    case 'USDT':
      return '#26A17B'; // Tether green
    case 'BNB':
      return '#F3BA2F'; // Binance yellow
    case 'ADA':
      return '#0033AD'; // Cardano blue
    case 'DOT':
      return '#E6007A'; // Polkadot pink
    case 'LINK':
      return '#375BD2'; // Chainlink blue
    case 'UNI':
      return '#FF007A'; // Uniswap pink
    case 'MATIC':
      return '#8247E5'; // Polygon purple
    case 'AVAX':
      return '#E84142'; // Avalanche red
    case 'SOL':
      return '#9945FF'; // Solana purple
    case 'ATOM':
      return '#2E3148'; // Cosmos dark blue
    case 'XRP':
      return '#23292F'; // Ripple dark
    case 'LTC':
      return '#BFBBBB'; // Litecoin silver
    case 'BCH':
      return '#8DC351'; // Bitcoin Cash green
    case 'DOGE':
      return '#C2A633'; // Dogecoin gold
    case 'TRX':
      return '#FF060A'; // TRON red
    case 'FTM':
      return '#1969FF'; // Fantom blue
    case 'NEAR':
      return '#00C08B'; // NEAR green
    case 'ALGO':
      return '#000000'; // Algorand black
    case 'XTZ':
      return '#2C7DF7'; // Tezos blue
    case 'FLOW':
      return '#00EF8B'; // Flow green
    case 'ICP':
      return '#29ABE2'; // Internet Computer blue
    default:
      return '#718096'; // Default gray
  }
};

export const getCryptoName = (currency: string): string => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return 'Bitcoin';
    case 'ETH':
      return 'Ethereum';
    case 'USDT':
      return 'Tether USD';
    case 'BNB':
      return 'Binance Coin';
    case 'ADA':
      return 'Cardano';
    case 'DOT':
      return 'Polkadot';
    case 'LINK':
      return 'Chainlink';
    case 'UNI':
      return 'Uniswap';
    default:
      return currency.toUpperCase();
  }
};

export const formatAddress = (address: string, startChars: number = 6, endChars: number = 4): string => {
  if (!address) return '';
  if (address.length <= startChars + endChars) return address;
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
};

export const validateAddressFormat = (address: string, currency: string): boolean => {
  switch (currency.toUpperCase()) {
    case 'BTC':
    case 'LTC':
    case 'BCH':
    case 'DOGE':
      // Bitcoin-like address validation (Legacy, SegWit)
      return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$|^ltc1[a-z0-9]{39,59}$/.test(address);
    case 'ETH':
    case 'USDT':
    case 'BNB':
    case 'LINK':
    case 'UNI':
    case 'MATIC':
    case 'AVAX':
    case 'FTM':
      // Ethereum-based address validation
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    case 'ADA':
      // Cardano address validation (simplified)
      return /^addr1[a-z0-9]{58}$/.test(address);
    case 'DOT':
      // Polkadot address validation (simplified)
      return /^1[a-zA-Z0-9]{47}$/.test(address);
    case 'SOL':
      // Solana address validation (Base58, 32-44 characters)
      return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
    case 'ATOM':
      // Cosmos address validation
      return /^cosmos1[a-z0-9]{38}$/.test(address);
    case 'XRP':
      // Ripple address validation
      return /^r[a-zA-Z0-9]{24,34}$/.test(address);
    case 'TRX':
      // TRON address validation
      return /^T[a-zA-Z0-9]{33}$/.test(address);
    case 'NEAR':
      // NEAR address validation
      return /^[a-z0-9_-]+\.near$|^[a-f0-9]{64}$/.test(address);
    case 'ALGO':
      // Algorand address validation
      return /^[A-Z2-7]{58}$/.test(address);
    case 'XTZ':
      // Tezos address validation
      return /^tz1[a-zA-Z0-9]{33}$|^tz2[a-zA-Z0-9]{33}$|^tz3[a-zA-Z0-9]{33}$/.test(address);
    case 'FLOW':
      // Flow address validation
      return /^0x[a-fA-F0-9]{16}$/.test(address);
    case 'ICP':
      // Internet Computer address validation (simplified)
      return /^[a-z0-9]{5}-[a-z0-9]{5}-[a-z0-9]{5}-[a-z0-9]{5}-[a-z0-9]{3}$/.test(address);
    default:
      return address.length > 10; // Basic length check for unknown currencies
  }
};

export const getAddressFormatHint = (currency: string): string => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return 'Legacy (1...) or SegWit (bc1...) format';
    case 'ETH':
    case 'USDT':
    case 'BNB':
    case 'LINK':
    case 'UNI':
    case 'MATIC':
    case 'AVAX':
    case 'FTM':
      return '0x... format (42 characters)';
    case 'ADA':
      return 'addr1... format (Cardano address)';
    case 'DOT':
      return '1... format (Polkadot address)';
    case 'SOL':
      return 'Base58 format (32-44 characters)';
    case 'ATOM':
      return 'cosmos1... format (Cosmos address)';
    case 'XRP':
      return 'r... format (Ripple address)';
    case 'LTC':
      return 'Legacy (L...) or SegWit (ltc1...) format';
    case 'BCH':
      return 'Legacy (1...) or CashAddr format';
    case 'DOGE':
      return 'D... format (Dogecoin address)';
    case 'TRX':
      return 'T... format (TRON address)';
    case 'NEAR':
      return 'account.near or hex format';
    case 'ALGO':
      return 'Base32 format (58 characters)';
    case 'XTZ':
      return 'tz1/tz2/tz3... format (Tezos address)';
    case 'FLOW':
      return '0x... format (18 characters)';
    case 'ICP':
      return 'xxxxx-xxxxx-xxxxx-xxxxx-xxx format';
    default:
      return 'Please enter a valid address for this currency';
  }
};

export const getNetworkOptions = (currency: string): string[] => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return ['mainnet', 'testnet'];
    case 'ETH':
      return ['mainnet', 'goerli', 'sepolia', 'arbitrum', 'optimism', 'polygon'];
    case 'USDT':
      return ['ethereum', 'tron', 'bsc', 'arbitrum', 'optimism', 'polygon', 'avalanche', 'solana'];
    case 'BNB':
      return ['bsc', 'binance-chain'];
    case 'ADA':
      return ['mainnet', 'testnet'];
    case 'DOT':
      return ['mainnet', 'kusama'];
    case 'LINK':
      return ['ethereum', 'bsc', 'arbitrum', 'optimism', 'polygon', 'avalanche'];
    case 'UNI':
      return ['ethereum', 'arbitrum', 'optimism', 'polygon'];
    case 'MATIC':
      return ['polygon', 'ethereum'];
    case 'AVAX':
      return ['avalanche', 'ethereum'];
    case 'SOL':
      return ['solana'];
    case 'ATOM':
      return ['cosmos'];
    case 'XRP':
      return ['xrpl'];
    case 'LTC':
      return ['mainnet', 'testnet'];
    case 'BCH':
      return ['mainnet', 'testnet'];
    case 'DOGE':
      return ['mainnet', 'testnet'];
    case 'TRX':
      return ['tron'];
    case 'FTM':
      return ['fantom', 'ethereum'];
    case 'NEAR':
      return ['near'];
    case 'ALGO':
      return ['algorand'];
    case 'XTZ':
      return ['tezos'];
    case 'FLOW':
      return ['flow'];
    case 'ICP':
      return ['internet-computer'];
    default:
      return ['mainnet'];
  }
};

export const getSupportedCurrencies = () => {
  return [
    { symbol: 'BTC', name: 'Bitcoin', color: getCryptoColor('BTC') },
    { symbol: 'ETH', name: 'Ethereum', color: getCryptoColor('ETH') },
    { symbol: 'USDT', name: 'Tether USD', color: getCryptoColor('USDT') },
    { symbol: 'BNB', name: 'Binance Coin', color: getCryptoColor('BNB') },
    { symbol: 'ADA', name: 'Cardano', color: getCryptoColor('ADA') },
    { symbol: 'DOT', name: 'Polkadot', color: getCryptoColor('DOT') },
    { symbol: 'LINK', name: 'Chainlink', color: getCryptoColor('LINK') },
    { symbol: 'UNI', name: 'Uniswap', color: getCryptoColor('UNI') },
    { symbol: 'MATIC', name: 'Polygon', color: getCryptoColor('MATIC') },
    { symbol: 'AVAX', name: 'Avalanche', color: getCryptoColor('AVAX') },
    { symbol: 'SOL', name: 'Solana', color: getCryptoColor('SOL') },
    { symbol: 'ATOM', name: 'Cosmos', color: getCryptoColor('ATOM') },
    { symbol: 'XRP', name: 'Ripple', color: getCryptoColor('XRP') },
    { symbol: 'LTC', name: 'Litecoin', color: getCryptoColor('LTC') },
    { symbol: 'BCH', name: 'Bitcoin Cash', color: getCryptoColor('BCH') },
    { symbol: 'DOGE', name: 'Dogecoin', color: getCryptoColor('DOGE') },
    { symbol: 'TRX', name: 'TRON', color: getCryptoColor('TRX') },
    { symbol: 'FTM', name: 'Fantom', color: getCryptoColor('FTM') },
    { symbol: 'NEAR', name: 'NEAR Protocol', color: getCryptoColor('NEAR') },
    { symbol: 'ALGO', name: 'Algorand', color: getCryptoColor('ALGO') },
    { symbol: 'XTZ', name: 'Tezos', color: getCryptoColor('XTZ') },
    { symbol: 'FLOW', name: 'Flow', color: getCryptoColor('FLOW') },
    { symbol: 'ICP', name: 'Internet Computer', color: getCryptoColor('ICP') }
  ];
};
