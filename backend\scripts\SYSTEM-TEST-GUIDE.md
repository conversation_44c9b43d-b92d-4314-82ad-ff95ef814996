# 🧪 Comprehensive System Test Guide

## 📋 **Tổng Quan**

Script `comprehensive-system-test.ts` sẽ test toàn bộ hệ thống từ frontend đến backend và tạo báo cáo Excel chi tiết với kết quả Pass/Fail.

## 🎯 **Các Test Cases Được Thực Hiện**

### **1. Infrastructure Tests**
- ✅ Database Connection
- ✅ API Health Check
- ✅ Frontend Connectivity
- ✅ Database Operations

### **2. Authentication Tests**
- ✅ User Registration
- ✅ User Login
- ✅ User Profile Retrieval
- ✅ Token Authentication

### **3. Wallet Tests**
- ✅ Wallet Creation/Retrieval
- ✅ Transaction History
- ✅ Balance Operations

### **4. Investment Tests**
- ✅ Investment Package Creation
- ✅ Investment Retrieval
- ✅ Daily Interest Calculation

### **5. API Endpoint Tests**
- ✅ All Major API Endpoints
- ✅ Authentication Headers
- ✅ Response Format Validation

## 🚀 **Cách Chạy System Test**

### **Bước 1: Đảm Bảo Containers Đ<PERSON>**

```bash
# Kiểm tra containers
docker ps

# Nếu chưa ch<PERSON>, khởi động containers
docker-compose -f docker-compose.dev-complete.yml up -d
```

### **Bước 2: Chạy Comprehensive System Test**

```bash
# Chạy trong Docker container (Khuyến nghị)
docker exec -it cryptoyield-backend-dev npx ts-node scripts/comprehensive-system-test.ts
```

**Hoặc chạy từ host machine:**
```bash
cd backend
npx ts-node scripts/comprehensive-system-test.ts
```

### **Bước 3: Kiểm Tra Kết Quả**

Script sẽ tạo file Excel báo cáo trong thư mục `backend/scripts/reports/`

## 📊 **Báo Cáo Excel Được Tạo**

### **Sheet 1: Test Summary**
- Tổng số tests
- Số tests Pass/Fail/Skip
- Phần trăm thành công
- Breakdown theo category

### **Sheet 2: Detailed Results**
- Chi tiết từng test case
- Status (PASS/FAIL/SKIP)
- Thời gian thực hiện
- Error messages (nếu có)
- Test data sử dụng

### **Sheet 3: Test Data Used**
- Thông tin test user
- API URLs
- Test currencies và amounts
- Database IDs được tạo

### **Sheet 4: Performance Metrics**
- Thời gian thực hiện theo category
- Average/Max/Min duration
- Performance analysis

## 📝 **Sample Output Console**

```
🧪 COMPREHENSIVE SYSTEM TEST SUITE
===================================
Testing entire system from frontend to backend...

✅ Infrastructure - Database Connection: PASS (245ms)
✅ API - API Health Check: PASS (156ms)
❌ Frontend - Frontend Connectivity: FAIL (5000ms)
   Error: connect ECONNREFUSED 127.0.0.1:3003
✅ Database - Database Operations: PASS (89ms)
✅ Authentication - User Registration: PASS (234ms)
✅ Authentication - User Login: PASS (187ms)
✅ Authentication - User Profile: PASS (123ms)
✅ Wallet - Wallet Operations: PASS (145ms)
✅ Wallet - Transaction History: PASS (98ms)
✅ Investment - Investment Creation: PASS (456ms)
✅ Investment - Investment Retrieval: PASS (134ms)
✅ Investment - Interest Calculation: PASS (623ms)
✅ API - API Endpoint GET /users/profile: PASS (112ms)
✅ API - API Endpoint GET /wallets/info: PASS (98ms)
✅ API - API Endpoint GET /investments/packages: PASS (145ms)
✅ API - API Endpoint GET /wallets/transactions: PASS (87ms)

📊 Generating Excel report...
✅ Excel report generated: /app/scripts/reports/system-test-report-2025-06-09T08-45-23-456Z.xlsx

🧹 Cleaning up test data...
✅ Deleted 3 test investment packages
✅ Deleted 3 test transactions
✅ Deleted test user and wallet

📊 TEST RESULTS SUMMARY
========================
📦 Total Tests: 16
✅ Passed: 15 (93.8%)
❌ Failed: 1 (6.3%)
⏭️ Skipped: 0 (0.0%)
⏱️ Total Duration: 2456ms (2.46s)

📋 Results by Category:
✅ Infrastructure: 3/4
✅ Authentication: 3/3
✅ Wallet: 2/2
✅ Investment: 3/3
✅ API: 4/4
✅ Database: 1/1

❌ Failed Tests:
   - Frontend - Frontend Connectivity: connect ECONNREFUSED 127.0.0.1:3003

🎯 OVERALL STATUS:
🟡 MOSTLY PASSING - Minor issues detected

🎉 Comprehensive system test completed!
```

## 🔧 **Tùy Chỉnh Test Configuration**

Bạn có thể sửa cấu hình test trong file `comprehensive-system-test.ts`:

```typescript
const TEST_CONFIG: TestConfig = {
  baseUrl: 'http://localhost:5000/api',        // Backend API URL
  frontendUrl: 'http://localhost:3003',        // Frontend URL
  testUser: {
    email: `test-system-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    firstName: 'System',
    lastName: 'Test'
  },
  testData: {
    currencies: ['USDT', 'BTC', 'ETH'],         // Test currencies
    amounts: [100, 500, 1000],                  // Test amounts
    investmentAmounts: [50, 250, 500]           // Investment amounts
  }
};
```

## 📁 **Cấu Trúc File Báo Cáo**

```
backend/scripts/reports/
└── system-test-report-2025-06-09T08-45-23-456Z.xlsx
    ├── Test Summary        (Tổng quan kết quả)
    ├── Detailed Results    (Chi tiết từng test)
    ├── Test Data Used      (Data sử dụng để test)
    └── Performance Metrics (Metrics hiệu suất)
```

## 🎯 **Các Trường Hợp Sử Dụng**

### **1. Development Testing**
```bash
# Test trong quá trình phát triển
docker exec -it cryptoyield-backend-dev npx ts-node scripts/comprehensive-system-test.ts
```

### **2. Pre-deployment Testing**
```bash
# Test trước khi deploy
npm run test:system
```

### **3. CI/CD Integration**
```bash
# Trong pipeline CI/CD
docker exec cryptoyield-backend-dev npx ts-node scripts/comprehensive-system-test.ts
```

## 🔍 **Troubleshooting**

### **Lỗi Database Connection**
```bash
# Kiểm tra MongoDB container
docker logs cryptoyield-mongodb-dev

# Restart containers nếu cần
docker-compose -f docker-compose.dev-complete.yml restart
```

### **Lỗi Frontend Connectivity**
- Đảm bảo frontend đang chạy trên port 3003
- Hoặc sửa `frontendUrl` trong config

### **Lỗi API Endpoints**
```bash
# Kiểm tra backend logs
docker logs cryptoyield-backend-dev

# Restart backend container
docker restart cryptoyield-backend-dev
```

### **Lỗi Permission (Excel File)**
```bash
# Tạo thư mục reports nếu chưa có
mkdir -p backend/scripts/reports
chmod 755 backend/scripts/reports
```

## 📈 **Đọc Hiểu Kết Quả**

### **Status Codes**
- ✅ **PASS**: Test thành công
- ❌ **FAIL**: Test thất bại
- ⏭️ **SKIP**: Test bị bỏ qua

### **Performance Thresholds**
- **< 200ms**: Excellent
- **200-500ms**: Good
- **500-1000ms**: Acceptable
- **> 1000ms**: Slow (cần tối ưu)

### **Success Rate Thresholds**
- **100%**: Perfect
- **90-99%**: Excellent
- **80-89%**: Good
- **< 80%**: Needs attention

## 🎉 **Kết Luận**

Script comprehensive system test cung cấp:

1. **✅ Test Coverage Toàn Diện**: Từ frontend đến backend
2. **📊 Báo Cáo Chi Tiết**: Excel với multiple sheets
3. **🔧 Tự Động Cleanup**: Xóa test data sau khi chạy
4. **⚡ Performance Metrics**: Đo thời gian thực hiện
5. **🎯 Pass/Fail Status**: Kết quả rõ ràng cho từng test

**Chạy script này thường xuyên để đảm bảo hệ thống hoạt động ổn định!**
