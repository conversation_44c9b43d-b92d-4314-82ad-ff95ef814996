import React, { useState, useEffect } from 'react';

const SimpleTest: React.FC = () => {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log('🔧 SimpleTest component mounted');
    console.log('🔧 Environment variables:', {
      VITE_API_URL: import.meta.env.VITE_API_URL,
      NODE_ENV: import.meta.env.NODE_ENV,
      MODE: import.meta.env.MODE
    });
  }, []);

  const testDirectFetch = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('🚀 Testing direct fetch to backend...');
      
      // Test 1: Health check
      console.log('📡 Testing health endpoint...');
      const healthResponse = await fetch('http://localhost:5002/health');
      const healthData = await healthResponse.json();
      console.log('✅ Health response:', healthData);
      
      // Test 2: Maintenance status
      console.log('📡 Testing maintenance status endpoint...');
      const maintenanceResponse = await fetch('http://localhost:5002/api/system/maintenance-status');
      const maintenanceData = await maintenanceResponse.json();
      console.log('✅ Maintenance response:', maintenanceData);
      
      // Test 3: Login
      console.log('📡 Testing login endpoint...');
      const loginResponse = await fetch('http://localhost:5002/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      });
      
      const loginData = await loginResponse.json();
      console.log('✅ Login response:', loginData);
      
      setResult({
        health: healthData,
        maintenance: maintenanceData,
        login: loginData,
        success: true
      });
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      setResult({
        error: error.message,
        success: false
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#0B0E11', 
      color: '#EAECEF', 
      minHeight: '100vh',
      fontFamily: 'monospace'
    }}>
      <h1 style={{ color: '#FCD535' }}>Simple Backend Test</h1>
      
      <button 
        onClick={testDirectFetch}
        disabled={loading}
        style={{
          backgroundColor: '#FCD535',
          color: '#0B0E11',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '5px',
          cursor: loading ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 'bold'
        }}
      >
        {loading ? 'Testing...' : 'Test Backend Connection'}
      </button>
      
      {result && (
        <div style={{ 
          marginTop: '20px', 
          padding: '20px', 
          backgroundColor: '#1E2026', 
          borderRadius: '8px',
          border: '1px solid #3C4043'
        }}>
          <h3>Test Results:</h3>
          <pre style={{ 
            whiteSpace: 'pre-wrap', 
            wordBreak: 'break-word',
            fontSize: '12px',
            lineHeight: '1.4'
          }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
      
      <div style={{ marginTop: '20px', fontSize: '14px' }}>
        <p><strong>Instructions:</strong></p>
        <ol>
          <li>Open browser Developer Tools (F12)</li>
          <li>Go to Console tab</li>
          <li>Click "Test Backend Connection" button</li>
          <li>Watch console logs for detailed information</li>
          <li>Check Network tab for actual HTTP requests</li>
        </ol>
      </div>
    </div>
  );
};

export default SimpleTest;
