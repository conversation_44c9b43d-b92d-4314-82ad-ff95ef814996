import React, { useEffect, useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Icon,
  Button,
  SimpleGrid,
  VStack,
  HStack,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Spinner,
  Alert,
  AlertIcon,
  useToast,
  useColorModeValue,
  useDisclosure,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
} from '@chakra-ui/react';
import {
  FaMoneyBillWave,
  FaCalendarAlt,
  FaCoins,
  FaChartLine,
  FaArrowUp,
  FaArrowDown,
  FaExchangeAlt,
  FaPlus,
  FaInfoCircle,
  FaHistory,
  FaSearch,
  FaFilter,
  FaSortAmountDown,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import { investmentService } from '../services/investmentService';
import InvestmentCard from './InvestmentCard';
import DepositModal from './modals/DepositModal';
import ThreeStepWithdrawModal from './modals/ThreeStepWithdrawModal';
import { cryptoIcons, cryptoColors, getCryptoIcon, getCryptoColor } from '../utils/cryptoIcons';

// Define GroupedInvestment interface
interface GroupedInvestment {
  currency: string;
  totalAmount: number;
  investments: Investment[];
  firstInvestmentDate: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected' | 'mixed';
  networks: string[];
  addresses: string[];
}

interface Investment {
  _id: string;
  userId: string;
  currency: string;
  amount: number;
  description?: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected';
  receiptUrl?: string;
  cryptoAddress: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  // Amount correction fields for admin verification
  originalAmount?: number;
  adminVerifiedAmount?: number;
  amountModifiedBy?: string;
  amountModifiedAt?: string;
  amountCorrectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

const UserInvestments: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useToast();
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [groupedInvestments, setGroupedInvestments] = useState<GroupedInvestment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalInvested, setTotalInvested] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('date-desc');

  // Modal states for deposit and withdraw functionality
  const { isOpen: isDepositOpen, onOpen: onDepositOpen, onClose: onDepositClose } = useDisclosure();
  const { isOpen: isWithdrawOpen, onOpen: onWithdrawOpen, onClose: onWithdrawClose } = useDisclosure();
  const [selectedCrypto, setSelectedCrypto] = useState<string>('USDT');
  const [selectedInterestAmount, setSelectedInterestAmount] = useState<number>(0);

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";

  useEffect(() => {
    fetchInvestments();
  }, [toast]); // Add toast as dependency since it's used in fetchInvestments

  const fetchInvestments = async () => {
    try {
      setLoading(true);
      console.log('Fetching investments...');

      // Sử dụng phương thức getGroupedInvestments từ investmentService
      const response = await investmentService.getGroupedInvestments();
      console.log('Investment response:', response);

      // Kiểm tra cấu trúc dữ liệu trả về
      let investmentsData: Investment[] = [];
      let groupedInvestmentsData: GroupedInvestment[] = [];

      if (response && response.investments && Array.isArray(response.investments)) {
        // Lấy danh sách đầu tư gốc
        investmentsData = response.investments;
      } else if (response && Array.isArray(response)) {
        // Cấu trúc trực tiếp là mảng
        investmentsData = response;
      } else if (response && typeof response === 'object') {
        // Trường hợp khác, thử lấy dữ liệu từ response
        const possibleData = response.data || response.investments || response;
        if (Array.isArray(possibleData)) {
          investmentsData = possibleData;
        }
      }

      // Lấy dữ liệu đã được nhóm từ backend
      if (response && response.groupedInvestments && Array.isArray(response.groupedInvestments)) {
        groupedInvestmentsData = response.groupedInvestments;
        console.log('Grouped investments from backend:', groupedInvestmentsData);
      }

      console.log('Processed investments data:', investmentsData);

      // Cập nhật state với dữ liệu từ API
      setInvestments(investmentsData);

      // Tính tổng số tiền đầu tư (chỉ tính các khoản đầu tư đã được phê duyệt)
      const total = investmentsData.reduce((sum, inv) => {
        if (inv.status === 'approved') {
          return sum + inv.amount;
        }
        return sum;
      }, 0);

      setTotalInvested(total);

      // Tính tổng số tiền kiếm được (1% hàng ngày cho mỗi khoản đầu tư đã được phê duyệt)
      const earnings = investmentsData.reduce((sum, inv) => {
        if (inv.status === 'approved') {
          // Tính số ngày kể từ khi phê duyệt
          const approvalDate = inv.approvedAt ? new Date(inv.approvedAt) : new Date(inv.createdAt);
          const today = new Date();
          const diffTime = Math.abs(today.getTime() - approvalDate.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // 1% lợi nhuận hàng ngày
          return sum + (inv.amount * 0.01 * diffDays);
        }
        return sum;
      }, 0);

      setTotalEarned(earnings);

      // Nếu không có dữ liệu đã nhóm từ backend, sử dụng hàm nhóm ở frontend
      if (groupedInvestmentsData.length === 0 && investmentsData.length > 0) {
        console.log('No grouped data from backend, using frontend grouping');
        // Sử dụng hàm nhóm ở frontend (sẽ được xóa sau khi backend hoạt động ổn định)
        groupedInvestmentsData = groupInvestmentsByCurrency(investmentsData);
      }

      // Cập nhật state với dữ liệu đã nhóm
      setGroupedInvestments(groupedInvestmentsData);

    } catch (err: any) {
      console.error('Error fetching investments:', err);
      setError(err.message || 'Failed to fetch investments');
      toast({
        title: 'Error',
        description: 'Failed to fetch investments: ' + (err.message || 'Unknown error'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge colorScheme="green">Approved</Badge>;
      case 'pending':
        return <Badge colorScheme="yellow">Pending</Badge>;
      case 'processing':
        return <Badge colorScheme="blue">Processing</Badge>;
      case 'rejected':
        return <Badge colorScheme="red">Rejected</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Group investments by currency
  const groupInvestmentsByCurrency = (investments: Investment[]): GroupedInvestment[] => {
    // First, filter only approved investments
    const approvedInvestments = investments.filter(inv => inv.status === 'approved');

    console.log('Approved investments:', approvedInvestments);

    // Group investments by currency using reduce instead of forEach
    const groupedByCurrency = approvedInvestments.reduce<Record<string, Investment[]>>((acc, investment) => {
      // Chuẩn hóa currency để đảm bảo nhóm đúng (viết hoa)
      const currency = investment.currency.toUpperCase();

      if (!acc[currency]) {
        acc[currency] = [];
      }

      acc[currency].push(investment);
      return acc;
    }, {});

    console.log('Grouped by currency:', groupedByCurrency);

    // Then, convert to GroupedInvestment array
    return Object.entries(groupedByCurrency).map(([currency, investments]) => {
      // Sort investments by date (oldest first) to get the first investment date
      const sortedInvestments = [...investments].sort(
        (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      // Calculate total amount
      const totalAmount = investments.reduce((sum, inv) => sum + inv.amount, 0);

      // All investments are approved since we filtered them above
      const status = 'approved';

      // Get unique networks
      const networks = [...new Set(investments.map(inv => inv.network || 'Default'))];

      // Get unique addresses
      const addresses = [...new Set(investments.map(inv => inv.cryptoAddress))];

      return {
        currency,
        totalAmount,
        investments,
        firstInvestmentDate: sortedInvestments[0].createdAt,
        status,
        networks,
        addresses,
      };
    });
  };

  // Kiểm tra xem có khoản đầu tư nào đã được approve chưa
  const hasApprovedInvestments = investments.some(inv => inv.status === 'approved');
  console.log('Has approved investments:', hasApprovedInvestments);
  console.log('Grouped investments:', groupedInvestments);

  const filteredGroupedInvestments = groupedInvestments
    .filter(group => {
      // Filter by search term (currency)
      const matchesSearch =
        group.currency.toLowerCase().includes(searchTerm.toLowerCase()) ||
        group.addresses.some(address => address.toLowerCase().includes(searchTerm.toLowerCase()));

      // Filter by status
      const matchesStatus =
        filterStatus === 'all' ||
        group.status === filterStatus;

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      // Sort by selected criteria
      switch (sortBy) {
        case 'amount-desc':
          return b.totalAmount - a.totalAmount;
        case 'amount-asc':
          return a.totalAmount - b.totalAmount;
        case 'date-desc':
          return new Date(b.firstInvestmentDate).getTime() - new Date(a.firstInvestmentDate).getTime();
        case 'date-asc':
          return new Date(a.firstInvestmentDate).getTime() - new Date(b.firstInvestmentDate).getTime();
        default:
          return new Date(b.firstInvestmentDate).getTime() - new Date(a.firstInvestmentDate).getTime();
      }
    });

  // Handle view details - redirect to transactions page with filters
  const navigate = useNavigate();
  const handleViewDetails = (currency: string) => {
    // Navigate to transactions page with filter for this currency and deposit type
    navigate(`/transactions?currency=${currency}&type=deposit`);

    toast({
      title: 'Investment Details',
      description: `Viewing all ${currency} investments`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  // Enhanced deposit button click handler for investment cards
  const handleDepositClick = (currency: string, investmentData?: any) => {
    console.log(`Opening deposit modal for ${currency}:`, {
      currency: currency,
      investmentData: investmentData
    });
    setSelectedCrypto(currency);
    onDepositOpen();
  };

  // Enhanced withdraw button click handler with Total Earned data
  const handleWithdrawClick = (currency: string, investmentData?: any) => {
    const totalEarned = investmentData?.totalEarned || 0;
    console.log(`Opening withdraw modal for ${currency}:`, {
      currency: currency,
      totalEarned: totalEarned,
      investmentData: investmentData
    });
    setSelectedCrypto(currency);
    setSelectedInterestAmount(totalEarned);
    onWithdrawOpen();
  };

  if (loading) {
    return (
      <Box textAlign="center" py={10}>
        <Spinner size="xl" color={primaryColor} />
        <Text mt={4} color={textColor}>Loading investments...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error" borderRadius="md" mb={4}>
        <AlertIcon />
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Investment Summary - Removed */}

      {/* Filter and Search Controls - Removed */}

      {/* Investment List */}
      {investments.length > 0 ? (
        hasApprovedInvestments ? (
          filteredGroupedInvestments.length > 0 ? (
            <Box position="relative">
              {/* Scroll indicator for desktop */}
              {filteredGroupedInvestments.length > 1 && (
                <Box
                  position="absolute"
                  top="50%"
                  right={2}
                  transform="translateY(-50%)"
                  zIndex={2}
                  display={{ base: 'none', lg: 'block' }}
                  opacity={0.7}
                  _hover={{ opacity: 1 }}
                  transition="opacity 0.2s"
                >
                  <Text fontSize="xs" color="#848E9C" textAlign="center">
                    ← Scroll →
                  </Text>
                </Box>
              )}

              <Box
                className="wallet-cards-horizontal"
                w="100%"
                overflowX="auto"
                overflowY="hidden"
                pb={4}
                sx={{
                  // Custom scrollbar styling for better UX
                  '&::-webkit-scrollbar': {
                    height: '8px',
                  },
                  '&::-webkit-scrollbar-track': {
                    bg: 'rgba(43, 49, 57, 0.3)',
                    borderRadius: 'full',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    bg: 'rgba(240, 185, 11, 0.6)',
                    borderRadius: 'full',
                    _hover: {
                      bg: 'rgba(240, 185, 11, 0.8)',
                    },
                  },
                  // Smooth scrolling
                  scrollBehavior: 'smooth',
                  // Prevent layout shifts
                  contain: 'layout style paint',
                  // Touch scrolling for mobile
                  WebkitOverflowScrolling: 'touch',
                  // Scroll snap for better UX
                  scrollSnapType: 'x mandatory',
                }}
              >
              <Flex
                gap={{ base: 3, md: 4, lg: 6 }}
                align="stretch"
                minW="fit-content"
                w="max-content"
                px={1}
              >
                {filteredGroupedInvestments.map((groupedInvestment) => (
                  <Box
                    key={groupedInvestment.currency}
                    className="wallet-card-item"
                    minW={{ base: "320px", sm: "360px", md: "400px", lg: "420px" }}
                    maxW={{ base: "320px", sm: "360px", md: "400px", lg: "420px" }}
                    flexShrink={0}
                    sx={{
                      scrollSnapAlign: 'start',
                      scrollSnapStop: 'normal',
                    }}
                  >
                    <InvestmentCard
                      groupedInvestment={groupedInvestment}
                      totalEarned={totalEarned}
                      onViewDetails={handleViewDetails}
                      onDepositClick={handleDepositClick}
                      onWithdrawClick={handleWithdrawClick}
                    />
                  </Box>
                ))}
              </Flex>
              </Box>
            </Box>
          ) : (
            <Box
              bg={bgColor}
              p={8}
              borderRadius="lg"
              borderWidth="1px"
              borderColor={borderColor}
              textAlign="center"
            >
              <Icon as={FaCoins} boxSize={12} color={primaryColor} mb={4} />
              <Text color={secondaryTextColor} fontSize="lg" fontWeight="medium" mb={6}>
                {t('investment.noMatchingInvestments', 'No matching investments found with current filters.')}
              </Text>
              <Button
                variant="outline"
                colorScheme="yellow"
                onClick={() => {
                  setSearchTerm('');
                  setFilterStatus('all');
                  setSortBy('date-desc');
                }}
              >
                {t('investment.clearFilters', 'Clear Filters')}
              </Button>
            </Box>
          )
        ) : (
          // Có đầu tư nhưng chưa được approve
          <Box
            bg={bgColor}
            p={8}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
            textAlign="center"
          >
            <Icon as={FaCoins} boxSize={12} color={primaryColor} mb={4} />
            <Text color={secondaryTextColor} fontSize="xl" fontWeight="medium" mb={6}>
              {t('investment.pendingInvestments', 'Your investments are pending approval')}
            </Text>
            <Text color={secondaryTextColor} mb={6}>
              {t('investment.pendingMessage', 'Your investments will appear here once they are approved by our team.')}
            </Text>
            <Button
              leftIcon={<Icon as={FaPlus} />}
              colorScheme="yellow"
              onClick={() => {
                // Chuyển hướng đến trang transactions để xem các khoản đầu tư đang chờ duyệt
                navigate('/transactions?type=deposit');
              }}
            >
              {t('investment.viewPendingInvestments', 'View Pending Investments')}
            </Button>
          </Box>
        )
      ) : (
        // Không có đầu tư nào
        <Box
          bg={bgColor}
          p={8}
          borderRadius="lg"
          borderWidth="1px"
          borderColor={borderColor}
          textAlign="center"
        >
          <Icon as={FaCoins} boxSize={12} color={primaryColor} mb={4} />
          <Text color={secondaryTextColor} fontSize="xl" fontWeight="medium" mb={6}>
            {t('investment.noInvestments', 'You have no investment')}
          </Text>
          <Text color={secondaryTextColor} mb={6}>
            {t('investment.startInvesting', 'Start investing to earn daily returns!')}
          </Text>
          <Button
            leftIcon={<Icon as={FaPlus} />}
            colorScheme="yellow"
            onClick={() => {
              toast({
                title: t('investment.investment', 'Investment'),
                description: t('investment.redirectingToDeposit', 'Redirecting to deposit page...'),
                status: 'info',
                duration: 3000,
                isClosable: true,
              });
              // Chuyển hướng đến trang deposit
              window.location.href = '/deposit';
            }}
          >
            {t('investment.makeFirstInvestment', 'Make Your First Investment')}
          </Button>
        </Box>
      )}

      {/* Enhanced Modal Integration */}
      <DepositModal
        isOpen={isDepositOpen}
        onClose={onDepositClose}
        defaultAsset={selectedCrypto}
        onSuccess={() => {
          fetchInvestments();
          toast({
            title: t('investments.depositSuccess', 'Deposit Successful'),
            description: t('investments.depositSuccessDesc', 'Your deposit has been submitted successfully.'),
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
        }}
      />

    </Box>
  );
};

export default UserInvestments;
