# CryptoYield Development Environment - Complete Stack
# Frontend + Backend + Database + Admin Interface

services:
  # MongoDB service with replica set for transaction support
  mongodb:
    image: mongo:7.0
    container_name: cryptoyield-mongodb
    restart: always
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    ports:
      - "27017:27017"
    networks:
      - cryptoyield-network
    command: mongod --replSet rs0 --bind_ip_all --noauth
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    environment:
      - MONGO_INITDB_DATABASE=cryptoyield

  # Redis service for caching and session management
  redis:
    image: redis:7-alpine
    container_name: cryptoyield-redis
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cryptoyield-network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # Mongo Express service (MongoDB admin interface)
  mongo-express:
    image: mongo-express:latest
    container_name: cryptoyield-mongo-express
    restart: always
    depends_on:
      - mongodb
    environment:
      # MongoDB connection settings (no auth for development)
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      
      # Basic authentication for Mongo Express web interface
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
      
      # Mongo Express settings
      ME_CONFIG_MONGODB_ENABLE_ADMIN: "true"
      ME_CONFIG_OPTIONS_EDITORTHEME: ambiance
      
      # Connection URL (no auth for development)
      ME_CONFIG_MONGODB_URL: mongodb://mongodb:27017/
    ports:
      - "8081:8081"
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 45s

  # Backend service with live code editing and hot-reload
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cryptoyield-backend
    restart: always
    ports:
      - "5001:5000"  # Backend runs on port 5000 internally, exposed on 5001
    working_dir: /app
    environment:
      # Development environment variables
      NODE_ENV: development
      PORT: 5000
      
      # MongoDB connection (using container network, no auth for development)
      MONGO_URI: mongodb://mongodb:27017/cryptoyield?replicaSet=rs0
      
      # Redis connection (using container network)
      REDIS_URL: redis://redis:6379
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # JWT configuration
      JWT_SECRET: crypto_yield_hub_dev_jwt_secret
      JWT_EXPIRES_IN: 1d
      JWT_REFRESH_EXPIRES_IN: 7d
      
      # CORS - Allow frontend access
      FRONTEND_URL: http://localhost:3003
      CORS_ORIGIN: "http://localhost:3003,http://localhost:3004,http://localhost:3005"
      
      # Blockchain
      CONTRACT_ADDRESS: ******************************************
      PROVIDER_URL: https://mainnet.infura.io/v3/********************************
      
      # Logging
      LOG_LEVEL: debug
      
      # Rate limiting
      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX: 1000
      
      # Cache
      CACHE_TTL: 300
      
      # Email configuration
      EMAIL_HOST: mail.shpnfinance.com
      EMAIL_PORT: 587
      EMAIL_SECURE: false
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: ThisIsPass@123
      EMAIL_FROM: <EMAIL>
    volumes:
      # Mount source code for live editing
      - ./backend/src:/app/src:cached
      - ./backend/package.json:/app/package.json:ro
      - ./backend/package-lock.json:/app/package-lock.json:ro
      - ./backend/tsconfig.json:/app/tsconfig.json:ro
      - ./backend/nodemon.json:/app/nodemon.json:ro
      - ./backend/.env.docker:/app/.env.docker:ro
      - ./backend/scripts:/app/scripts:cached
      - ./backend/uploads:/app/uploads:cached
      # Exclude node_modules to avoid conflicts
      - backend_node_modules:/app/node_modules
    networks:
      - cryptoyield-network
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["npm", "run", "dev:docker"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend service with live code editing and hot-reload
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: cryptoyield-frontend
    restart: always
    ports:
      - "3003:3003"  # Frontend development server
    working_dir: /app
    environment:
      # Development environment variables
      NODE_ENV: development
      
      # API configuration - connect to backend service
      VITE_API_URL: http://localhost:5001/api
      VITE_SOCKET_URL: http://localhost:5001
      
      # Frontend configuration
      VITE_APP_NAME: CryptoYield
      VITE_APP_VERSION: 1.0.0
      
      # Blockchain configuration
      VITE_CONTRACT_ADDRESS: ******************************************
      VITE_PROVIDER_URL: https://mainnet.infura.io/v3/********************************
      
      # Development settings
      VITE_DEV_MODE: true
      VITE_DEBUG: true
      
      # Hot reload settings
      CHOKIDAR_USEPOLLING: true
      WATCHPACK_POLLING: true
    volumes:
      # Mount source code for live editing
      - ./frontend/src:/app/src:cached
      - ./frontend/public:/app/public:cached
      - ./frontend/index.html:/app/index.html:ro
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/package-lock.json:/app/package-lock.json:ro
      - ./frontend/vite.config.ts:/app/vite.config.ts:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
      - ./frontend/tsconfig.app.json:/app/tsconfig.app.json:ro
      - ./frontend/tsconfig.node.json:/app/tsconfig.node.json:ro
      - ./frontend/eslint.config.js:/app/eslint.config.js:ro
      # Exclude node_modules to avoid conflicts
      - frontend_node_modules:/app/node_modules
    networks:
      - cryptoyield-network
    depends_on:
      backend:
        condition: service_healthy
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3003"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  cryptoyield-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  backend_node_modules:
    driver: local
  frontend_node_modules:
    driver: local
