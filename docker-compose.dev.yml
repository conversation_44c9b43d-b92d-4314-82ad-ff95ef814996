version: '3.8'

services:
  # MongoDB service with replica set for transaction support
  mongodb:
    image: mongo:7.0
    container_name: cryptoyield-mongodb-dev
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: cryptoyield_admin
      MONGO_INITDB_ROOT_PASSWORD: secure_password123
      MONGO_INITDB_DATABASE: cryptoyield
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongo-init-replica.js:/docker-entrypoint-initdb.d/mongo-init-replica.js:ro
      - ./mongodb-keyfile/mongodb-keyfile:/tmp/keyfile-source:ro
    ports:
      - "27017:27017"
    networks:
      - cryptoyield-dev-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    entrypoint: >
      bash -c "
        cp /tmp/keyfile-source /data/keyfile &&
        chmod 600 /data/keyfile &&
        chown mongodb:mongodb /data/keyfile &&
        exec docker-entrypoint.sh mongod --replSet rs0 --keyFile /data/keyfile --bind_ip_all
      "

  # Mongo Express service (MongoDB admin interface)
  mongo-express:
    image: mongo-express:latest
    container_name: cryptoyield-mongo-express-dev
    restart: always
    depends_on:
      - mongodb
    environment:
      # MongoDB connection settings
      ME_CONFIG_MONGODB_ADMINUSERNAME: cryptoyield_admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: secure_password123
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_MONGODB_AUTH_DATABASE: admin

      # Basic authentication for Mongo Express web interface
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123

      # Mongo Express settings
      ME_CONFIG_MONGODB_ENABLE_ADMIN: "true"
      ME_CONFIG_OPTIONS_EDITORTHEME: ambiance

      # Connection URL
      ME_CONFIG_MONGODB_URL: ***********************************************************************************
    ports:
      - "8081:8081"
    networks:
      - cryptoyield-dev-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 45s

  # Redis service for caching and session management
  redis:
    image: redis:7-alpine
    container_name: cryptoyield-redis-dev
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cryptoyield-dev-network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # Backend service with live code editing and hot-reload
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cryptoyield-backend-dev
    restart: always
    ports:
      - "5000:5000"  # Backend runs on port 5000
    working_dir: /app
    environment:
      # Development environment variables
      NODE_ENV: development
      PORT: 5000

      # MongoDB connection (using container network)
      MONGO_URI: ********************************************************************************************************
      MONGO_USER: cryptoyield_admin
      MONGO_PASSWORD: secure_password123

      # Redis connection (using container network)
      REDIS_URL: redis://redis:6379
      REDIS_HOST: redis
      REDIS_PORT: 6379

      # JWT configuration
      JWT_SECRET: crypto_yield_hub_dev_jwt_secret
      JWT_EXPIRES_IN: 1d
      JWT_REFRESH_EXPIRES_IN: 7d

      # CORS
      FRONTEND_URL: http://localhost:3003
      CORS_ORIGIN: "*"

      # Blockchain
      CONTRACT_ADDRESS: ******************************************
      PROVIDER_URL: https://mainnet.infura.io/v3/********************************

      # Logging
      LOG_LEVEL: debug

      # Rate limiting
      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX: 1000

      # Cache
      CACHE_TTL: 300

      # Email configuration
      EMAIL_HOST: mail.shpnfinance.com
      EMAIL_PORT: 587
      EMAIL_SECURE: false
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: ThisIsPass@123
      EMAIL_FROM: <EMAIL>
    volumes:
      # Mount source code for live editing
      - ./backend/src:/app/src:cached
      - ./backend/package.json:/app/package.json:ro
      - ./backend/package-lock.json:/app/package-lock.json:ro
      - ./backend/tsconfig.json:/app/tsconfig.json:ro
      - ./backend/nodemon.json:/app/nodemon.json:ro
      - ./backend/.env.docker:/app/.env.docker:ro
      - ./backend/scripts:/app/scripts:cached
      - ./backend/uploads:/app/uploads:cached
      # Exclude node_modules to avoid conflicts
      - backend_node_modules:/app/node_modules
    networks:
      - cryptoyield-dev-network
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["npm", "run", "dev:docker"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  cryptoyield-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  backend_node_modules:
    driver: local
