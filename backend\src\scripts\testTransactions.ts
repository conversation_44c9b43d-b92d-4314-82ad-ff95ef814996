#!/usr/bin/env ts-node

/**
 * Test script to verify MongoDB transaction functionality
 * This script tests various transaction scenarios to ensure proper configuration
 */

import mongoose from 'mongoose';
import { db } from '../config/database';
import { logger } from '../config/logger';
import transactionManager from '../utils/transactionManager';
import { User } from '../models/userModel';
import { Wallet } from '../models/walletModel';
import { Transaction } from '../models/transactionModel';

// Test data
const testUser = {
  firstName: 'Transaction',
  lastName: 'Test',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  phoneNumber: '+1234567890',
  country: 'US',
  city: 'Test City',
  isVerified: true
};

const testWallet = {
  assets: [{
    symbol: 'BTC',
    balance: 1000,
    commissionBalance: 100,
    interestBalance: 50,
    mode: 'commission' as const,
    network: 'bitcoin'
  }],
  totalCommissionEarned: 100,
  totalInterestEarned: 50
};

async function testBasicTransaction() {
  logger.info('🧪 Testing basic transaction functionality...');
  
  try {
    const result = await transactionManager.executeTransaction(async (session) => {
      // Create a test user within transaction
      const user = await User.create([testUser], { session });
      logger.info('✅ User created within transaction');
      
      // Create a test wallet within transaction
      const wallet = await Wallet.create([{
        ...testWallet,
        userId: user[0]._id
      }], { session });
      logger.info('✅ Wallet created within transaction');
      
      return { user: user[0], wallet: wallet[0] };
    });
    
    logger.info('✅ Basic transaction test passed');
    return result;
  } catch (error) {
    logger.error('❌ Basic transaction test failed:', error);
    throw error;
  }
}

async function testTransactionRollback() {
  logger.info('🧪 Testing transaction rollback functionality...');
  
  try {
    await transactionManager.executeTransaction(async (session) => {
      // Create a test user
      const user = await User.create([{
        ...testUser,
        email: '<EMAIL>'
      }], { session });
      logger.info('✅ User created for rollback test');
      
      // Create a wallet
      const wallet = await Wallet.create([{
        ...testWallet,
        userId: user[0]._id
      }], { session });
      logger.info('✅ Wallet created for rollback test');
      
      // Intentionally throw an error to trigger rollback
      throw new Error('Intentional error to test rollback');
    });
    
    logger.error('❌ Transaction should have failed but didn\'t');
    throw new Error('Rollback test failed - transaction should have been aborted');
  } catch (error) {
    if (error.message === 'Intentional error to test rollback') {
      logger.info('✅ Transaction rollback test passed');
      
      // Verify that no data was persisted
      const userExists = await User.findOne({ email: '<EMAIL>' });
      if (userExists) {
        throw new Error('Rollback failed - user data was persisted');
      }
      logger.info('✅ Rollback verification passed - no data persisted');
    } else {
      logger.error('❌ Unexpected error in rollback test:', error);
      throw error;
    }
  }
}

async function testBatchOperations() {
  logger.info('🧪 Testing batch operations within transaction...');
  
  try {
    const operations = [
      async (session: mongoose.ClientSession) => {
        return await User.create([{
          ...testUser,
          email: '<EMAIL>'
        }], { session });
      },
      async (session: mongoose.ClientSession) => {
        return await User.create([{
          ...testUser,
          email: '<EMAIL>'
        }], { session });
      },
      async (session: mongoose.ClientSession) => {
        return await User.create([{
          ...testUser,
          email: '<EMAIL>'
        }], { session });
      }
    ];
    
    const results = await transactionManager.batchOperations(operations);
    logger.info(`✅ Batch operations test passed - created ${results.length} users`);
    
    return results;
  } catch (error) {
    logger.error('❌ Batch operations test failed:', error);
    throw error;
  }
}

async function testConditionalTransaction() {
  logger.info('🧪 Testing conditional transaction functionality...');
  
  try {
    const conditions = [
      {
        condition: async () => true, // Always execute
        operation: async (session: mongoose.ClientSession) => {
          return await User.create([{
            ...testUser,
            email: '<EMAIL>'
          }], { session });
        }
      },
      {
        condition: async () => false, // Never execute
        operation: async (session: mongoose.ClientSession) => {
          return await User.create([{
            ...testUser,
            email: '<EMAIL>'
          }], { session });
        },
        onSkip: () => logger.info('✅ Conditional operation skipped as expected')
      }
    ];
    
    const results = await transactionManager.conditionalTransaction(conditions);
    
    if (results.length === 1) {
      logger.info('✅ Conditional transaction test passed');
    } else {
      throw new Error(`Expected 1 result, got ${results.length}`);
    }
    
    return results;
  } catch (error) {
    logger.error('❌ Conditional transaction test failed:', error);
    throw error;
  }
}

async function testValidationAndExecution() {
  logger.info('🧪 Testing validation and execution functionality...');
  
  try {
    const validators = [
      async (session: mongoose.ClientSession) => {
        // Validate that we can connect to the database
        const admin = mongoose.connection.db.admin();
        await admin.command({ ping: 1 });
        logger.info('✅ Database connectivity validation passed');
      },
      async (session: mongoose.ClientSession) => {
        // Validate that the email doesn't already exist
        const existingUser = await User.findOne({ 
          email: '<EMAIL>' 
        }).session(session);
        
        if (existingUser) {
          throw new Error('User with this email already exists');
        }
        logger.info('✅ Email uniqueness validation passed');
      }
    ];
    
    const operation = async (session: mongoose.ClientSession) => {
      return await User.create([{
        ...testUser,
        email: '<EMAIL>'
      }], { session });
    };
    
    const result = await transactionManager.validateAndExecute(validators, operation);
    logger.info('✅ Validation and execution test passed');
    
    return result;
  } catch (error) {
    logger.error('❌ Validation and execution test failed:', error);
    throw error;
  }
}

async function cleanupTestData() {
  logger.info('🧹 Cleaning up test data...');
  
  try {
    // Delete all test users
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    for (const email of testEmails) {
      const user = await User.findOne({ email });
      if (user) {
        // Delete associated wallet
        await Wallet.deleteMany({ userId: user._id });
        // Delete user
        await User.deleteOne({ _id: user._id });
        logger.info(`✅ Cleaned up user: ${email}`);
      }
    }
    
    logger.info('✅ Test data cleanup completed');
  } catch (error) {
    logger.error('❌ Test data cleanup failed:', error);
  }
}

async function runAllTests() {
  logger.info('🚀 Starting MongoDB transaction tests...');
  
  try {
    // Connect to database
    await db.connect();
    logger.info('✅ Database connected');
    
    // Run tests
    await testBasicTransaction();
    await testTransactionRollback();
    await testBatchOperations();
    await testConditionalTransaction();
    await testValidationAndExecution();
    
    logger.info('🎉 All transaction tests passed successfully!');
    
  } catch (error) {
    logger.error('💥 Transaction tests failed:', error);
    process.exit(1);
  } finally {
    // Cleanup
    await cleanupTestData();
    
    // Close database connection
    await mongoose.connection.close();
    logger.info('✅ Database connection closed');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch((error) => {
    logger.error('Fatal error:', error);
    process.exit(1);
  });
}

export {
  testBasicTransaction,
  testTransactionRollback,
  testBatchOperations,
  testConditionalTransaction,
  testValidationAndExecution,
  cleanupTestData,
  runAllTests
};
