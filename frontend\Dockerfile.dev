# Development Dockerfile for CryptoYield Frontend
# Optimized for live code editing and hot-reload

FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for development
RUN apk add --no-cache \
    curl \
    bash \
    git

# Copy package files first for better caching
COPY package*.json ./

# Install all dependencies (including dev dependencies) as root
RUN npm ci

# Copy configuration files
COPY vite.config.ts ./
COPY tsconfig*.json ./
COPY eslint.config.js ./
COPY index.html ./

# Create necessary directories
RUN mkdir -p src public dist

# Set proper permissions for the node user
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Expose the port
EXPOSE 3003

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3003 || exit 1

# Default command for development with hot-reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3003"]
