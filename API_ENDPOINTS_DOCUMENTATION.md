# API Endpoints Documentation

## System Configuration API Routes

### Overview
The system configuration API has been restructured to provide better security and separation of concerns between admin and user access.

---

## 🔐 Admin Routes (Admin Authentication Required)

### Base URL: `/api/admin/system`

#### 1. Get System Configuration (Admin)
- **Endpoint**: `GET /api/admin/system/config`
- **Authentication**: Admin token required
- **Description**: Get complete system configuration with all admin fields
- **Response**: Full system configuration including sensitive data

```json
{
  "success": true,
  "data": {
    "siteName": "Shipping Finance",
    "siteDescription": "Secure Crypto Investment Platform",
    "maintenanceMode": false,
    "maintenanceMessage": "System under maintenance",
    "commissionRate": 1.0,
    "referralRate": 3.0,
    "minimumDeposit": 100,
    "minimumWithdrawal": 50,
    "withdrawalsEnabled": true,
    "depositsEnabled": true,
    "emailNotifications": "all",
    "cryptoAddresses": [...],
    "supportedCurrencies": ["BTC", "ETH", "USDT"]
  }
}
```

#### 2. Update System Configuration (Admin)
- **Endpoint**: `PUT /api/admin/system/config`
- **Authentication**: Admin token required
- **Description**: Update system configuration
- **Body**: System configuration object

#### 3. Get Crypto Addresses (Admin)
- **Endpoint**: `GET /api/admin/system/crypto-addresses`
- **Authentication**: Admin token required
- **Description**: Get all crypto addresses configuration

#### 4. Update Crypto Addresses (Admin)
- **Endpoint**: `PUT /api/admin/system/crypto-addresses/:currency`
- **Authentication**: Admin token required
- **Description**: Update crypto addresses for specific currency

---

## 👤 User Routes (User Authentication Required)

### Base URL: `/api/user-system`

#### 1. Get User System Configuration
- **Endpoint**: `GET /api/user-system/config`
- **Authentication**: User token required
- **Description**: Get system configuration filtered for user access
- **Response**: User-safe system configuration

```json
{
  "success": true,
  "data": {
    "siteName": "Shipping Finance",
    "siteDescription": "Secure Crypto Investment Platform",
    "maintenanceMode": false,
    "maintenanceMessage": "System under maintenance",
    "commissionRate": 1.0,
    "referralRate": 3.0,
    "minimumDeposit": 100,
    "minimumWithdrawal": 50,
    "withdrawalsEnabled": true,
    "depositsEnabled": true,
    "supportedCurrencies": ["BTC", "ETH", "USDT"],
    "cryptoAddresses": [
      {
        "currency": "BTC",
        "enabled": true,
        "network": "bitcoin",
        "hasAddresses": true
      }
    ]
  }
}
```

#### 2. Get Commission Rates
- **Endpoint**: `GET /api/user-system/commission-rates`
- **Authentication**: None required
- **Description**: Get commission rates for calculation purposes

```json
{
  "success": true,
  "data": {
    "commissionRate": 1.0,
    "referralRate": 3.0,
    "minimumDeposit": 100,
    "minimumWithdrawal": 50
  }
}
```

#### 3. Get Transaction Settings
- **Endpoint**: `GET /api/user-system/transaction-settings`
- **Authentication**: None required
- **Description**: Get transaction-related settings

```json
{
  "success": true,
  "data": {
    "depositsEnabled": true,
    "withdrawalsEnabled": true,
    "minimumDeposit": 100,
    "minimumWithdrawal": 50,
    "supportedCurrencies": ["BTC", "ETH", "USDT"]
  }
}
```

---

## 🌐 Public Routes (No Authentication Required)

### Base URL: `/api/user-system`

#### 1. Get Public System Information
- **Endpoint**: `GET /api/user-system/public`
- **Authentication**: None required
- **Description**: Get basic public system information

```json
{
  "success": true,
  "data": {
    "siteName": "Shipping Finance",
    "siteDescription": "Secure Crypto Investment Platform",
    "maintenanceMode": false,
    "maintenanceMessage": "System under maintenance",
    "supportedCurrencies": ["BTC", "ETH", "USDT"]
  }
}
```

### Base URL: `/api/system`

#### 2. Get Maintenance Status
- **Endpoint**: `GET /api/system/maintenance-status`
- **Authentication**: None required
- **Description**: Get current maintenance status

```json
{
  "success": true,
  "data": {
    "maintenance": false,
    "message": "System under maintenance",
    "siteName": "Shipping Finance",
    "estimatedTime": "We will be back soon"
  }
}
```

---

## 🔄 Migration Guide

### For Frontend Developers:

#### Old Usage:
```typescript
// OLD - Direct system config access
const response = await axios.get('/api/system/config');
```

#### New Usage:
```typescript
// NEW - User-specific system config
const response = await userSystemService.getSystemConfig();
// OR
const response = await axios.get('/api/user-system/config');

// For public info
const response = await userSystemService.getPublicSystemInfo();
// OR
const response = await axios.get('/api/user-system/public');
```

#### Admin Usage:
```typescript
// Admin system config access
const response = await adminApiService.getSystemConfig();
// OR
const response = await axios.get('/api/admin/system/config');
```

### Security Benefits:

1. **🔐 Admin-only modifications**: System configuration can only be modified by admin users
2. **👤 User-filtered data**: Users only see relevant, non-sensitive information
3. **🌐 Public endpoints**: Basic information available without authentication
4. **🛡️ Data protection**: Sensitive admin data is not exposed to regular users

### Backward Compatibility:

- **Legacy endpoint** `/api/system/config/public` is maintained for backward compatibility
- **Deprecation notice**: Will be removed in future versions
- **Migration path**: Use `/api/user-system/public` instead
