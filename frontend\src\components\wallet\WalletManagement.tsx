import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon,
  useDisclosure,
  useToast,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Badge,
  Flex,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  Divider
} from '@chakra-ui/react';
import {
  FaWallet,
  FaPlus,
  FaEdit,
  FaTrash,
  FaCheck,
  FaExclamationTriangle,
  FaStar,
  FaRegStar
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import AddWalletModal from './AddWalletModal';
import EditWalletModal from './EditWalletModal';
import VerifyWalletModal from './VerifyWalletModal';
import DeleteWalletModal from './DeleteWalletModal';
import { walletManagementService } from '../../services/walletManagementService';
import { getCryptoColor } from '../../utils/cryptoUtils';
import { getCryptoIcon } from '../../utils/cryptoIconMap';
import systemConfigService from '../../services/systemConfigService';

interface WithdrawalAddress {
  _id: string;
  currency: string;
  address: string;
  formattedAddress: string;
  label: string;
  network: string;
  isDefault: boolean;
  isActive: boolean;
  isVerified: boolean;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const WalletManagement: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  
  const [addresses, setAddresses] = useState<WithdrawalAddress[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAddress, setSelectedAddress] = useState<WithdrawalAddress | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  const [currencies, setCurrencies] = useState<string[]>([]);
  const [loadingCurrencies, setLoadingCurrencies] = useState(true);

  // Modal states
  const { isOpen: isAddOpen, onOpen: onAddOpen, onClose: onAddClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isVerifyOpen, onOpen: onVerifyOpen, onClose: onVerifyClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  // Fetch withdrawal addresses
  const fetchAddresses = async (currency?: string) => {
    try {
      setLoading(true);
      const response = await walletManagementService.getUserAddresses(currency);
      setAddresses(response.data);
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to fetch wallet addresses'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAddresses(selectedCurrency);
  }, [selectedCurrency]);

  useEffect(() => {
    loadSupportedCurrencies();
  }, []);

  // Handle add wallet
  const handleAddWallet = () => {
    onAddOpen();
  };

  // Handle edit wallet
  const handleEditWallet = (address: WithdrawalAddress) => {
    setSelectedAddress(address);
    onEditOpen();
  };

  // Handle verify wallet
  const handleVerifyWallet = (address: WithdrawalAddress) => {
    setSelectedAddress(address);
    onVerifyOpen();
  };

  // Handle delete wallet
  const handleDeleteWallet = (address: WithdrawalAddress) => {
    setSelectedAddress(address);
    onDeleteOpen();
  };

  // Handle set default
  const handleSetDefault = async (addressId: string) => {
    try {
      await walletManagementService.setDefaultAddress(addressId);
      toast({
        title: t('Success'),
        description: t('Default wallet address updated'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      fetchAddresses(selectedCurrency);
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to set default address'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Load supported currencies from system config
  const loadSupportedCurrencies = async () => {
    setLoadingCurrencies(true);
    try {
      console.log('🔧 Loading supported currencies from system config...');
      const supportedCurrencies = await systemConfigService.getSupportedCurrencySymbols();
      setCurrencies(supportedCurrencies);
      console.log('✅ Supported currencies loaded:', supportedCurrencies);
    } catch (error) {
      console.error('❌ Failed to load supported currencies:', error);
      // Fallback to default currencies
      const fallbackCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT'];
      setCurrencies(fallbackCurrencies);
      console.log('📦 Using fallback currencies:', fallbackCurrencies);
    } finally {
      setLoadingCurrencies(false);
    }
  };

  if (loading) {
    return (
      <Center h="400px">
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" />
          <Text>{t('Loading wallet addresses...')}</Text>
        </VStack>
      </Center>
    );
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
          <VStack align="start" spacing={1}>
            <Heading size="lg" color="gray.800">
              <Icon as={FaWallet} mr={3} />
              {t('Wallet Management')}
            </Heading>
            <Text color="gray.600">
              {t('Manage your cryptocurrency withdrawal addresses')}
            </Text>
          </VStack>
          <Button
            colorScheme="blue"
            leftIcon={<FaPlus />}
            onClick={handleAddWallet}
            size="lg"
          >
            {t('Add Wallet Address')}
          </Button>
        </Flex>

        {/* Currency Filter */}
        <Card>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between" align="center">
                <Text fontWeight="semibold">{t('Filter by Currency')}</Text>
                {loadingCurrencies && (
                  <HStack spacing={2}>
                    <Spinner size="sm" />
                    <Text fontSize="sm" color="gray.600">
                      {t('Loading from system config...')}
                    </Text>
                  </HStack>
                )}
              </HStack>
              <HStack spacing={2} wrap="wrap">
                <Button
                  size="sm"
                  variant={selectedCurrency === '' ? 'solid' : 'outline'}
                  colorScheme="blue"
                  onClick={() => setSelectedCurrency('')}
                  isDisabled={loadingCurrencies}
                >
                  {t('All')}
                </Button>
                {currencies.map((currency) => (
                  <Button
                    key={currency}
                    size="sm"
                    variant={selectedCurrency === currency ? 'solid' : 'outline'}
                    colorScheme="blue"
                    leftIcon={<Icon as={getCryptoIcon(currency)} />}
                    onClick={() => setSelectedCurrency(currency)}
                    isDisabled={loadingCurrencies}
                  >
                    {currency}
                  </Button>
                ))}
                {loadingCurrencies && currencies.length === 0 && (
                  <Text fontSize="sm" color="gray.500">
                    {t('Loading supported currencies...')}
                  </Text>
                )}
              </HStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Addresses Grid */}
        {addresses.length === 0 ? (
          <Card>
            <CardBody>
              <Center py={12}>
                <VStack spacing={4}>
                  <Icon as={FaWallet} size="3xl" color="gray.400" />
                  <Text fontSize="lg" color="gray.600">
                    {t('No wallet addresses found')}
                  </Text>
                  <Text color="gray.500" textAlign="center">
                    {t('Add your first withdrawal address to get started')}
                  </Text>
                  <Button
                    colorScheme="blue"
                    leftIcon={<FaPlus />}
                    onClick={handleAddWallet}
                  >
                    {t('Add Wallet Address')}
                  </Button>
                </VStack>
              </Center>
            </CardBody>
          </Card>
        ) : (
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            {addresses.map((address) => (
              <Card
                key={address._id}
                borderWidth={address.isDefault ? 2 : 1}
                borderColor={address.isDefault ? 'blue.500' : 'gray.200'}
                position="relative"
                _hover={{ shadow: 'md' }}
                transition="all 0.2s"
              >
                <CardHeader pb={2}>
                  <Flex justify="space-between" align="center">
                    <HStack spacing={3}>
                      <Icon
                        as={getCryptoIcon(address.currency)}
                        color={getCryptoColor(address.currency)}
                        boxSize={6}
                      />
                      <VStack align="start" spacing={0}>
                        <Text fontWeight="bold" fontSize="lg">
                          {address.currency}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          {address.label}
                        </Text>
                      </VStack>
                    </HStack>
                    <VStack spacing={1}>
                      {address.isDefault && (
                        <Badge colorScheme="blue" variant="solid">
                          <Icon as={FaStar} mr={1} />
                          {t('Default')}
                        </Badge>
                      )}
                      <Badge
                        colorScheme={address.isVerified ? 'green' : 'orange'}
                        variant="subtle"
                      >
                        {address.isVerified ? t('Verified') : t('Unverified')}
                      </Badge>
                    </VStack>
                  </Flex>
                </CardHeader>

                <CardBody pt={0}>
                  <VStack spacing={3} align="stretch">
                    <Box>
                      <Text fontSize="sm" color="gray.600" mb={1}>
                        {t('Address')}
                      </Text>
                      <Text
                        fontSize="sm"
                        fontFamily="mono"
                        bg="gray.50"
                        p={2}
                        borderRadius="md"
                        wordBreak="break-all"
                      >
                        {address.formattedAddress}
                      </Text>
                    </Box>

                    <Box>
                      <Text fontSize="sm" color="gray.600" mb={1}>
                        {t('Network')}
                      </Text>
                      <Badge variant="outline">{address.network}</Badge>
                    </Box>

                    {address.lastUsed && (
                      <Box>
                        <Text fontSize="sm" color="gray.600" mb={1}>
                          {t('Last Used')}
                        </Text>
                        <Text fontSize="sm">
                          {new Date(address.lastUsed).toLocaleDateString()}
                        </Text>
                      </Box>
                    )}

                    <Divider />

                    {/* Action Buttons */}
                    <VStack spacing={2}>
                      {!address.isVerified && (
                        <Button
                          size="sm"
                          colorScheme="orange"
                          variant="outline"
                          leftIcon={<FaCheck />}
                          onClick={() => handleVerifyWallet(address)}
                          w="full"
                        >
                          {t('Verify')}
                        </Button>
                      )}

                      <HStack spacing={2} w="full">
                        <Button
                          size="sm"
                          variant="outline"
                          leftIcon={<FaEdit />}
                          onClick={() => handleEditWallet(address)}
                          flex={1}
                        >
                          {t('Edit')}
                        </Button>
                        
                        {!address.isDefault && address.isVerified && (
                          <Button
                            size="sm"
                            variant="outline"
                            leftIcon={<FaStar />}
                            onClick={() => handleSetDefault(address._id)}
                            flex={1}
                          >
                            {t('Set Default')}
                          </Button>
                        )}
                        
                        <Button
                          size="sm"
                          colorScheme="red"
                          variant="outline"
                          leftIcon={<FaTrash />}
                          onClick={() => handleDeleteWallet(address)}
                        >
                          {t('Delete')}
                        </Button>
                      </HStack>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        )}

        {/* Info Alert */}
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <Text fontWeight="semibold">{t('Important Information')}</Text>
            <Text fontSize="sm">
              {t('Only verified addresses can be used for withdrawals. You can have up to 10 addresses per currency.')}
            </Text>
          </Box>
        </Alert>
      </VStack>

      {/* Modals */}
      <AddWalletModal
        isOpen={isAddOpen}
        onClose={onAddClose}
        onSuccess={() => {
          fetchAddresses(selectedCurrency);
          onAddClose();
        }}
      />

      <EditWalletModal
        isOpen={isEditOpen}
        onClose={onEditClose}
        address={selectedAddress}
        onSuccess={() => {
          fetchAddresses(selectedCurrency);
          onEditClose();
        }}
      />

      <VerifyWalletModal
        isOpen={isVerifyOpen}
        onClose={onVerifyClose}
        address={selectedAddress}
        onSuccess={() => {
          fetchAddresses(selectedCurrency);
          onVerifyClose();
        }}
      />

      <DeleteWalletModal
        isOpen={isDeleteOpen}
        onClose={onDeleteClose}
        address={selectedAddress}
        onSuccess={() => {
          fetchAddresses(selectedCurrency);
          onDeleteClose();
        }}
      />
    </Container>
  );
};

export default WalletManagement;
