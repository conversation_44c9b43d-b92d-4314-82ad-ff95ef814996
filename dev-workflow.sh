#!/bin/bash

# CryptoYield Development Workflow Helper
# Quick commands for common development tasks

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_header() { echo -e "${PURPLE}[DEV]${NC} $1"; }

show_help() {
    echo "🚀 CryptoYield Development Workflow Helper"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       Start the development environment"
    echo "  stop        Stop the development environment"
    echo "  restart     Restart the development environment"
    echo "  logs        Show backend logs (follow mode)"
    echo "  logs-all    Show all service logs"
    echo "  status      Show container status"
    echo "  shell       Open shell in backend container"
    echo "  mongo       Open MongoDB shell"
    echo "  redis       Open Redis CLI"
    echo "  build       Rebuild backend container"
    echo "  clean       Clean up containers and volumes"
    echo "  test        Run verification tests"
    echo "  health      Check service health"
    echo "  setup       Run initial setup"
    echo ""
    echo "Examples:"
    echo "  $0 start     # Start development environment"
    echo "  $0 logs      # Follow backend logs"
    echo "  $0 mongo     # Open MongoDB shell"
    echo ""
}

case "$1" in
    "start")
        print_header "Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d
        print_success "Development environment started"
        print_status "Backend: http://localhost:5000"
        print_status "Mongo Express: http://localhost:8081"
        ;;
    
    "stop")
        print_header "Stopping development environment..."
        docker-compose -f docker-compose.dev.yml down
        print_success "Development environment stopped"
        ;;
    
    "restart")
        print_header "Restarting development environment..."
        docker-compose -f docker-compose.dev.yml restart
        print_success "Development environment restarted"
        ;;
    
    "logs")
        print_header "Following backend logs..."
        docker-compose -f docker-compose.dev.yml logs -f backend
        ;;
    
    "logs-all")
        print_header "Following all service logs..."
        docker-compose -f docker-compose.dev.yml logs -f
        ;;
    
    "status")
        print_header "Container status:"
        docker-compose -f docker-compose.dev.yml ps
        ;;
    
    "shell")
        print_header "Opening shell in backend container..."
        docker exec -it cryptoyield-backend-dev /bin/bash
        ;;
    
    "mongo")
        print_header "Opening MongoDB shell..."
        docker exec -it cryptoyield-mongodb-dev mongosh -u cryptoyield_admin -p secure_password123 --authenticationDatabase admin
        ;;
    
    "redis")
        print_header "Opening Redis CLI..."
        docker exec -it cryptoyield-redis-dev redis-cli
        ;;
    
    "build")
        print_header "Rebuilding backend container..."
        docker-compose -f docker-compose.dev.yml build --no-cache backend
        print_success "Backend container rebuilt"
        ;;
    
    "clean")
        print_header "Cleaning up containers and volumes..."
        docker-compose -f docker-compose.dev.yml down -v
        docker system prune -f
        print_success "Cleanup completed"
        ;;
    
    "test")
        print_header "Running verification tests..."
        ./verify-dev-environment.sh
        ;;
    
    "health")
        print_header "Checking service health..."
        echo "🔍 Backend Health:"
        curl -s http://localhost:5000/api/health || echo "❌ Backend not accessible"
        echo ""
        echo "🔍 MongoDB Health:"
        docker exec cryptoyield-mongodb-dev mongosh --eval "db.adminCommand('ping')" 2>/dev/null || echo "❌ MongoDB not accessible"
        echo "🔍 Redis Health:"
        docker exec cryptoyield-redis-dev redis-cli ping 2>/dev/null || echo "❌ Redis not accessible"
        echo "🔍 Mongo Express Health:"
        curl -s -o /dev/null -w "HTTP %{http_code}\n" http://localhost:8081 || echo "❌ Mongo Express not accessible"
        ;;
    
    "setup")
        print_header "Running initial setup..."
        ./setup-dev-environment.sh
        ;;
    
    "")
        show_help
        ;;
    
    *)
        print_warning "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
