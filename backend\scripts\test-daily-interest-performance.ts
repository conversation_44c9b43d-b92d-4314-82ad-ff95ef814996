#!/usr/bin/env npx ts-node

/**
 * Performance-Optimized Daily Interest Payment Test Script
 * 
 * This script is specifically designed to test performance with large numbers
 * of investment packages (e.g., 1000+ packages) and includes optimizations
 * for batch processing and performance monitoring.
 * 
 * Usage: npx ts-node scripts/test-daily-interest-performance.ts
 */

// Load environment variables first
require('dotenv').config({ path: require('path').join(__dirname, '../.env.docker') });

import mongoose from 'mongoose';

// Import models and services
require('../src/models/userModel');
require('../src/models/walletModel');
require('../src/models/investmentPackageModel');
require('../src/models/transactionModel');
require('../src/models/paymentHistoryModel');
require('../src/models/auditTrailModel');

const interestCalculationService = require('../src/services/interestCalculationService').default;
const User = require('../src/models/userModel').default;
const Wallet = require('../src/models/walletModel').default;
const InvestmentPackage = require('../src/models/investmentPackageModel').default;
const Transaction = require('../src/models/transactionModel').default;

// Performance test configuration
interface PerformanceConfig {
  createTestPackages: boolean;
  numberOfTestPackages: number;
  batchSize: number;
  maxConcurrentBatches: number;
  enableDetailedLogging: boolean;
  cleanupAfterTest: boolean;
  testAmounts: number[];
  testCurrencies: string[];
}

const PERFORMANCE_CONFIG: PerformanceConfig = {
  createTestPackages: true,
  numberOfTestPackages: 100, // Start with 100, can be increased
  batchSize: 50, // Larger batch size for better performance
  maxConcurrentBatches: 3, // Process multiple batches concurrently
  enableDetailedLogging: false, // Disable detailed logging for performance
  cleanupAfterTest: false,
  testAmounts: [100, 500, 1000, 2000, 5000],
  testCurrencies: ['USDT', 'BTC', 'ETH', 'BNB', 'ADA']
};

// Performance metrics
interface PerformanceMetrics {
  totalPackages: number;
  totalDuration: number;
  avgTimePerPackage: number;
  packagesPerSecond: number;
  batchProcessingTime: number[];
  memoryUsage: NodeJS.MemoryUsage;
  successRate: number;
  throughput: number;
}

// Store test data for cleanup
const testDataIds = {
  users: [] as string[],
  wallets: [] as string[],
  packages: [] as string[],
  transactions: [] as string[]
};

/**
 * Connect to database with optimized settings
 */
async function connectDatabase(): Promise<void> {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log('🔌 Connecting to database with performance optimizations...');
    
    await mongoose.connect(mongoUri, {
      maxPoolSize: 20, // Increase connection pool
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false
    });
    
    console.log('✅ Database connection established with optimized settings');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

/**
 * Create test packages in bulk for performance testing
 */
async function createBulkTestPackages(count: number): Promise<void> {
  console.log(`\n🔧 Creating ${count} test packages for performance testing...`);
  
  const startTime = Date.now();
  const batchSize = 50; // Create in batches
  
  for (let i = 0; i < count; i += batchSize) {
    const currentBatchSize = Math.min(batchSize, count - i);
    const batchStartTime = Date.now();
    
    // Create users in batch
    const users = [];
    for (let j = 0; j < currentBatchSize; j++) {
      users.push({
        email: `perf-test-${Date.now()}-${i + j}@example.com`,
        password: 'Test123!@#',
        firstName: 'Perf',
        lastName: `User${i + j}`,
        emailVerified: true,
        kycVerified: true,
        referralCode: Math.random().toString(36).substring(7).toUpperCase()
      });
    }
    
    const createdUsers = await User.insertMany(users);
    testDataIds.users.push(...createdUsers.map((u: any) => u._id.toString()));
    
    // Create wallets in batch
    const wallets = createdUsers.map((user: any) => ({
      userId: user._id,
      assets: PERFORMANCE_CONFIG.testCurrencies.map(currency => ({
        symbol: currency,
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'interest'
      })),
      totalCommissionEarned: 0,
      totalInterestEarned: 0
    }));
    
    const createdWallets = await Wallet.insertMany(wallets);
    testDataIds.wallets.push(...createdWallets.map((w: any) => w._id.toString()));
    
    // Create transactions and packages
    const transactions = [];
    const packages = [];
    
    for (let j = 0; j < currentBatchSize; j++) {
      const user = createdUsers[j];
      const wallet = createdWallets[j];
      const amount = PERFORMANCE_CONFIG.testAmounts[j % PERFORMANCE_CONFIG.testAmounts.length];
      const currency = PERFORMANCE_CONFIG.testCurrencies[j % PERFORMANCE_CONFIG.testCurrencies.length];
      
      // Transaction
      const transaction = {
        userId: user._id,
        walletId: wallet._id,
        type: 'deposit',
        asset: currency,
        amount,
        status: 'completed',
        description: `Performance test deposit - ${amount} ${currency}`
      };
      transactions.push(transaction);
    }
    
    const createdTransactions = await Transaction.insertMany(transactions);
    testDataIds.transactions.push(...createdTransactions.map((t: any) => t._id.toString()));
    
    // Create investment packages
    for (let j = 0; j < currentBatchSize; j++) {
      const user = createdUsers[j];
      const transaction = createdTransactions[j];
      const amount = PERFORMANCE_CONFIG.testAmounts[j % PERFORMANCE_CONFIG.testAmounts.length];
      const currency = PERFORMANCE_CONFIG.testCurrencies[j % PERFORMANCE_CONFIG.testCurrencies.length];
      
      // Random activation time (1-10 days ago)
      const daysAgo = Math.floor(Math.random() * 10) + 1;
      const activatedAt = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000);
      
      const packageData = {
        userId: user._id,
        transactionId: transaction._id,
        packageId: `PERF-${Date.now()}-${i + j}`,
        amount,
        currency,
        status: 'active',
        createdAt: activatedAt,
        activatedAt,
        dailyInterest: 0,
        totalEarned: 0,
        accumulatedInterest: 0,
        interestRate: 0.01, // 1% daily
        lastCalculatedAt: null,
        lastInterestDistribution: null,
        compoundEnabled: false,
        emergencyWithdrawFee: 0.05,
        packageHash: require('crypto').randomBytes(32).toString('hex'),
        activeDays: 0,
        totalDays: 365,
        roi: 0,
        autoCreated: false,
        minimumWithdrawalUSDT: 50,
        realTimeUSDTValue: 0,
        withdrawableInterest: 0,
        principalLocked: true
      };
      
      packages.push(packageData);
    }
    
    const createdPackages = await InvestmentPackage.insertMany(packages);
    testDataIds.packages.push(...createdPackages.map((p: any) => p._id.toString()));
    
    const batchDuration = Date.now() - batchStartTime;
    console.log(`✅ Batch ${Math.floor(i / batchSize) + 1}: Created ${currentBatchSize} packages in ${batchDuration}ms`);
  }
  
  const totalDuration = Date.now() - startTime;
  console.log(`✅ Successfully created ${count} test packages in ${totalDuration}ms`);
  console.log(`📊 Average creation time: ${(totalDuration / count).toFixed(2)}ms per package`);
}

/**
 * Get memory usage statistics
 */
function getMemoryUsage(): NodeJS.MemoryUsage {
  return process.memoryUsage();
}

/**
 * Format memory usage for display
 */
function formatMemoryUsage(memUsage: NodeJS.MemoryUsage): string {
  const formatBytes = (bytes: number) => (bytes / 1024 / 1024).toFixed(2) + ' MB';
  return `RSS: ${formatBytes(memUsage.rss)}, Heap Used: ${formatBytes(memUsage.heapUsed)}, Heap Total: ${formatBytes(memUsage.heapTotal)}`;
}

/**
 * Performance-optimized interest calculation test
 */
async function performanceTest(): Promise<PerformanceMetrics> {
  console.log('\n🚀 Starting performance-optimized interest calculation test...');
  
  const startTime = Date.now();
  const startMemory = getMemoryUsage();
  
  // Get all active packages
  const activePackages = await InvestmentPackage.find({
    status: 'active',
    activatedAt: { $ne: null }
  });
  
  console.log(`📦 Found ${activePackages.length} active packages for performance testing`);
  
  if (activePackages.length === 0) {
    throw new Error('No active packages found for testing');
  }
  
  // Run the optimized interest calculation
  const summary = await interestCalculationService.processAllActivePackages();
  
  const endTime = Date.now();
  const endMemory = getMemoryUsage();
  const totalDuration = endTime - startTime;
  
  // Calculate performance metrics
  const metrics: PerformanceMetrics = {
    totalPackages: summary.totalPackages,
    totalDuration,
    avgTimePerPackage: totalDuration / summary.totalPackages,
    packagesPerSecond: (summary.totalPackages / totalDuration) * 1000,
    batchProcessingTime: [], // Would need to be collected from service
    memoryUsage: endMemory,
    successRate: (summary.successfulCalculations / summary.totalPackages) * 100,
    throughput: summary.totalPackages / (totalDuration / 1000) // packages per second
  };
  
  // Display performance results
  console.log('\n📊 PERFORMANCE TEST RESULTS');
  console.log('============================');
  console.log(`📦 Total packages processed: ${metrics.totalPackages}`);
  console.log(`✅ Successful calculations: ${summary.successfulCalculations}`);
  console.log(`❌ Failed calculations: ${summary.failedCalculations}`);
  console.log(`💰 Total interest paid: ${summary.totalInterestPaid.toFixed(6)}`);
  console.log(`⏱️  Total processing time: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
  console.log(`📈 Success rate: ${metrics.successRate.toFixed(2)}%`);
  console.log(`⚡ Average time per package: ${metrics.avgTimePerPackage.toFixed(2)}ms`);
  console.log(`🚀 Throughput: ${metrics.throughput.toFixed(2)} packages/second`);
  console.log(`💾 Memory usage: ${formatMemoryUsage(endMemory)}`);
  
  // Performance analysis
  console.log('\n📈 PERFORMANCE ANALYSIS');
  console.log('=======================');
  
  if (metrics.avgTimePerPackage < 100) {
    console.log('🟢 EXCELLENT: Very fast processing (<100ms per package)');
  } else if (metrics.avgTimePerPackage < 500) {
    console.log('🟡 GOOD: Acceptable processing time (100-500ms per package)');
  } else if (metrics.avgTimePerPackage < 1000) {
    console.log('🟠 MODERATE: Slow processing (500-1000ms per package)');
  } else {
    console.log('🔴 POOR: Very slow processing (>1000ms per package)');
  }
  
  // Estimate time for different scales
  console.log('\n⏰ ESTIMATED PROCESSING TIMES FOR DIFFERENT SCALES:');
  console.log('===================================================');
  const scales = [100, 500, 1000, 5000, 10000];
  scales.forEach(scale => {
    const estimatedTime = (scale * metrics.avgTimePerPackage) / 1000;
    console.log(`📊 ${scale.toLocaleString()} packages: ~${estimatedTime.toFixed(1)}s (${(estimatedTime / 60).toFixed(1)} minutes)`);
  });
  
  return metrics;
}

/**
 * Cleanup test data
 */
async function cleanupTestData(): Promise<void> {
  if (!PERFORMANCE_CONFIG.cleanupAfterTest) {
    console.log('\n🔧 Cleanup disabled - test data will remain in database');
    return;
  }

  console.log('\n🧹 Cleaning up performance test data...');

  try {
    const startTime = Date.now();

    // Delete in reverse order to maintain referential integrity
    if (testDataIds.packages.length > 0) {
      const deletedPackages = await InvestmentPackage.deleteMany({
        packageId: { $regex: /^PERF-/ }
      });
      console.log(`✅ Deleted ${deletedPackages.deletedCount} test packages`);
    }

    if (testDataIds.transactions.length > 0) {
      const deletedTransactions = await Transaction.deleteMany({
        description: { $regex: /Performance test deposit/ }
      });
      console.log(`✅ Deleted ${deletedTransactions.deletedCount} test transactions`);
    }

    if (testDataIds.wallets.length > 0) {
      const deletedWallets = await Wallet.deleteMany({
        _id: { $in: testDataIds.wallets.map(id => new mongoose.Types.ObjectId(id)) }
      });
      console.log(`✅ Deleted ${deletedWallets.deletedCount} test wallets`);
    }

    if (testDataIds.users.length > 0) {
      const deletedUsers = await User.deleteMany({
        email: { $regex: /^perf-test-/ }
      });
      console.log(`✅ Deleted ${deletedUsers.deletedCount} test users`);
    }

    const cleanupTime = Date.now() - startTime;
    console.log(`✅ Cleanup completed in ${cleanupTime}ms`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

/**
 * Main performance test execution
 */
async function main(): Promise<void> {
  console.log('🧪 DAILY INTEREST PAYMENT PERFORMANCE TEST');
  console.log('==========================================');
  console.log('This script tests the performance of daily interest calculation');
  console.log('with large numbers of investment packages.\n');

  // Display configuration
  console.log('⚙️  PERFORMANCE TEST CONFIGURATION');
  console.log('===================================');
  console.log(`Create test packages: ${PERFORMANCE_CONFIG.createTestPackages ? '✅ Yes' : '❌ No'}`);
  console.log(`Number of test packages: ${PERFORMANCE_CONFIG.numberOfTestPackages.toLocaleString()}`);
  console.log(`Batch size: ${PERFORMANCE_CONFIG.batchSize}`);
  console.log(`Max concurrent batches: ${PERFORMANCE_CONFIG.maxConcurrentBatches}`);
  console.log(`Detailed logging: ${PERFORMANCE_CONFIG.enableDetailedLogging ? '✅ Yes' : '❌ No'}`);
  console.log(`Cleanup after test: ${PERFORMANCE_CONFIG.cleanupAfterTest ? '✅ Yes' : '❌ No'}`);
  console.log(`Database URI: ${process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield'}`);

  try {
    // Connect to database
    await connectDatabase();

    // Check existing packages
    const existingPackages = await InvestmentPackage.find({
      status: 'active',
      activatedAt: { $ne: null }
    });

    console.log(`\n📊 Found ${existingPackages.length} existing active packages`);

    // Create test packages if needed
    if (PERFORMANCE_CONFIG.createTestPackages) {
      if (existingPackages.length < PERFORMANCE_CONFIG.numberOfTestPackages) {
        const packagesToCreate = PERFORMANCE_CONFIG.numberOfTestPackages - existingPackages.length;
        await createBulkTestPackages(packagesToCreate);
      } else {
        console.log(`✅ Sufficient packages exist (${existingPackages.length} >= ${PERFORMANCE_CONFIG.numberOfTestPackages})`);
      }
    }

    // Run performance test
    const metrics = await performanceTest();

    // Cleanup if configured
    await cleanupTestData();

    // Final summary
    console.log('\n🎉 PERFORMANCE TEST COMPLETED!');
    console.log('==============================');
    console.log(`📊 Processed ${metrics.totalPackages.toLocaleString()} packages`);
    console.log(`⏱️  Total time: ${(metrics.totalDuration / 1000).toFixed(2)}s`);
    console.log(`🚀 Throughput: ${metrics.throughput.toFixed(2)} packages/second`);
    console.log(`📈 Success rate: ${metrics.successRate.toFixed(2)}%`);

    // Performance recommendations
    console.log('\n💡 PERFORMANCE RECOMMENDATIONS');
    console.log('===============================');

    if (metrics.throughput < 5) {
      console.log('🔴 CRITICAL: Very low throughput detected!');
      console.log('   - Consider increasing batch size');
      console.log('   - Optimize database queries');
      console.log('   - Check database connection pool settings');
      console.log('   - Consider parallel processing');
    } else if (metrics.throughput < 20) {
      console.log('🟡 WARNING: Low throughput detected');
      console.log('   - Consider increasing batch size');
      console.log('   - Optimize transaction handling');
    } else {
      console.log('🟢 GOOD: Acceptable throughput for production use');
    }

    // Estimate for 1000 packages
    const timeFor1000 = (1000 / metrics.throughput);
    console.log(`\n📊 Estimated time for 1000 packages: ${timeFor1000.toFixed(1)}s (${(timeFor1000 / 60).toFixed(1)} minutes)`);

  } catch (error) {
    console.error('\n💥 Performance test failed:', error);

    // Try cleanup even if test failed
    if (PERFORMANCE_CONFIG.cleanupAfterTest) {
      await cleanupTestData();
    }

    process.exit(1);
  } finally {
    // Close database connection
    try {
      await mongoose.connection.close();
      console.log('✅ Database connection closed');
    } catch (error) {
      console.error('❌ Error closing database:', error);
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Received SIGINT. Shutting down...');
  try {
    if (PERFORMANCE_CONFIG.cleanupAfterTest) {
      await cleanupTestData();
    }
    await mongoose.connection.close();
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Execute main function
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
