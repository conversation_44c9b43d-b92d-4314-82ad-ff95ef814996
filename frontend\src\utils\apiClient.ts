import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { logger } from './logger';
import { errorTracking } from './errorTracking';

interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
}

interface RetryConfig {
  maxRetries: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffFactor: number;
}

class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(private config: CircuitBreakerConfig) {}

  async executeRequest<T>(request: () => Promise<T>): Promise<T> {
    if (this.isOpen()) {
      if (this.shouldAttemptReset()) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await request();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private isOpen(): boolean {
    return this.state === 'OPEN';
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.config.resetTimeout;
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.config.failureThreshold) {
      this.state = 'OPEN';
      logger.warn('Circuit breaker opened', {
        failures: this.failures,
        lastFailureTime: this.lastFailureTime
      });
    }
  }

  // Public method to manually reset circuit breaker
  public reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
    logger.info('Circuit breaker manually reset');
  }

  // Public method to get current state
  public getState(): string {
    return this.state;
  }
}

class APIClient {
  private static instance: APIClient;
  private axiosInstance!: AxiosInstance;
  private circuitBreaker!: CircuitBreaker;
  private retryConfig!: RetryConfig;

  private constructor() {
    this.initializeAxios();
    this.setupCircuitBreaker();
    this.setupRetryConfig();
  }

  public static getInstance(): APIClient {
    if (!APIClient.instance) {
      APIClient.instance = new APIClient();
    }
    return APIClient.instance;
  }

  private initializeAxios(): void {
    const apiUrl = import.meta.env.VITE_API_URL || '/api';
    console.log('🔧 APIClient URL:', apiUrl);
    this.axiosInstance = axios.create({
      baseURL: apiUrl,
      timeout: 30000, // Increased timeout to 30 seconds
      headers: {
        'Content-Type': 'application/json'
      },
      withCredentials: true // Important for cookies and proxy
    });

    this.setupInterceptors();
  }

  private setupCircuitBreaker(): void {
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 10, // Increase threshold to be more tolerant
      resetTimeout: 15000 // Reduce reset timeout to 15 seconds for faster recovery
    });
  }

  private setupRetryConfig(): void {
    this.retryConfig = {
      maxRetries: 3,
      initialDelayMs: 1000,
      maxDelayMs: 10000,
      backoffFactor: 2
    };
  }

  private setupInterceptors(): void {
    // Request interceptor - no need to set Authorization header with cookie-based auth
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Ensure withCredentials is set for all requests
        config.withCredentials = true;
        return config;
      },
      (error) => {
        logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        if (error.response?.status === 401) {
          // Authentication failed - clear user data and redirect
          localStorage.removeItem('user');
          localStorage.removeItem('adminToken');

          // Only redirect if not already on login page
          if (window.location.pathname !== '/login' && window.location.pathname !== '/admin/login') {
            window.location.href = '/login';
          }
          return Promise.reject(error);
        }
        return Promise.reject(error);
      }
    );
  }

  private async executeWithRetry<T>(
    request: () => Promise<T>,
    retryCount: number = 0
  ): Promise<T> {
    try {
      return await this.circuitBreaker.executeRequest(request);
    } catch (error) {
      if (
        retryCount < this.retryConfig.maxRetries &&
        this.shouldRetry(error as AxiosError)
      ) {
        const delay = this.calculateRetryDelay(retryCount);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.executeWithRetry(request, retryCount + 1);
      }
      throw error;
    }
  }

  private shouldRetry(error: AxiosError): boolean {
    // Retry on network errors or 5xx server errors
    const isNetworkError = !error.response;
    const isServerError = error.response && error.response.status >= 500 && error.response.status < 600;

    // Log the error for debugging
    if (isNetworkError) {
      console.warn('Network error detected, will retry:', error.message);
    } else if (isServerError && error.response) {
      console.warn(`Server error detected (${error.response.status}), will retry:`, error.message);
    }

    return isNetworkError || Boolean(isServerError);
  }

  private calculateRetryDelay(retryCount: number): number {
    const delay = this.retryConfig.initialDelayMs *
      Math.pow(this.retryConfig.backoffFactor, retryCount);
    return Math.min(delay, this.retryConfig.maxDelayMs);
  }

  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.executeWithRetry(async () => {
      const response = await this.axiosInstance.get<T>(url, config);
      return response.data;
    });
  }

  public async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.executeWithRetry(async () => {
      try {
        const response = await this.axiosInstance.post<T>(url, data, config);
        return response.data;
      } catch (error: any) {
        // Add more context to the error
        if (error.message) {
          error.message = `POST ${url} failed: ${error.message}`;
        }

        // Log the error details for debugging
        if (process.env.NODE_ENV === 'development') {
          console.error('API POST request failed:', {
            url,
            data,
            error: {
              message: error.message,
              response: error.response?.data,
              status: error.response?.status
            }
          });
        }

        throw error;
      }
    });
  }

  public async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.executeWithRetry(async () => {
      const response = await this.axiosInstance.put<T>(url, data, config);
      return response.data;
    });
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.executeWithRetry(async () => {
      const response = await this.axiosInstance.delete<T>(url, config);
      return response.data;
    });
  }

  public async patch<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.executeWithRetry(async () => {
      const response = await this.axiosInstance.patch<T>(url, data, config);
      return response.data;
    });
  }

  // Cookie-based authentication - no need for token methods
  // Authentication is handled automatically via HTTP-only cookies

  // Public method to reset circuit breaker
  public resetCircuitBreaker(): void {
    this.circuitBreaker.reset();
  }

  // Public method to get circuit breaker state
  public getCircuitBreakerState(): string {
    return this.circuitBreaker.getState();
  }
}

export const apiClient = APIClient.getInstance();