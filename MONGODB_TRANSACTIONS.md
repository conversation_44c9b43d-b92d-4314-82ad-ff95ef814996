# MongoDB Transactions Configuration

This document describes the MongoDB transaction support configuration implemented in the CryptoYield backend.

## Overview

MongoDB transactions provide ACID properties for multi-document operations, ensuring data consistency across collections. This is crucial for financial operations like deposit approvals, withdrawals, and commission calculations.

## Configuration Changes

### 1. Docker Compose Updates

All Docker Compose files have been updated to support MongoDB replica sets:

- **docker-compose.yml** (Production): Uses MongoDB 6.0 with replica set `rs0` and keyfile authentication
- **docker-compose.dev.yml** (Development): Uses MongoDB 6.0 with replica set `rs0`
- **docker-compose.simple.yml** (Simple setup): Uses MongoDB 6.0 with replica set `rs0`

### 2. MongoDB Connection Configuration

Updated `backend/src/config/database.ts` with:

- Replica set connection options
- Transaction support validation
- Automatic retry logic for failed transactions
- Helper methods for transaction management

### 3. Environment Variables

Updated environment files to include replica set parameters:

```bash
# Example MongoDB URI with replica set
MONGO_URI=***********************************************************************************
```

## New Files and Components

### 1. MongoDB Initialization Scripts

- **mongo-init/01-replica-init.js**: Initializes the replica set
- **mongo-init/02-create-user.js**: Creates application user and indexes

### 2. Transaction Manager Utility

- **backend/src/utils/transactionManager.ts**: Provides helper functions for transaction management

### 3. Security Configuration

- **mongodb-keyfile/mongodb-keyfile**: Keyfile for replica set authentication (production)

### 4. Test Scripts

- **backend/src/scripts/testTransactions.ts**: Comprehensive transaction testing

## Usage Examples

### Basic Transaction

```typescript
import transactionManager from '../utils/transactionManager';

const result = await transactionManager.executeTransaction(async (session) => {
  // Create user
  const user = await User.create([userData], { session });
  
  // Create wallet
  const wallet = await Wallet.create([walletData], { session });
  
  return { user: user[0], wallet: wallet[0] };
});
```

### Transaction with Rollback

```typescript
const result = await transactionManager.executeWithRollback({
  execute: async (session) => {
    // Main operations
    const user = await User.create([userData], { session });
    const wallet = await Wallet.create([walletData], { session });
    return { user, wallet };
  },
  rollback: async (session, error) => {
    // Custom rollback logic if needed
    logger.error('Transaction failed, rolling back:', error);
  }
});
```

### Batch Operations

```typescript
const operations = [
  async (session) => User.create([user1Data], { session }),
  async (session) => User.create([user2Data], { session }),
  async (session) => User.create([user3Data], { session })
];

const results = await transactionManager.batchOperations(operations);
```

### Validation and Execution

```typescript
const validators = [
  async (session) => {
    // Check if user exists
    const existing = await User.findOne({ email }).session(session);
    if (existing) throw new Error('User already exists');
  }
];

const operation = async (session) => {
  return await User.create([userData], { session });
};

const result = await transactionManager.validateAndExecute(validators, operation);
```

## Database Class Methods

### Connection with Transaction Support

```typescript
import { db } from '../config/database';

// Start a session
const session = await db.startSession();

// Execute with automatic retry
const result = await db.withTransaction(async (session) => {
  // Your operations here
  return await someOperation(session);
});
```

## Testing

### Run Transaction Tests

```bash
# Local environment
npm run test:transactions:local

# Docker environment
npm run test:transactions:docker
```

### Test Coverage

The test script covers:

- Basic transaction functionality
- Transaction rollback
- Batch operations
- Conditional transactions
- Validation and execution
- Error handling and retry logic

## Deployment Steps

### 1. Development Environment

```bash
# Start MongoDB with replica set
docker-compose -f docker-compose.dev.yml up -d mongo

# Wait for replica set initialization
sleep 10

# Test transaction support
npm run test:transactions:docker
```

### 2. Production Environment

```bash
# Ensure keyfile exists
ls -la mongodb-keyfile/mongodb-keyfile

# Start services
docker-compose up -d mongodb

# Wait for replica set initialization
sleep 30

# Verify transaction support
docker-compose exec backend npm run test:transactions
```

## Monitoring and Troubleshooting

### Check Replica Set Status

```bash
# Connect to MongoDB
docker-compose exec mongodb mongosh -u root -p example

# Check replica set status
rs.status()

# Check if transactions are supported
db.adminCommand("isMaster")
```

### Common Issues

1. **Replica Set Not Initialized**
   - Check MongoDB logs: `docker-compose logs mongodb`
   - Manually initialize: Run `mongo-init/01-replica-init.js`

2. **Transaction Timeouts**
   - Increase `wtimeoutMS` in connection options
   - Check network connectivity between containers

3. **Keyfile Permissions**
   - Ensure keyfile has 600 permissions
   - Check container user permissions

### Performance Considerations

- Transactions have overhead compared to single operations
- Use transactions only when ACID properties are required
- Consider read preferences for better performance
- Monitor transaction duration and retry rates

## Migration from Non-Replica Set

If migrating from a standalone MongoDB instance:

1. **Backup existing data**
2. **Update Docker Compose configuration**
3. **Initialize replica set**
4. **Test transaction functionality**
5. **Update application code to use transactions**

## Security Notes

- Keyfile authentication is used in production
- Replica set members authenticate using the keyfile
- Regular rotation of keyfile is recommended
- Monitor access logs for unauthorized attempts

## Future Enhancements

- Multi-node replica set for high availability
- Read replicas for improved read performance
- Sharding for horizontal scaling
- Advanced monitoring and alerting
