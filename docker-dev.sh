#!/bin/bash

# CryptoYield Development Docker Management Script
# This script helps manage the development Docker environment with hot reload

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed or not in PATH."
        exit 1
    fi
}

# Function to start development environment
start_dev() {
    print_status "Starting CryptoYield development environment..."
    
    # Check prerequisites
    check_docker
    check_docker_compose
    
    # Create necessary directories
    print_status "Creating necessary directories..."
    mkdir -p backend/uploads
    mkdir -p backend/logs
    
    # Start services
    print_status "Starting Docker services with hot reload..."
    docker-compose -f docker-compose.dev.yml up -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check service status
    print_status "Checking service status..."
    docker-compose -f docker-compose.dev.yml ps
    
    print_success "Development environment started successfully!"
    print_status "Services available at:"
    echo "  🌐 Frontend:        http://localhost:3005"
    echo "  🔧 Backend API:     http://localhost:5001/api"
    echo "  🔍 API Health:      http://localhost:5001/api/health"
    echo "  🗄️  MongoDB:        mongodb://localhost:27017"
    echo "  📊 Mongo Express:   http://localhost:8081 (admin/admin123)"
    echo "  🔴 Redis:           redis://localhost:6379"
    echo ""
    print_status "Hot reload is enabled for both frontend and backend!"
    print_status "Edit files in ./frontend/src or ./backend/src and see changes instantly."
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping CryptoYield development environment..."
    
    check_docker_compose
    
    docker-compose -f docker-compose.dev.yml down
    
    print_success "Development environment stopped successfully!"
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting CryptoYield development environment..."
    stop_dev
    sleep 2
    start_dev
}

# Function to view logs
logs_dev() {
    check_docker_compose
    
    if [ -z "$2" ]; then
        print_status "Showing logs for all services..."
        docker-compose -f docker-compose.dev.yml logs -f
    else
        print_status "Showing logs for service: $2"
        docker-compose -f docker-compose.dev.yml logs -f "$2"
    fi
}

# Function to show status
status_dev() {
    check_docker_compose
    
    print_status "Development environment status:"
    docker-compose -f docker-compose.dev.yml ps
    
    echo ""
    print_status "Service health checks:"
    
    # Check backend health
    if curl -s http://localhost:5001/api/health > /dev/null 2>&1; then
        print_success "✅ Backend API is healthy"
    else
        print_warning "❌ Backend API is not responding"
    fi
    
    # Check frontend
    if curl -s http://localhost:3005 > /dev/null 2>&1; then
        print_success "✅ Frontend is healthy"
    else
        print_warning "❌ Frontend is not responding"
    fi
    
    # Check MongoDB
    if docker exec cryptoyield-mongodb-dev mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        print_success "✅ MongoDB is healthy"
    else
        print_warning "❌ MongoDB is not responding"
    fi
    
    # Check Redis
    if docker exec cryptoyield-redis-dev redis-cli ping > /dev/null 2>&1; then
        print_success "✅ Redis is healthy"
    else
        print_warning "❌ Redis is not responding"
    fi
}

# Function to clean up development environment
clean_dev() {
    print_warning "This will remove all containers, networks, and volumes for development environment."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up development environment..."
        
        check_docker_compose
        
        # Stop and remove containers, networks, and volumes
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans
        
        # Remove development images
        docker image prune -f --filter label=com.docker.compose.project=cryptoyield
        
        print_success "Development environment cleaned up successfully!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to rebuild services
rebuild_dev() {
    print_status "Rebuilding development services..."
    
    check_docker_compose
    
    # Stop services
    docker-compose -f docker-compose.dev.yml down
    
    # Rebuild images
    docker-compose -f docker-compose.dev.yml build --no-cache
    
    # Start services
    docker-compose -f docker-compose.dev.yml up -d
    
    print_success "Development services rebuilt and started!"
}

# Function to show help
show_help() {
    echo "CryptoYield Development Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start the development environment with hot reload"
    echo "  stop      Stop the development environment"
    echo "  restart   Restart the development environment"
    echo "  status    Show status of all services"
    echo "  logs      Show logs for all services (or specify service name)"
    echo "  clean     Clean up all development containers and volumes"
    echo "  rebuild   Rebuild and restart all services"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                 # Start development environment"
    echo "  $0 logs backend          # Show backend logs"
    echo "  $0 logs frontend         # Show frontend logs"
    echo "  $0 status                # Check service status"
    echo ""
    echo "Services will be available at:"
    echo "  Frontend:      http://localhost:3005"
    echo "  Backend API:   http://localhost:5001/api"
    echo "  Mongo Express: http://localhost:8081"
}

# Main script logic
case "$1" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart_dev
        ;;
    status)
        status_dev
        ;;
    logs)
        logs_dev "$@"
        ;;
    clean)
        clean_dev
        ;;
    rebuild)
        rebuild_dev
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        print_error "No command specified."
        echo ""
        show_help
        exit 1
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
