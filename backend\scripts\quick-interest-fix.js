#!/usr/bin/env node

/**
 * Quick Interest Fix Command
 * 
 * <PERSON><PERSON>y nhanh daily interest calculation khi cronjob bị lỗi
 * Phiên bản đơn giản, ít logging, chạy nhanh
 * 
 * Usage:
 *   node scripts/quick-interest-fix.js
 *   npm run quick-interest
 */

require('dotenv').config({ path: require('path').join(__dirname, '../.env.docker') });

const mongoose = require('mongoose');

// Import models
require('../src/models/userModel');
require('../src/models/walletModel');
require('../src/models/investmentPackageModel');
require('../src/models/transactionModel');
require('../src/models/paymentHistoryModel');
require('../src/models/auditTrailModel');

// Import service
const interestCalculationService = require('../src/services/interestCalculationService').default;

async function quickInterestFix() {
  console.log('⚡ Quick Interest Fix - Starting...');
  
  try {
    // Connect to database
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield';
    await mongoose.connect(mongoUri);
    console.log('✅ Database connected');
    
    // Run interest calculation
    console.log('🚀 Running interest calculation...');
    const startTime = Date.now();
    
    const summary = await interestCalculationService.processAllActivePackages();
    
    const duration = Date.now() - startTime;
    
    // Quick summary
    console.log('\n📊 RESULTS:');
    console.log(`   Packages: ${summary.totalPackages}`);
    console.log(`   Success: ${summary.successfulCalculations}`);
    console.log(`   Failed: ${summary.failedCalculations}`);
    console.log(`   Interest: ${summary.totalInterestPaid.toFixed(6)} USDT`);
    console.log(`   Duration: ${(duration/1000).toFixed(2)}s`);
    
    if (summary.errors && summary.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      summary.errors.forEach((error, i) => {
        console.log(`   ${i+1}. ${error.packageId}: ${error.error}`);
      });
    }
    
    if (summary.failedCalculations === 0) {
      console.log('\n🎉 SUCCESS: All packages processed!');
    } else {
      console.log(`\n⚠️  WARNING: ${summary.failedCalculations} packages failed!`);
    }
    
  } catch (error) {
    console.error('\n💥 ERROR:', error.message);
    process.exit(1);
  } finally {
    try {
      await mongoose.connection.close();
      console.log('✅ Database closed');
    } catch (error) {
      console.error('❌ Close error:', error.message);
    }
  }
}

// Handle interrupts
process.on('SIGINT', async () => {
  console.log('\n⚠️  Interrupted. Closing...');
  try {
    await mongoose.connection.close();
  } catch (error) {
    // Ignore
  }
  process.exit(0);
});

// Run
if (require.main === module) {
  quickInterestFix().catch(error => {
    console.error('💥 Fatal:', error.message);
    process.exit(1);
  });
}
