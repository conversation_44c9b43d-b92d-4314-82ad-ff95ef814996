// Mock service for wallet management - for development/testing
export interface WithdrawalAddress {
  _id: string;
  currency: string;
  address: string;
  formattedAddress: string;
  label: string;
  network: string;
  isDefault: boolean;
  isActive: boolean;
  isVerified: boolean;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface SupportedCurrency {
  currency: string;
  name: string;
  networks: string[];
  addressFormat: string;
}

export interface CreateAddressRequest {
  currency: string;
  address: string;
  label: string;
  network?: string;
}

export interface UpdateAddressRequest {
  label?: string;
  isDefault?: boolean;
}

export interface VerifyAddressRequest {
  verificationCode: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  count?: number;
}

// Mock data
const mockAddresses: WithdrawalAddress[] = [
  {
    _id: '1',
    currency: 'BTC',
    address: '**********************************',
    formattedAddress: '1A1zP1...DivfNa',
    label: 'My Bitcoin Wallet',
    network: 'mainnet',
    isDefault: true,
    isActive: true,
    isVerified: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    _id: '2',
    currency: 'ETH',
    address: '******************************************',
    formattedAddress: '0x742d...e1e1e1',
    label: 'Ethereum Main',
    network: 'mainnet',
    isDefault: false,
    isActive: true,
    isVerified: false,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  }
];

const mockSupportedCurrencies: SupportedCurrency[] = [
  {
    currency: 'BTC',
    name: 'Bitcoin',
    networks: ['mainnet', 'testnet'],
    addressFormat: 'Legacy (1...) or SegWit (bc1...)'
  },
  {
    currency: 'ETH',
    name: 'Ethereum',
    networks: ['mainnet', 'goerli', 'sepolia', 'arbitrum', 'optimism', 'polygon'],
    addressFormat: '0x... format (42 characters)'
  },
  {
    currency: 'USDT',
    name: 'Tether USD',
    networks: ['ethereum', 'tron', 'bsc', 'arbitrum', 'optimism', 'polygon', 'avalanche', 'solana'],
    addressFormat: '0x... (ERC-20/BEP-20) or T... (TRC-20)'
  },
  {
    currency: 'BNB',
    name: 'Binance Coin',
    networks: ['bsc', 'binance-chain'],
    addressFormat: '0x... format (42 characters)'
  },
  {
    currency: 'ADA',
    name: 'Cardano',
    networks: ['mainnet', 'testnet'],
    addressFormat: 'addr1... format (Cardano address)'
  },
  {
    currency: 'DOT',
    name: 'Polkadot',
    networks: ['mainnet', 'kusama'],
    addressFormat: '1... format (Polkadot address)'
  },
  {
    currency: 'LINK',
    name: 'Chainlink',
    networks: ['ethereum', 'bsc', 'arbitrum', 'optimism', 'polygon', 'avalanche'],
    addressFormat: '0x... format (42 characters)'
  },
  {
    currency: 'UNI',
    name: 'Uniswap',
    networks: ['ethereum', 'arbitrum', 'optimism', 'polygon'],
    addressFormat: '0x... format (42 characters)'
  },
  {
    currency: 'MATIC',
    name: 'Polygon',
    networks: ['polygon', 'ethereum'],
    addressFormat: '0x... format (42 characters)'
  },
  {
    currency: 'AVAX',
    name: 'Avalanche',
    networks: ['avalanche', 'ethereum'],
    addressFormat: '0x... format (42 characters)'
  },
  {
    currency: 'SOL',
    name: 'Solana',
    networks: ['solana'],
    addressFormat: 'Base58 format (32-44 characters)'
  },
  {
    currency: 'ATOM',
    name: 'Cosmos',
    networks: ['cosmos'],
    addressFormat: 'cosmos1... format (Cosmos address)'
  },
  {
    currency: 'XRP',
    name: 'Ripple',
    networks: ['xrpl'],
    addressFormat: 'r... format (Ripple address)'
  },
  {
    currency: 'LTC',
    name: 'Litecoin',
    networks: ['mainnet', 'testnet'],
    addressFormat: 'Legacy (L...) or SegWit (ltc1...) format'
  },
  {
    currency: 'BCH',
    name: 'Bitcoin Cash',
    networks: ['mainnet', 'testnet'],
    addressFormat: 'Legacy (1...) or CashAddr format'
  },
  {
    currency: 'DOGE',
    name: 'Dogecoin',
    networks: ['mainnet', 'testnet'],
    addressFormat: 'D... format (Dogecoin address)'
  },
  {
    currency: 'TRX',
    name: 'TRON',
    networks: ['tron'],
    addressFormat: 'T... format (TRON address)'
  }
];

// Mock service implementation
export const mockWalletManagementService = {
  // Get all withdrawal addresses for user
  async getUserAddresses(currency?: string): Promise<ApiResponse<WithdrawalAddress[]>> {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
    
    let filteredAddresses = mockAddresses;
    if (currency) {
      filteredAddresses = mockAddresses.filter(addr => addr.currency === currency.toUpperCase());
    }
    
    return {
      success: true,
      data: filteredAddresses,
      count: filteredAddresses.length
    };
  },

  // Get withdrawal address by ID
  async getAddressById(id: string): Promise<ApiResponse<WithdrawalAddress>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const address = mockAddresses.find(addr => addr._id === id);
    if (!address) {
      throw new Error('Address not found');
    }
    
    return {
      success: true,
      data: address
    };
  },

  // Add new withdrawal address
  async addAddress(data: CreateAddressRequest): Promise<ApiResponse<WithdrawalAddress & { verificationRequired: boolean }>> {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newAddress: WithdrawalAddress = {
      _id: Date.now().toString(),
      currency: data.currency.toUpperCase(),
      address: data.address,
      formattedAddress: `${data.address.slice(0, 6)}...${data.address.slice(-6)}`,
      label: data.label,
      network: data.network || 'mainnet',
      isDefault: false,
      isActive: true,
      isVerified: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    mockAddresses.push(newAddress);
    
    return {
      success: true,
      data: { ...newAddress, verificationRequired: true },
      message: 'Withdrawal address added successfully. Please verify with the code sent.'
    };
  },

  // Update withdrawal address
  async updateAddress(id: string, data: UpdateAddressRequest): Promise<ApiResponse<WithdrawalAddress>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const addressIndex = mockAddresses.findIndex(addr => addr._id === id);
    if (addressIndex === -1) {
      throw new Error('Address not found');
    }
    
    if (data.label) {
      mockAddresses[addressIndex].label = data.label;
    }
    
    if (data.isDefault) {
      // Remove default from other addresses of same currency
      mockAddresses.forEach(addr => {
        if (addr.currency === mockAddresses[addressIndex].currency && addr._id !== id) {
          addr.isDefault = false;
        }
      });
      mockAddresses[addressIndex].isDefault = true;
    }
    
    mockAddresses[addressIndex].updatedAt = new Date();
    
    return {
      success: true,
      data: mockAddresses[addressIndex],
      message: 'Withdrawal address updated successfully'
    };
  },

  // Delete withdrawal address
  async deleteAddress(id: string): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const addressIndex = mockAddresses.findIndex(addr => addr._id === id);
    if (addressIndex === -1) {
      throw new Error('Address not found');
    }
    
    mockAddresses.splice(addressIndex, 1);
    
    return {
      success: true,
      data: undefined,
      message: 'Withdrawal address deleted successfully'
    };
  },

  // Verify withdrawal address
  async verifyAddress(id: string, data: VerifyAddressRequest): Promise<ApiResponse<{ id: string; isVerified: boolean }>> {
    await new Promise(resolve => setTimeout(resolve, 600));
    
    const address = mockAddresses.find(addr => addr._id === id);
    if (!address) {
      throw new Error('Address not found');
    }
    
    if (data.verificationCode !== '123456') {
      throw new Error('Invalid verification code');
    }
    
    address.isVerified = true;
    address.updatedAt = new Date();
    
    return {
      success: true,
      data: { id: address._id, isVerified: true },
      message: 'Withdrawal address verified successfully'
    };
  },

  // Set default withdrawal address
  async setDefaultAddress(id: string): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const address = mockAddresses.find(addr => addr._id === id);
    if (!address) {
      throw new Error('Address not found');
    }
    
    if (!address.isVerified) {
      throw new Error('Only verified addresses can be set as default');
    }
    
    // Remove default from other addresses of same currency
    mockAddresses.forEach(addr => {
      if (addr.currency === address.currency && addr._id !== id) {
        addr.isDefault = false;
      }
    });
    
    address.isDefault = true;
    address.updatedAt = new Date();
    
    return {
      success: true,
      data: undefined,
      message: 'Default withdrawal address set successfully'
    };
  },

  // Resend verification code
  async resendVerificationCode(id: string): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const address = mockAddresses.find(addr => addr._id === id);
    if (!address) {
      throw new Error('Address not found');
    }
    
    if (address.isVerified) {
      throw new Error('Address is already verified');
    }
    
    return {
      success: true,
      data: undefined,
      message: 'Verification code sent successfully'
    };
  },

  // Get supported currencies and networks
  async getSupportedCurrencies(): Promise<ApiResponse<SupportedCurrency[]>> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return {
      success: true,
      data: mockSupportedCurrencies
    };
  },

  // Client-side validation functions
  validateAddressFormat: (address: string, currency: string): boolean => {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(address);
      case 'ETH':
      case 'USDT':
      case 'BNB':
        return /^0x[a-fA-F0-9]{40}$/.test(address);
      default:
        return address.length > 10;
    }
  },

  getAddressFormatHint: (currency: string): string => {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return 'Legacy (1...) or SegWit (bc1...) format';
      case 'ETH':
      case 'USDT':
      case 'BNB':
        return '0x... format (42 characters)';
      default:
        return 'Please enter a valid address for this currency';
    }
  },

  getNetworkOptions: (currency: string): string[] => {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return ['mainnet'];
      case 'ETH':
        return ['mainnet', 'testnet'];
      case 'USDT':
        return ['ethereum', 'tron', 'bsc'];
      default:
        return ['mainnet'];
    }
  }
};
