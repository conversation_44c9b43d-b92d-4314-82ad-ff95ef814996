#!/bin/bash

# CryptoYield Development Environment Setup
# This script sets up a complete development environment with hot-reload capabilities

set -e

echo "🚀 Setting up CryptoYield Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[SETUP]${NC} $1"
}

# Step 1: Check prerequisites
print_header "Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

print_success "Docker and Docker Compose are available"

# Step 2: Check and create MongoDB keyfile
print_header "Setting up MongoDB keyfile for replica set..."

if [ ! -f "mongodb-keyfile/mongodb-keyfile" ]; then
    print_warning "MongoDB keyfile not found. Creating keyfile..."
    mkdir -p mongodb-keyfile
    openssl rand -base64 756 > mongodb-keyfile/mongodb-keyfile
    chmod 600 mongodb-keyfile/mongodb-keyfile
    print_success "MongoDB keyfile created successfully"
else
    print_success "MongoDB keyfile already exists"
    chmod 600 mongodb-keyfile/mongodb-keyfile
fi

# Step 3: Ensure mongo-init-replica.js exists
print_header "Checking MongoDB initialization script..."

if [ ! -f "mongo-init-replica.js" ]; then
    print_warning "MongoDB initialization script not found. Creating it..."
    cat > mongo-init-replica.js << 'EOF'
// MongoDB Replica Set Initialization Script for Development
// This script initializes the replica set required for transactions

print('🚀 Starting MongoDB replica set initialization...');

// Wait for MongoDB to be ready
var attempts = 0;
var maxAttempts = 30;

while (attempts < maxAttempts) {
  try {
    db.adminCommand('ping');
    print('✅ MongoDB is ready');
    break;
  } catch (e) {
    attempts++;
    print('⏳ Waiting for MongoDB... attempt ' + attempts + '/' + maxAttempts);
    sleep(2000);
  }
}

if (attempts >= maxAttempts) {
  print('❌ MongoDB failed to start within expected time');
  quit(1);
}

// Check if replica set is already initialized
try {
  var status = rs.status();
  if (status.ok === 1) {
    print('✅ Replica set already initialized: ' + status.set);
    print('✅ Current primary: ' + status.members.find(m => m.stateStr === 'PRIMARY').name);
    quit(0);
  }
} catch (e) {
  print('📋 Replica set not initialized yet, proceeding with initialization...');
}

// Initialize replica set with proper hostname for Docker
var config = {
  _id: 'rs0',
  members: [
    {
      _id: 0,
      host: 'mongodb:27017',
      priority: 1,
      votes: 1,
      arbiterOnly: false,
      buildIndexes: true,
      hidden: false,
      secondaryDelaySecs: 0,
      tags: {}
    }
  ],
  settings: {
    electionTimeoutMillis: 2000,
    heartbeatTimeoutSecs: 2,
    heartbeatIntervalMillis: 2000,
    replicationOplogSizeMB: 1024,
    getLastErrorModes: {},
    getLastErrorDefaults: {
      w: 1,
      wtimeout: 0
    }
  }
};

try {
  var result = rs.initiate(config);
  if (result.ok === 1) {
    print('✅ Replica set initialized successfully');
    
    // Wait for primary election
    var primaryFound = false;
    var attempts = 0;
    var maxAttempts = 30;
    
    while (!primaryFound && attempts < maxAttempts) {
      try {
        var status = rs.status();
        if (status.ok === 1) {
          var primary = status.members.find(m => m.stateStr === 'PRIMARY');
          if (primary) {
            print('✅ Primary elected: ' + primary.name);
            primaryFound = true;
          } else {
            attempts++;
            print('⏳ Waiting for primary election... attempt ' + attempts + '/' + maxAttempts);
            sleep(2000);
          }
        }
      } catch (e) {
        attempts++;
        sleep(2000);
      }
    }
    
    if (primaryFound) {
      print('🎉 Replica set setup completed successfully!');
      print('📋 Replica set name: rs0');
      print('🔗 Connection string: ********************************************************************************************************');
    } else {
      print('⚠️ Replica set initialized but primary election may need more time');
    }
  } else {
    print('❌ Failed to initialize replica set: ' + JSON.stringify(result));
    quit(1);
  }
} catch (e) {
  print('❌ Error initializing replica set: ' + e.message);
  quit(1);
}

print('✅ MongoDB replica set initialization completed');
EOF
    print_success "MongoDB initialization script created"
else
    print_success "MongoDB initialization script already exists"
fi

# Step 4: Stop any existing services
print_header "Stopping any existing services..."
docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
docker-compose -f docker-compose.mongo.yml down 2>/dev/null || true

# Step 5: Build backend development image
print_header "Building backend development image..."
docker-compose -f docker-compose.dev.yml build backend

# Step 6: Start services
print_header "Starting development services..."
docker-compose -f docker-compose.dev.yml up -d

# Step 7: Wait for services to be ready
print_header "Waiting for services to be ready..."

# Wait for MongoDB
print_status "Waiting for MongoDB to be healthy..."
timeout=180
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.dev.yml ps mongodb | grep -q "healthy"; then
        print_success "MongoDB is healthy"
        break
    fi
    
    print_status "Waiting for MongoDB... ($elapsed/$timeout seconds)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

if [ $elapsed -ge $timeout ]; then
    print_error "MongoDB failed to become healthy within $timeout seconds"
    print_status "Checking MongoDB logs..."
    docker-compose -f docker-compose.dev.yml logs mongodb
    exit 1
fi

# Wait for Redis
print_status "Waiting for Redis to be ready..."
sleep 10

# Wait for Backend
print_status "Waiting for Backend to be ready..."
sleep 30

# Step 8: Initialize replica set if needed
print_header "Ensuring replica set is properly initialized..."
docker exec cryptoyield-mongodb-dev mongosh --eval "
try {
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set is active: ' + status.set);
        const primary = status.members.find(m => m.stateStr === 'PRIMARY');
        if (primary) {
            print('✅ Primary member: ' + primary.name);
        }
    }
} catch (e) {
    print('⚠️ Replica set status: ' + e.message);
}
" 2>/dev/null || print_warning "Replica set may need more time to initialize"

# Step 9: Test services
print_header "Testing services..."

# Test MongoDB
if docker exec cryptoyield-mongodb-dev mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
    print_success "✅ MongoDB is accessible"
else
    print_warning "⚠️ MongoDB may still be initializing"
fi

# Test Redis
if docker exec cryptoyield-redis-dev redis-cli ping 2>/dev/null | grep -q "PONG"; then
    print_success "✅ Redis is accessible"
else
    print_warning "⚠️ Redis connection issue"
fi

# Test Backend
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/health 2>/dev/null || echo "000")
if [[ "$backend_health" == "200" ]]; then
    print_success "✅ Backend is accessible"
else
    print_warning "⚠️ Backend may still be starting (HTTP $backend_health)"
fi

# Test Mongo Express
mongo_express_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
if [[ "$mongo_express_health" == "200" || "$mongo_express_health" == "401" ]]; then
    print_success "✅ Mongo Express is accessible"
else
    print_warning "⚠️ Mongo Express may still be starting (HTTP $mongo_express_health)"
fi

# Step 10: Display final information
echo ""
print_success "🎉 Development environment setup completed!"
echo ""
echo "📋 Service Information:"
echo "  🖥️  Backend API: http://localhost:5000"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🔗 Connection Details:"
echo "  📝 MongoDB URI: **********************************************************************************************************"
echo "  🔑 Redis URI: redis://localhost:6379"
echo ""
echo "📁 Development Features:"
echo "  ✅ Live code editing (./backend/src mounted as volume)"
echo "  ✅ Hot-reload enabled (nodemon + ts-node)"
echo "  ✅ Transaction support (MongoDB replica set)"
echo "  ✅ Database admin interface (Mongo Express)"
echo "  ✅ Proper networking between services"
echo ""

# Show container status
print_header "Final container status:"
docker-compose -f docker-compose.dev.yml ps

echo ""
print_success "🚀 Your development environment is ready!"
echo ""
echo "📝 Next steps:"
echo "  1. Edit backend code in ./backend/src/ - changes will be reflected immediately"
echo "  2. View logs: docker-compose -f docker-compose.dev.yml logs -f backend"
echo "  3. Access database: http://localhost:8081"
echo "  4. Test API: curl http://localhost:5000/api/health"
echo ""
print_success "Happy coding! 🎯"
