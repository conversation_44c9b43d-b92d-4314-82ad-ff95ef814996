#!/bin/bash

# CryptoYield Full-Stack Development Environment Setup
# Frontend + Backend + Database + Admin Interface

set -e

echo "🚀 Thiết lập môi trường phát triển Full-Stack CryptoYield..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[SETUP]${NC} $1"
}

# Step 1: Kiểm tra prerequisites
print_header "Kiểm tra prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker chưa được cài đặt. Vui lòng cài đặt Docker và thử lại."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose chưa được cài đặt. Vui lòng cài đặt Docker Compose và thử lại."
    exit 1
fi

print_success "Docker và Docker Compose đã sẵn sàng"

# Step 2: Dừng tất cả services hiện tại
print_header "Dừng tất cả services hiện tại..."
docker-compose -f docker-compose.dev-complete.yml down 2>/dev/null || true
docker-compose -f docker-compose.dev-working.yml down 2>/dev/null || true
docker-compose -f docker-compose.dev.yml down 2>/dev/null || true

# Step 3: Xóa volumes cũ nếu cần
print_header "Kiểm tra và xóa volumes cũ nếu cần..."
if docker volume ls | grep -q "mongodb_data"; then
    print_warning "Tìm thấy MongoDB volumes cũ. Bạn có muốn xóa để tạo database mới? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        docker volume rm mongodb_data mongodb_config 2>/dev/null || true
        print_success "Đã xóa volumes cũ"
    fi
fi

# Step 4: Build images
print_header "Build Docker images..."

print_status "Building backend image..."
docker-compose -f docker-compose.dev-complete.yml build backend

print_status "Building frontend image..."
docker-compose -f docker-compose.dev-complete.yml build frontend

# Step 5: Khởi động services theo thứ tự
print_header "Khởi động services..."

print_status "Khởi động database services..."
docker-compose -f docker-compose.dev-complete.yml up -d mongodb redis

# Chờ database services sẵn sàng
print_status "Chờ database services sẵn sàng..."
timeout=120
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.dev-complete.yml ps mongodb | grep -q "healthy" && \
       docker-compose -f docker-compose.dev-complete.yml ps redis | grep -q "healthy"; then
        print_success "Database services đã sẵn sàng"
        break
    fi
    
    print_status "Chờ database services... ($elapsed/$timeout giây)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

if [ $elapsed -ge $timeout ]; then
    print_error "Database services không thể khởi động trong $timeout giây"
    exit 1
fi

# Step 6: Khởi tạo MongoDB replica set
print_header "Khởi tạo MongoDB replica set..."
sleep 10

docker exec cryptoyield-mongodb mongosh --eval "
try {
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set đã được khởi tạo: ' + status.set);
    }
} catch (e) {
    if (e.message.includes('no replset config')) {
        print('🔧 Khởi tạo replica set...');
        rs.initiate({
            _id: 'rs0',
            members: [{ _id: 0, host: 'mongodb:27017' }]
        });
        print('✅ Replica set đã được khởi tạo thành công');
    } else {
        print('⚠️ Trạng thái replica set: ' + e.message);
    }
}
" 2>/dev/null || print_warning "Replica set có thể cần thêm thời gian để khởi tạo"

# Step 7: Khởi động backend
print_header "Khởi động backend service..."
docker-compose -f docker-compose.dev-complete.yml up -d backend mongo-express

# Chờ backend sẵn sàng
print_status "Chờ backend service sẵn sàng..."
timeout=90
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.dev-complete.yml ps backend | grep -q "healthy"; then
        print_success "Backend service đã sẵn sàng"
        break
    fi
    
    print_status "Chờ backend service... ($elapsed/$timeout giây)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

# Step 8: Khởi động frontend
print_header "Khởi động frontend service..."
docker-compose -f docker-compose.dev-complete.yml up -d frontend

# Step 9: Chờ tất cả services sẵn sàng
print_header "Chờ tất cả services sẵn sàng..."
sleep 30

# Step 10: Kiểm tra services
print_header "Kiểm tra services..."

# Test MongoDB
if docker exec cryptoyield-mongodb mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
    print_success "✅ MongoDB có thể truy cập"
else
    print_warning "⚠️ MongoDB có thể vẫn đang khởi tạo"
fi

# Test Redis
if docker exec cryptoyield-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
    print_success "✅ Redis có thể truy cập"
else
    print_warning "⚠️ Redis có vấn đề kết nối"
fi

# Test Backend
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5001 2>/dev/null || echo "000")
if [[ "$backend_health" == "200" || "$backend_health" == "404" ]]; then
    print_success "✅ Backend có thể truy cập"
else
    print_warning "⚠️ Backend có thể vẫn đang khởi động (HTTP $backend_health)"
fi

# Test Frontend
frontend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3003 2>/dev/null || echo "000")
if [[ "$frontend_health" == "200" ]]; then
    print_success "✅ Frontend có thể truy cập"
else
    print_warning "⚠️ Frontend có thể vẫn đang khởi động (HTTP $frontend_health)"
fi

# Test Mongo Express
mongo_express_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
if [[ "$mongo_express_health" == "200" || "$mongo_express_health" == "401" ]]; then
    print_success "✅ Mongo Express có thể truy cập"
else
    print_warning "⚠️ Mongo Express có thể vẫn đang khởi động (HTTP $mongo_express_health)"
fi

# Step 11: Test transaction capability
print_header "Kiểm tra khả năng transaction..."
docker exec cryptoyield-mongodb mongosh --eval "
const session = db.getMongo().startSession();
session.startTransaction();
session.getDatabase('cryptoyield_dev_test').transactionTest.insertOne({test: 'fullstack_dev_test', timestamp: new Date()});
session.commitTransaction();
session.endSession();
print('✅ Transaction test thành công!');
" 2>/dev/null || print_warning "Transaction test có thể cần thêm thời gian"

# Step 12: Hiển thị thông tin cuối cùng
echo ""
print_success "🎉 Môi trường phát triển Full-Stack đã sẵn sàng!"
echo ""
echo "📋 Thông tin Services:"
echo "  🌐 Frontend (React): http://localhost:3003"
echo "  🖥️  Backend API: http://localhost:5001"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo ""
echo "🔗 Chi tiết kết nối:"
echo "  📝 Frontend URL: http://localhost:3003"
echo "  📝 Backend API: http://localhost:5001/api"
echo "  📝 MongoDB URI: mongodb://localhost:27017/cryptoyield?replicaSet=rs0"
echo "  🔑 Redis URI: redis://localhost:6379"
echo ""
echo "📁 Tính năng phát triển:"
echo "  ✅ Frontend hot-reload (./frontend/src được mount)"
echo "  ✅ Backend hot-reload (./backend/src được mount)"
echo "  ✅ Hỗ trợ transaction (MongoDB replica set)"
echo "  ✅ Giao diện quản trị database (Mongo Express)"
echo "  ✅ CORS được cấu hình cho frontend-backend communication"
echo "  ✅ Networking phù hợp giữa tất cả services"
echo ""

# Hiển thị trạng thái container
print_header "Trạng thái container cuối cùng:"
docker-compose -f docker-compose.dev-complete.yml ps

echo ""
print_success "🚀 Môi trường phát triển Full-Stack đã sẵn sàng!"
echo ""
echo "📝 Các bước tiếp theo:"
echo "  1. Chỉnh sửa frontend code trong ./frontend/src/ - thay đổi sẽ được phản ánh ngay lập tức"
echo "  2. Chỉnh sửa backend code trong ./backend/src/ - thay đổi sẽ được phản ánh ngay lập tức"
echo "  3. Xem logs: docker-compose -f docker-compose.dev-complete.yml logs -f [service]"
echo "  4. Truy cập ứng dụng: http://localhost:3003"
echo "  5. Truy cập database admin: http://localhost:8081"
echo ""
echo "🔧 Lệnh hữu ích:"
echo "  • Xem logs frontend: docker-compose -f docker-compose.dev-complete.yml logs -f frontend"
echo "  • Xem logs backend: docker-compose -f docker-compose.dev-complete.yml logs -f backend"
echo "  • Dừng tất cả: docker-compose -f docker-compose.dev-complete.yml down"
echo "  • Khởi động lại: docker-compose -f docker-compose.dev-complete.yml restart [service]"
echo ""
print_success "Chúc bạn phát triển vui vẻ! 🎯"
