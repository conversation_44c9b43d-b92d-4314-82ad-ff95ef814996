// Quick database check
const { MongoClient } = require('mongodb');

const checkDatabase = async () => {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db('cryptoyield');
    
    // 1. Check users with referrers
    const usersWithReferrers = await db.collection('users').find({
      referrerId: { $exists: true, $ne: null }
    }).toArray();
    
    console.log(`\n👥 Users with referrers: ${usersWithReferrers.length}`);
    
    if (usersWithReferrers.length === 0) {
      console.log('❌ NO USERS WITH REFERRERS!');
      console.log('   This is why 3% commission is not working.');
      console.log('   Users need to register with referral codes.\n');
    } else {
      usersWithReferrers.forEach((user, i) => {
        console.log(`   ${i+1}. ${user.firstName} ${user.lastName}`);
        console.log(`      Email: ${user.email}`);
        console.log(`      ReferrerID: ${user.referrerId}`);
      });
    }
    
    // 2. Check existing commissions
    const commissions = await db.collection('referralcommissions').find({}).toArray();
    console.log(`\n💰 Existing commissions: ${commissions.length}`);
    
    if (commissions.length > 0) {
      commissions.forEach((comm, i) => {
        console.log(`   ${i+1}. ${comm.amount} ${comm.currency}`);
        console.log(`      Status: ${comm.status}`);
        console.log(`      Created: ${comm.createdAt}`);
      });
    }
    
    // 3. Check recent deposits
    const deposits = await db.collection('transactions').find({
      type: 'deposit',
      status: 'approved'
    }).sort({ createdAt: -1 }).limit(5).toArray();
    
    console.log(`\n📊 Recent approved deposits: ${deposits.length}`);
    deposits.forEach((deposit, i) => {
      console.log(`   ${i+1}. ${deposit.amount} ${deposit.asset}`);
      console.log(`      User: ${deposit.userId}`);
      console.log(`      Date: ${deposit.createdAt}`);
    });
    
    // 4. Summary
    console.log(`\n📋 SUMMARY:`);
    console.log(`   Users with referrers: ${usersWithReferrers.length}`);
    console.log(`   Existing commissions: ${commissions.length}`);
    console.log(`   Recent deposits: ${deposits.length}`);
    
    if (usersWithReferrers.length === 0) {
      console.log(`\n🚨 MAIN ISSUE: No referral relationships exist!`);
      console.log(`   Solution: Create users with referral codes`);
    } else if (commissions.length >= usersWithReferrers.length) {
      console.log(`\n⚠️  All users already have commissions (only paid once)`);
    } else {
      console.log(`\n✅ System should work - check server logs during approval`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
};

checkDatabase();
