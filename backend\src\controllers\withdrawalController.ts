import { Request, Response } from 'express';
import { catchAsync } from '../utils/errorHandler';
import withdrawalService from '../services/withdrawalService';
import Withdrawal from '../models/withdrawalModel';
import { logger } from '../utils/logger';

// @desc    Validate withdrawal request
// @route   POST /api/withdrawals/validate
// @access  Private
export const validateWithdrawal = catchAsync(async (req: Request, res: Response) => {
  const { cryptocurrency, withdrawalType, amount, walletAddress, network } = req.body;

  // Input validation
  if (!cryptocurrency || !withdrawalType || !amount || !walletAddress || !network) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: cryptocurrency, withdrawalType, amount, walletAddress, network'
    });
  }

  if (amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Amount must be greater than 0'
    });
  }

  if (!['balance', 'interest', 'commission'].includes(withdrawalType)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid withdrawal type. Must be: balance, interest, or commission'
    });
  }

  try {
    const validation = await withdrawalService.validateWithdrawal({
      userId: req.user._id.toString(),
      cryptocurrency,
      withdrawalType,
      amount: parseFloat(amount),
      walletAddress,
      network
    });

    res.json({
      success: true,
      data: {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        availableBalance: validation.availableBalance,
        usdValue: validation.usdValue,
        lockInfo: validation.lockInfo
      }
    });

  } catch (error: any) {
    logger.error('Withdrawal validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Validation failed',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Submit withdrawal request
// @route   POST /api/withdrawals/submit
// @access  Private
export const submitWithdrawal = catchAsync(async (req: Request, res: Response) => {
  const { cryptocurrency, withdrawalType, amount, walletAddress, network, investmentPackageId } = req.body;

  // Input validation
  if (!cryptocurrency || !withdrawalType || !amount || !walletAddress || !network) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: cryptocurrency, withdrawalType, amount, walletAddress, network'
    });
  }

  if (amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Amount must be greater than 0'
    });
  }

  if (!['balance', 'interest', 'commission'].includes(withdrawalType)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid withdrawal type. Must be: balance, interest, or commission'
    });
  }

  // Validate investmentPackageId requirements
  if (withdrawalType === 'balance' && !investmentPackageId) {
    return res.status(400).json({
      success: false,
      message: 'investmentPackageId is required for balance withdrawals'
    });
  }

  if (withdrawalType !== 'balance' && investmentPackageId) {
    return res.status(400).json({
      success: false,
      message: 'investmentPackageId should not be provided for interest/commission withdrawals'
    });
  }

  try {
    const withdrawal = await withdrawalService.submitWithdrawal({
      userId: req.user._id.toString(),
      cryptocurrency,
      withdrawalType,
      amount: parseFloat(amount),
      walletAddress,
      network,
      investmentPackageId
    });

    res.status(201).json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        withdrawalId: withdrawal._id,
        cryptocurrency: withdrawal.cryptocurrency,
        withdrawalType: withdrawal.withdrawalType,
        amount: withdrawal.amount,
        usdValue: withdrawal.usdValue,
        networkFee: withdrawal.networkFee,
        netAmount: withdrawal.netAmount,
        walletAddress: withdrawal.walletAddress,
        network: withdrawal.network,
        status: withdrawal.status,
        createdAt: withdrawal.createdAt
      }
    });

  } catch (error: any) {
    logger.error('Withdrawal submission error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to submit withdrawal request',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// @desc    Get withdrawal history
// @route   GET /api/withdrawals/history
// @access  Private
export const getWithdrawalHistory = catchAsync(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;

  try {
    const result = await withdrawalService.getWithdrawalHistory(
      req.user._id.toString(),
      page,
      limit
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error: any) {
    logger.error('Get withdrawal history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawal history',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get withdrawable balances
// @route   GET /api/withdrawals/balance/:crypto?
// @access  Private
export const getWithdrawableBalances = catchAsync(async (req: Request, res: Response) => {
  const { crypto } = req.params;

  try {
    const result = await withdrawalService.getWithdrawableBalances(
      req.user._id.toString(),
      crypto
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error: any) {
    logger.error('Get withdrawable balances error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawable balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get single withdrawal details
// @route   GET /api/withdrawals/:id
// @access  Private
export const getWithdrawalDetails = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const withdrawal = await Withdrawal.findOne({
      _id: id,
      userId: req.user._id
    }).populate('user', 'firstName lastName email');

    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal not found'
      });
    }

    res.json({
      success: true,
      data: withdrawal
    });

  } catch (error: any) {
    logger.error('Get withdrawal details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawal details',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Cancel pending withdrawal
// @route   DELETE /api/withdrawals/:id
// @access  Private
export const cancelWithdrawal = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const withdrawal = await Withdrawal.findOne({
      _id: id,
      userId: req.user._id,
      status: 'pending'
    });

    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Pending withdrawal not found'
      });
    }

    // TODO: Implement fund restoration logic
    withdrawal.status = 'failed';
    withdrawal.adminNotes = 'Cancelled by user';
    await withdrawal.save();

    res.json({
      success: true,
      message: 'Withdrawal cancelled successfully'
    });

  } catch (error: any) {
    logger.error('Cancel withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel withdrawal',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get withdrawal statistics
// @route   GET /api/withdrawals/stats
// @access  Private
export const getWithdrawalStats = catchAsync(async (req: Request, res: Response) => {
  try {
    const stats = await Withdrawal.getStatistics(req.user._id.toString());

    res.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    logger.error('Get withdrawal stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawal statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
