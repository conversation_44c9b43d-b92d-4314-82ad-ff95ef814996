Stack trace:
Frame         Function      Args
0007FFFF91F0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF91F0, 0007FFFF80F0) msys-2.0.dll+0x1FE8E
0007FFFF91F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF94C8) msys-2.0.dll+0x67F9
0007FFFF91F0  000210046832 (000210286019, 0007FFFF90A8, 0007FFFF91F0, 000000000000) msys-2.0.dll+0x6832
0007FFFF91F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF91F0  000210068E24 (0007FFFF9200, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF94D0  00021006A225 (0007FFFF9200, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAA5D00000 ntdll.dll
7FFAA4C20000 KERNEL32.DLL
7FFAA3470000 KERNELBASE.dll
7FFAA4010000 USER32.dll
7FFAA2E50000 win32u.dll
7FFAA3AA0000 GDI32.dll
7FFAA32A0000 gdi32full.dll
000210040000 msys-2.0.dll
7FFAA2E80000 msvcp_win.dll
7FFAA2FD0000 ucrtbase.dll
7FFAA5890000 advapi32.dll
7FFAA3AD0000 msvcrt.dll
7FFAA4690000 sechost.dll
7FFAA49F0000 RPCRT4.dll
7FFAA2360000 CRYPTBASE.DLL
7FFAA2F30000 bcryptPrimitives.dll
7FFAA4800000 IMM32.DLL
