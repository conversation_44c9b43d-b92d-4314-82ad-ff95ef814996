/**
 * Frontend script to create investment test data via API calls
 * This script can be run in the browser console or as a Node.js script
 */

// Configuration
const API_BASE_URL = process.env.VITE_API_URL || 'http://localhost:5000/api';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'Investor'
};

// Test investment packages data
const TEST_INVESTMENTS = [
  // BTC Investments
  { currency: 'BTC', amount: 0.5, daysAgo: 45 },
  { currency: 'BTC', amount: 0.25, daysAgo: 30 },
  { currency: 'BTC', amount: 1.0, daysAgo: 120 },
  { currency: 'BTC', amount: 0.1, daysAgo: 2 },

  // ETH Investments
  { currency: 'ETH', amount: 5.0, daysAgo: 60 },
  { currency: 'ETH', amount: 2.5, daysAgo: 20 },
  { currency: 'ETH', amount: 10.0, daysAgo: 90 },
  { currency: 'ETH', amount: 1.5, daysAgo: 1 },

  // USDT Investments
  { currency: 'USDT', amount: 1000, daysAgo: 35 },
  { currency: 'USDT', amount: 500, daysAgo: 15 },
  { currency: 'USDT', amount: 2000, daysAgo: 75 },
  { currency: 'USDT', amount: 750, daysAgo: 100 },
  { currency: 'USDT', amount: 300, daysAgo: 3 },

  // BNB Investments
  { currency: 'BNB', amount: 20, daysAgo: 25 },
  { currency: 'BNB', amount: 50, daysAgo: 50 },
  { currency: 'BNB', amount: 15, daysAgo: 1 },

  // SOL Investments
  { currency: 'SOL', amount: 100, daysAgo: 40 },
  { currency: 'SOL', amount: 250, daysAgo: 80 },
  { currency: 'SOL', amount: 75, daysAgo: 2 },

  // TRX Investments
  { currency: 'TRX', amount: 10000, daysAgo: 55 },
  { currency: 'TRX', amount: 5000, daysAgo: 18 },
  { currency: 'TRX', amount: 15000, daysAgo: 110 },

  // DOGE Investments
  { currency: 'DOGE', amount: 5000, daysAgo: 28 },
  { currency: 'DOGE', amount: 2500, daysAgo: 1 },
  { currency: 'DOGE', amount: 7500, daysAgo: 65 }
];

class InvestmentTestDataCreator {
  constructor() {
    this.authToken = null;
    this.userId = null;
  }

  // Helper function to make API requests
  async apiRequest(endpoint, method = 'GET', data = null, requireAuth = true) {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
    };

    if (requireAuth && this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    const config = {
      method,
      headers,
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error(`API request failed: ${method} ${endpoint}`, error);
      throw error;
    }
  }

  // Register test user
  async registerTestUser() {
    try {
      console.log('📝 Registering test user...');
      
      const userData = {
        firstName: TEST_USER.firstName,
        lastName: TEST_USER.lastName,
        email: TEST_USER.email,
        password: TEST_USER.password,
        confirmPassword: TEST_USER.password
      };

      const result = await this.apiRequest('/auth/register', 'POST', userData, false);
      console.log('✅ Test user registered successfully');
      return result;
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ Test user already exists, proceeding with login...');
        return null;
      }
      throw error;
    }
  }

  // Login test user
  async loginTestUser() {
    try {
      console.log('🔐 Logging in test user...');
      
      const loginData = {
        email: TEST_USER.email,
        password: TEST_USER.password
      };

      const result = await this.apiRequest('/auth/login', 'POST', loginData, false);
      this.authToken = result.token;
      this.userId = result.user?._id || result._id;
      
      console.log('✅ Test user logged in successfully');
      return result;
    } catch (error) {
      console.error('❌ Login failed:', error);
      throw error;
    }
  }

  // Create investment packages
  async createInvestmentPackages() {
    try {
      console.log('💰 Creating investment packages...');
      
      const createdPackages = [];
      
      for (let i = 0; i < TEST_INVESTMENTS.length; i++) {
        const investment = TEST_INVESTMENTS[i];
        
        try {
          console.log(`Creating ${investment.currency} investment: ${investment.amount} ${investment.currency}`);
          
          const packageData = {
            amount: investment.amount,
            currency: investment.currency,
            compoundEnabled: false
          };

          const result = await this.apiRequest('/investments/create', 'POST', packageData);
          createdPackages.push(result.data.package);
          
          // Small delay to avoid overwhelming the server
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          console.warn(`⚠️ Failed to create ${investment.currency} investment:`, error.message);
        }
      }

      console.log(`✅ Created ${createdPackages.length} investment packages`);
      return createdPackages;
    } catch (error) {
      console.error('❌ Error creating investment packages:', error);
      throw error;
    }
  }

  // Get investment summary
  async getInvestmentSummary() {
    try {
      console.log('📊 Fetching investment summary...');
      
      const result = await this.apiRequest('/investments/packages');
      const packages = result.data?.packages || [];
      
      // Calculate summary by currency
      const summary = packages.reduce((acc, pkg) => {
        if (!acc[pkg.currency]) {
          acc[pkg.currency] = {
            totalInvested: 0,
            totalEarned: 0,
            activePackages: 0,
            totalPackages: 0
          };
        }
        
        acc[pkg.currency].totalInvested += pkg.amount;
        acc[pkg.currency].totalEarned += pkg.totalEarned || 0;
        acc[pkg.currency].totalPackages += 1;
        
        if (pkg.status === 'active') {
          acc[pkg.currency].activePackages += 1;
        }
        
        return acc;
      }, {});

      console.log('\n📊 Investment Summary by Currency:');
      console.log('=====================================');
      Object.entries(summary).forEach(([currency, data]) => {
        console.log(`${currency}:`);
        console.log(`  Total Invested: ${data.totalInvested} ${currency}`);
        console.log(`  Total Earned: ${data.totalEarned.toFixed(6)} ${currency}`);
        console.log(`  Active Packages: ${data.activePackages}/${data.totalPackages}`);
        console.log('');
      });

      return summary;
    } catch (error) {
      console.error('❌ Error fetching investment summary:', error);
      throw error;
    }
  }

  // Main execution function
  async run() {
    try {
      console.log('🚀 Starting Investment Test Data Creation via API...');
      console.log('==================================================');

      // Register test user (if not exists)
      await this.registerTestUser();

      // Login test user
      await this.loginTestUser();

      // Create investment packages
      await this.createInvestmentPackages();

      // Get and display summary
      await this.getInvestmentSummary();

      console.log('\n🎉 Investment test data creation completed successfully!');
      console.log('\nYou can now:');
      console.log('1. Login with: <EMAIL> / TestPassword123!');
      console.log('2. Navigate to the Investments page');
      console.log('3. View the Investment Summary by Currency section');

    } catch (error) {
      console.error('❌ Error creating investment test data:', error);
    }
  }
}

// Export for use in different environments
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = InvestmentTestDataCreator;
} else {
  // Browser environment
  window.InvestmentTestDataCreator = InvestmentTestDataCreator;
}

// Auto-run if this script is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const creator = new InvestmentTestDataCreator();
  creator.run();
}

// For browser console usage:
// const creator = new InvestmentTestDataCreator();
// creator.run();
