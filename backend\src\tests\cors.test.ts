import request from 'supertest';
import express from 'express';
import { safariCompatibleCorsMiddleware, safariCorsDebugMiddleware } from '../middleware/safariCorsMiddleware';

describe('CORS Middleware Tests', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(safariCompatibleCorsMiddleware);
    app.use(safariCorsDebugMiddleware);
    
    // Test route
    app.get('/test', (req, res) => {
      res.json({ message: 'Test successful' });
    });
    
    app.options('/test', (req, res) => {
      res.status(200).end();
    });
  });

  describe('Preflight Requests', () => {
    it('should handle OPTIONS requests correctly', async () => {
      const response = await request(app)
        .options('/test')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET')
        .set('Access-Control-Request-Headers', 'Content-Type');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:3000');
      expect(response.headers['access-control-allow-credentials']).toBe('true');
      expect(response.headers['access-control-allow-methods']).toContain('GET');
    });

    it('should handle Safari preflight requests', async () => {
      const response = await request(app)
        .options('/test')
        .set('Origin', 'http://localhost:3000')
        .set('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type, Authorization');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:3000');
      expect(response.headers['access-control-max-age']).toBe('86400');
      expect(response.headers['vary']).toContain('Origin');
    });
  });

  describe('CORS Headers', () => {
    it('should set correct CORS headers for allowed origins', async () => {
      const response = await request(app)
        .get('/test')
        .set('Origin', 'http://localhost:3000');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:3000');
      expect(response.headers['access-control-allow-credentials']).toBe('true');
      expect(response.headers['cross-origin-resource-policy']).toBe('cross-origin');
    });

    it('should handle requests without origin', async () => {
      const response = await request(app)
        .get('/test');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-origin']).toBe('*');
    });

    it('should set Safari-specific headers', async () => {
      const response = await request(app)
        .get('/test')
        .set('Origin', 'http://localhost:3000')
        .set('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15');

      expect(response.status).toBe(200);
      expect(response.headers['vary']).toContain('Origin');
      expect(response.headers['cross-origin-resource-policy']).toBe('cross-origin');
    });
  });

  describe('Development vs Production', () => {
    it('should allow all origins in development mode', async () => {
      process.env.NODE_ENV = 'development';
      
      const response = await request(app)
        .get('/test')
        .set('Origin', 'http://unknown-origin.com');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-origin']).toBe('http://unknown-origin.com');
    });

    it('should restrict origins in production mode', async () => {
      process.env.NODE_ENV = 'production';
      
      const response = await request(app)
        .get('/test')
        .set('Origin', 'http://unknown-origin.com');

      expect(response.status).toBe(200);
      // Should not set the unknown origin
      expect(response.headers['access-control-allow-origin']).not.toBe('http://unknown-origin.com');
    });
  });

  describe('Allowed Headers', () => {
    it('should expose correct headers', async () => {
      const response = await request(app)
        .get('/test')
        .set('Origin', 'http://localhost:3000');

      expect(response.headers['access-control-expose-headers']).toContain('Content-Length');
      expect(response.headers['access-control-expose-headers']).toContain('Content-Type');
      expect(response.headers['access-control-expose-headers']).toContain('Content-Range');
    });

    it('should allow required request headers', async () => {
      const response = await request(app)
        .options('/test')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Headers', 'Content-Type, Authorization, X-API-Key');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-headers']).toContain('Content-Type');
      expect(response.headers['access-control-allow-headers']).toContain('Authorization');
      expect(response.headers['access-control-allow-headers']).toContain('X-API-Key');
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed origin headers gracefully', async () => {
      const response = await request(app)
        .get('/test')
        .set('Origin', 'not-a-valid-url');

      expect(response.status).toBe(200);
      // Should still work, just not set CORS headers for invalid origin
    });
  });

  afterEach(() => {
    // Reset environment
    delete process.env.NODE_ENV;
  });
});
