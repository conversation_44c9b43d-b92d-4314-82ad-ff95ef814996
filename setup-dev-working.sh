#!/bin/bash

# CryptoYield Development Environment Setup (Working Version)
# This script sets up a development environment without authentication complexity

set -e

echo "🚀 Setting up CryptoYield Development Environment (Working Version)..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[SETUP]${NC} $1"
}

# Step 1: Check prerequisites
print_header "Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

print_success "Docker and Docker Compose are available"

# Step 2: Stop any existing services
print_header "Stopping any existing services..."
docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
docker-compose -f docker-compose.dev-simple.yml down 2>/dev/null || true
docker-compose -f docker-compose.dev-working.yml down 2>/dev/null || true
docker-compose -f docker-compose.mongo.yml down 2>/dev/null || true

# Step 3: Build backend development image
print_header "Building backend development image..."
docker-compose -f docker-compose.dev-working.yml build backend

# Step 4: Start services
print_header "Starting development services..."
docker-compose -f docker-compose.dev-working.yml up -d

# Step 5: Wait for services to be ready
print_header "Waiting for services to be ready..."

# Wait for MongoDB
print_status "Waiting for MongoDB to be healthy..."
timeout=120
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.dev-working.yml ps mongodb | grep -q "healthy"; then
        print_success "MongoDB is healthy"
        break
    fi
    
    print_status "Waiting for MongoDB... ($elapsed/$timeout seconds)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

if [ $elapsed -ge $timeout ]; then
    print_error "MongoDB failed to become healthy within $timeout seconds"
    print_status "Checking MongoDB logs..."
    docker-compose -f docker-compose.dev-working.yml logs mongodb
    exit 1
fi

# Step 6: Initialize replica set
print_header "Initializing MongoDB replica set..."
sleep 10

docker exec cryptoyield-mongodb-dev mongosh --eval "
try {
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set already active: ' + status.set);
    }
} catch (e) {
    if (e.message.includes('no replset config')) {
        rs.initiate({
            _id: 'rs0',
            members: [{ _id: 0, host: 'mongodb:27017' }]
        });
        print('✅ Replica set initialized successfully');
    } else {
        print('⚠️ Replica set status: ' + e.message);
    }
}
" 2>/dev/null || print_warning "Replica set initialization may need more time"

# Step 7: Wait for replica set to stabilize
print_status "Waiting for replica set to stabilize..."
sleep 30

# Step 8: Test services
print_header "Testing services..."

# Test MongoDB
if docker exec cryptoyield-mongodb-dev mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
    print_success "✅ MongoDB is accessible"
else
    print_warning "⚠️ MongoDB may still be initializing"
fi

# Test Redis
if docker exec cryptoyield-redis-dev redis-cli ping 2>/dev/null | grep -q "PONG"; then
    print_success "✅ Redis is accessible"
else
    print_warning "⚠️ Redis connection issue"
fi

# Test Backend
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/health 2>/dev/null || echo "000")
if [[ "$backend_health" == "200" ]]; then
    print_success "✅ Backend is accessible"
else
    print_warning "⚠️ Backend may still be starting (HTTP $backend_health)"
fi

# Test Mongo Express
mongo_express_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
if [[ "$mongo_express_health" == "200" || "$mongo_express_health" == "401" ]]; then
    print_success "✅ Mongo Express is accessible"
else
    print_warning "⚠️ Mongo Express may still be starting (HTTP $mongo_express_health)"
fi

# Step 9: Test transaction capability
print_header "Testing transaction capability..."
docker exec cryptoyield-mongodb-dev mongosh --eval "
try {
    const session = db.getMongo().startSession();
    session.startTransaction();
    
    const testDb = session.getDatabase('cryptoyield_dev_test');
    testDb.transactionTest.insertOne({
        test: 'dev_environment_setup',
        timestamp: new Date(),
        environment: 'development'
    }, {session: session});
    
    session.commitTransaction();
    session.endSession();
    
    print('✅ Transaction test successful');
} catch (e) {
    print('❌ Transaction test failed: ' + e.message);
}
" 2>/dev/null || print_warning "Transaction test may need more time"

# Step 10: Display final information
echo ""
print_success "🎉 Development environment setup completed!"
echo ""
echo "📋 Service Information:"
echo "  🖥️  Backend API: http://localhost:5000"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🔗 Connection Details:"
echo "  📝 MongoDB URI: mongodb://localhost:27017/cryptoyield?replicaSet=rs0"
echo "  🔑 Redis URI: redis://localhost:6379"
echo ""
echo "📁 Development Features:"
echo "  ✅ Live code editing (./backend/src mounted as volume)"
echo "  ✅ Hot-reload enabled (nodemon + ts-node)"
echo "  ✅ Transaction support (MongoDB replica set)"
echo "  ✅ Database admin interface (Mongo Express)"
echo "  ✅ Proper networking between services"
echo ""

# Show container status
print_header "Final container status:"
docker-compose -f docker-compose.dev-working.yml ps

echo ""
print_success "🚀 Your development environment is ready!"
echo ""
echo "📝 Next steps:"
echo "  1. Edit backend code in ./backend/src/ - changes will be reflected immediately"
echo "  2. View logs: docker-compose -f docker-compose.dev-working.yml logs -f backend"
echo "  3. Access database: http://localhost:8081"
echo "  4. Test API: curl http://localhost:5000/api/health"
echo ""
echo "🔧 Development Commands:"
echo "  • Start: docker-compose -f docker-compose.dev-working.yml up -d"
echo "  • Stop: docker-compose -f docker-compose.dev-working.yml down"
echo "  • Logs: docker-compose -f docker-compose.dev-working.yml logs -f backend"
echo "  • Shell: docker exec -it cryptoyield-backend-dev /bin/bash"
echo ""
print_success "Happy coding! 🎯"
