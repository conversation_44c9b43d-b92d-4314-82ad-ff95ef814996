#!/usr/bin/env npx ts-node

/**
 * Standalone Test Script for Daily Interest Payment Feature
 * 
 * This script tests the interest calculation service's processAllActivePackages() method
 * and can be executed directly without adding it to package.json.
 * 
 * Usage: npx ts-node scripts/test-daily-interest.ts
 * 
 * Features:
 * - Tests daily interest calculation for active investment packages
 * - Creates sample data if no active packages exist
 * - Provides detailed console output with results
 * - Includes proper error handling and logging
 * - Connects to database using existing configuration
 * - Optional cleanup of test data
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import * as path from 'path';
import * as crypto from 'crypto';

// Load environment variables
config({ path: path.join(__dirname, '../.env.docker') });

// Import models and services
import '../src/models/userModel';
import '../src/models/walletModel';
import '../src/models/investmentPackageModel';
import '../src/models/transactionModel';
import '../src/models/paymentHistoryModel';
import '../src/models/auditTrailModel';

import User from '../src/models/userModel';
import Wallet from '../src/models/walletModel';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';
import interestCalculationService from '../src/services/interestCalculationService';
import { db } from '../src/config/database';
import { logger } from '../src/utils/logger';

// Test configuration
interface TestConfig {
  createSampleData: boolean;
  cleanupAfterTest: boolean;
  numberOfTestPackages: number;
  testAmounts: number[];
  testCurrencies: string[];
}

const TEST_CONFIG: TestConfig = {
  createSampleData: true,
  cleanupAfterTest: false, // Set to true to cleanup test data
  numberOfTestPackages: 3,
  testAmounts: [1000, 5000, 10000],
  testCurrencies: ['USDT', 'BTC', 'ETH']
};

// Store created test data for cleanup
const testDataIds = {
  users: [] as string[],
  wallets: [] as string[],
  packages: [] as string[],
  transactions: [] as string[]
};

/**
 * Generate unique test email
 */
function generateTestEmail(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `test-interest-${timestamp}-${random}@example.com`;
}

/**
 * Generate unique package ID
 */
function generatePackageId(): string {
  return `PKG-${Date.now()}-${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
}

/**
 * Generate package hash
 */
function generatePackageHash(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Create test user
 */
async function createTestUser(): Promise<any> {
  const userData = {
    email: generateTestEmail(),
    password: 'Test123!@#',
    firstName: 'Test',
    lastName: 'User',
    emailVerified: true,
    kycVerified: true,
    referralCode: crypto.randomBytes(8).toString('hex').toUpperCase()
  };

  const user = await User.create(userData);
  testDataIds.users.push(user._id.toString());
  
  console.log(`✅ Created test user: ${user.email} (ID: ${user._id})`);
  return user;
}

/**
 * Create test wallet for user
 */
async function createTestWallet(userId: mongoose.Types.ObjectId): Promise<any> {
  const walletData = {
    userId,
    assets: [
      {
        symbol: 'USDT',
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'interest' as const
      },
      {
        symbol: 'BTC',
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'interest' as const
      },
      {
        symbol: 'ETH',
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'interest' as const
      }
    ],
    totalCommissionEarned: 0,
    totalInterestEarned: 0
  };

  const wallet = await Wallet.create(walletData);
  testDataIds.wallets.push(wallet._id.toString());
  
  console.log(`✅ Created test wallet for user ${userId} (Wallet ID: ${wallet._id})`);
  return wallet;
}

/**
 * Create test transaction
 */
async function createTestTransaction(
  userId: mongoose.Types.ObjectId,
  walletId: mongoose.Types.ObjectId,
  amount: number,
  currency: string
): Promise<any> {
  const transactionData = {
    userId,
    walletId,
    type: 'deposit',
    asset: currency,
    amount,
    status: 'completed',
    description: `Test deposit for interest calculation - ${amount} ${currency}`,
    metadata: {
      testData: true,
      createdForInterestTest: true
    }
  };

  const transaction = await Transaction.create(transactionData);
  testDataIds.transactions.push(transaction._id.toString());
  
  console.log(`✅ Created test transaction: ${amount} ${currency} (ID: ${transaction._id})`);
  return transaction;
}

/**
 * Create test investment package
 */
async function createTestInvestmentPackage(
  userId: mongoose.Types.ObjectId,
  transactionId: mongoose.Types.ObjectId,
  amount: number,
  currency: string
): Promise<any> {
  const now = new Date();
  const activatedAt = new Date(now.getTime() - (Math.random() * 5 + 1) * 24 * 60 * 60 * 1000); // 1-5 days ago

  const packageData = {
    userId,
    transactionId,
    packageId: generatePackageId(),
    amount,
    currency,
    status: 'active',
    createdAt: activatedAt,
    activatedAt,
    dailyInterest: 0,
    totalEarned: 0,
    accumulatedInterest: 0,
    interestRate: 0.01, // 1% daily
    lastCalculatedAt: null,
    lastInterestDistribution: null,
    compoundEnabled: false,
    emergencyWithdrawFee: 0.05,
    packageHash: generatePackageHash(),
    activeDays: 0,
    totalDays: 365,
    roi: 0,
    autoCreated: false,
    minimumWithdrawalUSDT: 50,
    realTimeUSDTValue: 0,
    withdrawableInterest: 0,
    principalLocked: true
  };

  const investmentPackage = await InvestmentPackage.create(packageData);
  testDataIds.packages.push(investmentPackage._id.toString());
  
  console.log(`✅ Created test investment package: ${amount} ${currency} (ID: ${investmentPackage._id})`);
  return investmentPackage;
}

/**
 * Create sample test data
 */
async function createSampleTestData(): Promise<void> {
  console.log('\n🔧 Creating sample test data...');

  for (let i = 0; i < TEST_CONFIG.numberOfTestPackages; i++) {
    try {
      // Create test user
      const user = await createTestUser();

      // Create test wallet
      const wallet = await createTestWallet(user._id);

      // Create test transaction and investment package
      const amount = TEST_CONFIG.testAmounts[i % TEST_CONFIG.testAmounts.length];
      const currency = TEST_CONFIG.testCurrencies[i % TEST_CONFIG.testCurrencies.length];

      const transaction = await createTestTransaction(user._id, wallet._id, amount, currency);
      const investmentPackage = await createTestInvestmentPackage(user._id, transaction._id, amount, currency);

      console.log(`✅ Test package ${i + 1}/${TEST_CONFIG.numberOfTestPackages} created successfully`);

    } catch (error) {
      console.error(`❌ Error creating test package ${i + 1}:`, error);
      throw error;
    }
  }

  console.log(`✅ Successfully created ${TEST_CONFIG.numberOfTestPackages} test investment packages`);
}

/**
 * Check for existing active packages
 */
async function checkExistingActivePackages(): Promise<any[]> {
  try {
    const activePackages = await InvestmentPackage.find({
      status: 'active',
      activatedAt: { $ne: null }
    }).populate('userId', 'email firstName lastName');

    console.log(`📊 Found ${activePackages.length} existing active investment packages`);

    if (activePackages.length > 0) {
      console.log('\n📋 Existing active packages:');
      activePackages.forEach((pkg, index) => {
        const user = pkg.userId as any;
        const userEmail = user?.email || 'Unknown';
        console.log(`  ${index + 1}. Package ${pkg.packageId}: ${pkg.amount} ${pkg.currency} (User: ${userEmail})`);
      });
    }

    return activePackages;
  } catch (error) {
    console.error('❌ Error checking existing packages:', error);
    throw error;
  }
}

/**
 * Test the daily interest calculation
 */
async function testDailyInterestCalculation(): Promise<void> {
  console.log('\n🚀 Starting daily interest calculation test...');

  try {
    const startTime = Date.now();

    // Run the interest calculation
    const summary = await interestCalculationService.processAllActivePackages();

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Display detailed results
    console.log('\n📊 DAILY INTEREST CALCULATION RESULTS');
    console.log('=====================================');
    console.log(`📦 Total packages processed: ${summary.totalPackages}`);
    console.log(`✅ Successful calculations: ${summary.successfulCalculations}`);
    console.log(`❌ Failed calculations: ${summary.failedCalculations}`);
    console.log(`💰 Total interest paid: ${summary.totalInterestPaid.toFixed(6)}`);
    console.log(`⏱️  Processing duration: ${duration}ms`);
    console.log(`🕐 Timestamp: ${summary.timestamp.toISOString()}`);

    // Show success rate
    if (summary.totalPackages > 0) {
      const successRate = (summary.successfulCalculations / summary.totalPackages * 100).toFixed(2);
      console.log(`📈 Success rate: ${successRate}%`);
    }

    // Display errors if any
    if (summary.errors && summary.errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:');
      summary.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. Package ${error.packageId}: ${error.error}`);
      });
    }

    // Display performance metrics
    if (summary.totalPackages > 0) {
      const avgTimePerPackage = duration / summary.totalPackages;
      console.log(`\n⚡ PERFORMANCE METRICS:`);
      console.log(`   Average time per package: ${avgTimePerPackage.toFixed(2)}ms`);
      console.log(`   Packages per second: ${(1000 / avgTimePerPackage).toFixed(2)}`);
    }

    console.log('\n✅ Daily interest calculation test completed successfully!');

  } catch (error) {
    console.error('\n❌ Daily interest calculation test failed:', error);
    throw error;
  }
}

/**
 * Cleanup test data
 */
async function cleanupTestData(): Promise<void> {
  if (!TEST_CONFIG.cleanupAfterTest) {
    console.log('\n🔧 Cleanup disabled - test data will remain in database');
    return;
  }

  console.log('\n🧹 Cleaning up test data...');

  try {
    // Delete test investment packages
    if (testDataIds.packages.length > 0) {
      const deletedPackages = await InvestmentPackage.deleteMany({
        _id: { $in: testDataIds.packages.map(id => new mongoose.Types.ObjectId(id)) }
      });
      console.log(`✅ Deleted ${deletedPackages.deletedCount} test investment packages`);
    }

    // Delete test transactions
    if (testDataIds.transactions.length > 0) {
      const deletedTransactions = await Transaction.deleteMany({
        _id: { $in: testDataIds.transactions.map(id => new mongoose.Types.ObjectId(id)) }
      });
      console.log(`✅ Deleted ${deletedTransactions.deletedCount} test transactions`);
    }

    // Delete test wallets
    if (testDataIds.wallets.length > 0) {
      const deletedWallets = await Wallet.deleteMany({
        _id: { $in: testDataIds.wallets.map(id => new mongoose.Types.ObjectId(id)) }
      });
      console.log(`✅ Deleted ${deletedWallets.deletedCount} test wallets`);
    }

    // Delete test users
    if (testDataIds.users.length > 0) {
      const deletedUsers = await User.deleteMany({
        _id: { $in: testDataIds.users.map(id => new mongoose.Types.ObjectId(id)) }
      });
      console.log(`✅ Deleted ${deletedUsers.deletedCount} test users`);
    }

    console.log('✅ Test data cleanup completed successfully');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    // Don't throw error during cleanup to avoid masking main test results
  }
}

/**
 * Display test configuration
 */
function displayTestConfiguration(): void {
  console.log('\n⚙️  TEST CONFIGURATION');
  console.log('=====================');
  console.log(`Create sample data: ${TEST_CONFIG.createSampleData ? '✅ Yes' : '❌ No'}`);
  console.log(`Cleanup after test: ${TEST_CONFIG.cleanupAfterTest ? '✅ Yes' : '❌ No'}`);
  console.log(`Number of test packages: ${TEST_CONFIG.numberOfTestPackages}`);
  console.log(`Test amounts: ${TEST_CONFIG.testAmounts.join(', ')}`);
  console.log(`Test currencies: ${TEST_CONFIG.testCurrencies.join(', ')}`);
  console.log(`Database URI: ${process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyieldhub'}`);
}

/**
 * Main test execution function
 */
async function main(): Promise<void> {
  console.log('🧪 DAILY INTEREST PAYMENT TEST SCRIPT');
  console.log('=====================================');
  console.log('This script tests the daily interest calculation functionality');
  console.log('for active investment packages in the CryptoYield platform.\n');

  displayTestConfiguration();

  try {
    // Connect to database
    console.log('\n🔌 Connecting to database...');
    await db.connect();
    console.log('✅ Database connection established');

    // Check existing active packages
    const existingPackages = await checkExistingActivePackages();

    // Create sample data if needed and configured
    if (TEST_CONFIG.createSampleData && existingPackages.length === 0) {
      await createSampleTestData();
    } else if (existingPackages.length === 0) {
      console.log('\n⚠️  No active investment packages found and sample data creation is disabled.');
      console.log('   Set TEST_CONFIG.createSampleData = true to create test data automatically.');
      console.log('   Or create some active investment packages manually before running this test.');
      return;
    }

    // Run the daily interest calculation test
    await testDailyInterestCalculation();

    // Cleanup test data if configured
    await cleanupTestData();

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('\n💥 Test failed with error:', error);

    // Try to cleanup even if test failed
    if (TEST_CONFIG.cleanupAfterTest) {
      console.log('\n🧹 Attempting cleanup after error...');
      await cleanupTestData();
    }

    process.exit(1);
  } finally {
    // Close database connection
    try {
      await db.closeConnection();
      console.log('✅ Database connection closed');
    } catch (error) {
      console.error('❌ Error closing database connection:', error);
    }
  }
}

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  process.exit(1);
});

/**
 * Handle process termination
 */
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Received SIGINT. Gracefully shutting down...');

  try {
    if (TEST_CONFIG.cleanupAfterTest) {
      console.log('🧹 Cleaning up test data before exit...');
      await cleanupTestData();
    }

    await db.closeConnection();
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
});

// Execute the main function
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
