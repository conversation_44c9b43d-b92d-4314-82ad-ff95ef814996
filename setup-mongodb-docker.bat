@echo off
setlocal enabledelayedexpansion

REM MongoDB Docker Setup Script for CryptoYield (Windows)
REM This script sets up MongoDB with replica set support for transactions

echo 🚀 Setting up MongoDB with Docker for CryptoYield...

REM Check if Docker is running
docker info >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ERROR: Docker is not running. Please start Docker and try again.
    exit /b 1
)
echo ✅ Docker is running

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ERROR: docker-compose is not installed. Please install docker-compose and try again.
    exit /b 1
)
echo ✅ docker-compose is available

REM Create data directories
echo 📁 Creating data directories...
if not exist "data" mkdir data
if not exist "data\mongodb" mkdir data\mongodb
if not exist "data\mongodb-config" mkdir data\mongodb-config
if not exist "data\redis" mkdir data\redis
echo ✅ Data directories created

REM Check if keyfile exists
if not exist "mongodb-keyfile\mongodb-keyfile" (
    echo ❌ ERROR: MongoDB keyfile not found. Please ensure mongodb-keyfile\mongodb-keyfile exists.
    exit /b 1
)
echo ✅ MongoDB keyfile found

REM Stop any existing containers
echo 🛑 Stopping any existing containers...
docker-compose -f docker-compose.mongo.yml down --remove-orphans 2>nul

REM Remove existing volumes for clean setup
echo ⚠️ Removing existing volumes to ensure clean setup...
docker volume rm cryptoyield_mongodb_data 2>nul
docker volume rm cryptoyield_mongodb_config 2>nul
docker volume rm cryptoyield_redis_data 2>nul

REM Start MongoDB first
echo 🗄️ Starting MongoDB...
docker-compose -f docker-compose.mongo.yml up -d mongodb

REM Wait for MongoDB to be healthy
echo ⏳ Waiting for MongoDB to be healthy...
set timeout=300
set elapsed=0
set interval=10

:wait_mongodb
if !elapsed! GEQ !timeout! (
    echo ❌ ERROR: MongoDB failed to become healthy within !timeout! seconds
    echo 📋 Checking MongoDB logs...
    docker-compose -f docker-compose.mongo.yml logs mongodb
    exit /b 1
)

docker-compose -f docker-compose.mongo.yml ps mongodb | findstr "healthy" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ MongoDB is healthy
    goto mongodb_ready
)

echo ⏳ Waiting for MongoDB... (!elapsed!/!timeout! seconds)
timeout /t !interval! /nobreak >nul
set /a elapsed=!elapsed!+!interval!
goto wait_mongodb

:mongodb_ready

REM Test MongoDB connection and replica set
echo 🔍 Testing MongoDB connection and replica set...
docker exec cryptoyield-mongodb mongosh --quiet --eval "try { db.adminCommand('ping'); print('✅ MongoDB connection successful'); const status = rs.status(); if (status.ok === 1) { print('✅ Replica set is active: ' + status.set); const primary = status.members.find(m => m.stateStr === 'PRIMARY'); if (primary) { print('✅ Primary member found: ' + primary.name); } else { print('⚠️ No primary member found yet'); } } else { print('❌ Replica set not ready'); } } catch (e) { print('❌ Error: ' + e.message); exit(1); }"

REM Test transaction support
echo 🔄 Testing transaction support...
docker exec cryptoyield-mongodb mongosh --quiet --eval "try { const session = db.getMongo().startSession(); session.startTransaction(); const testDb = session.getDatabase('test'); testDb.transactionTest.insertOne({test: 'transaction', timestamp: new Date()}, {session: session}); session.commitTransaction(); session.endSession(); print('✅ Transaction test successful'); } catch (e) { print('❌ Transaction test failed: ' + e.message); exit(1); }"

REM Start Redis
echo 🔴 Starting Redis...
docker-compose -f docker-compose.mongo.yml up -d redis

REM Wait for Redis to be ready
echo ⏳ Waiting for Redis to be ready...
timeout /t 10 /nobreak >nul

REM Test Redis connection
echo 🔍 Testing Redis connection...
docker exec cryptoyield-redis redis-cli ping | findstr "PONG" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Redis is ready
) else (
    echo ❌ ERROR: Redis is not responding
    exit /b 1
)

REM Start Mongo Express
echo 🌐 Starting Mongo Express...
docker-compose -f docker-compose.mongo.yml up -d mongo-express

REM Wait for Mongo Express to be ready
echo ⏳ Waiting for Mongo Express to be ready...
timeout /t 20 /nobreak >nul

REM Check if Mongo Express is accessible
curl -s -o nul -w "%%{http_code}" http://localhost:8081 | findstr "200" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Mongo Express is accessible at http://localhost:8081
    echo 🔑 Login credentials: admin / admin123
) else (
    echo ⚠️ Mongo Express may not be ready yet. Check http://localhost:8081 in a few minutes.
)

REM Display final status
echo.
echo ✅ MongoDB Docker setup completed successfully!
echo.
echo 📋 Service Status:
echo   🗄️  MongoDB: localhost:27017 (replica set: rs0)
echo   🔴 Redis: localhost:6379
echo   🌐 Mongo Express: http://localhost:8081 (admin/admin123)
echo.
echo 🔗 Connection Details:
echo   📝 MongoDB URI: *******************************************************************************************^&replicaSet=rs0
echo   🔑 Redis URI: redis://localhost:6379
echo.
echo ✅ Your backend application can now connect to MongoDB with transaction support!

pause
