@echo off
setlocal enabledelayedexpansion

REM CryptoYield Development Docker Management Script for Windows
REM This script helps manage the development Docker environment with hot reload

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Function to print colored output
:print_status
echo %BLUE%[INFO]%NC% %~1
goto :eof

:print_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM Function to check if Docker is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker Desktop first."
    exit /b 1
)
goto :eof

REM Function to check if docker-compose is available
:check_docker_compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "docker-compose is not installed or not in PATH."
    exit /b 1
)
goto :eof

REM Function to start development environment
:start_dev
call :print_status "Starting CryptoYield development environment..."

REM Check prerequisites
call :check_docker
if errorlevel 1 exit /b 1

call :check_docker_compose
if errorlevel 1 exit /b 1

REM Create necessary directories
call :print_status "Creating necessary directories..."
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "backend\logs" mkdir "backend\logs"

REM Start services
call :print_status "Starting Docker services with hot reload..."
docker-compose -f docker-compose.dev.yml up -d

REM Wait for services to be ready
call :print_status "Waiting for services to be ready..."
timeout /t 10 /nobreak >nul

REM Check service status
call :print_status "Checking service status..."
docker-compose -f docker-compose.dev.yml ps

call :print_success "Development environment started successfully!"
call :print_status "Services available at:"
echo   🌐 Frontend:        http://localhost:3005
echo   🔧 Backend API:     http://localhost:5001/api
echo   🔍 API Health:      http://localhost:5001/api/health
echo   🗄️  MongoDB:        mongodb://localhost:27017
echo   📊 Mongo Express:   http://localhost:8081 (admin/admin123)
echo   🔴 Redis:           redis://localhost:6379
echo.
call :print_status "Hot reload is enabled for both frontend and backend!"
call :print_status "Edit files in .\frontend\src or .\backend\src and see changes instantly."
goto :eof

REM Function to stop development environment
:stop_dev
call :print_status "Stopping CryptoYield development environment..."

call :check_docker_compose
if errorlevel 1 exit /b 1

docker-compose -f docker-compose.dev.yml down

call :print_success "Development environment stopped successfully!"
goto :eof

REM Function to restart development environment
:restart_dev
call :print_status "Restarting CryptoYield development environment..."
call :stop_dev
timeout /t 2 /nobreak >nul
call :start_dev
goto :eof

REM Function to view logs
:logs_dev
call :check_docker_compose
if errorlevel 1 exit /b 1

if "%~2"=="" (
    call :print_status "Showing logs for all services..."
    docker-compose -f docker-compose.dev.yml logs -f
) else (
    call :print_status "Showing logs for service: %~2"
    docker-compose -f docker-compose.dev.yml logs -f %~2
)
goto :eof

REM Function to show status
:status_dev
call :check_docker_compose
if errorlevel 1 exit /b 1

call :print_status "Development environment status:"
docker-compose -f docker-compose.dev.yml ps

echo.
call :print_status "Service health checks:"

REM Check backend health
curl -s http://localhost:5001/api/health >nul 2>&1
if errorlevel 1 (
    call :print_warning "❌ Backend API is not responding"
) else (
    call :print_success "✅ Backend API is healthy"
)

REM Check frontend
curl -s http://localhost:3005 >nul 2>&1
if errorlevel 1 (
    call :print_warning "❌ Frontend is not responding"
) else (
    call :print_success "✅ Frontend is healthy"
)

REM Check MongoDB
docker exec cryptoyield-mongodb-dev mongosh --eval "db.adminCommand('ping')" >nul 2>&1
if errorlevel 1 (
    call :print_warning "❌ MongoDB is not responding"
) else (
    call :print_success "✅ MongoDB is healthy"
)

REM Check Redis
docker exec cryptoyield-redis-dev redis-cli ping >nul 2>&1
if errorlevel 1 (
    call :print_warning "❌ Redis is not responding"
) else (
    call :print_success "✅ Redis is healthy"
)
goto :eof

REM Function to clean up development environment
:clean_dev
call :print_warning "This will remove all containers, networks, and volumes for development environment."
set /p "confirm=Are you sure? (y/N): "

if /i "!confirm!"=="y" (
    call :print_status "Cleaning up development environment..."
    
    call :check_docker_compose
    if errorlevel 1 exit /b 1
    
    REM Stop and remove containers, networks, and volumes
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans
    
    REM Remove development images
    docker image prune -f --filter label=com.docker.compose.project=cryptoyield
    
    call :print_success "Development environment cleaned up successfully!"
) else (
    call :print_status "Cleanup cancelled."
)
goto :eof

REM Function to rebuild services
:rebuild_dev
call :print_status "Rebuilding development services..."

call :check_docker_compose
if errorlevel 1 exit /b 1

REM Stop services
docker-compose -f docker-compose.dev.yml down

REM Rebuild images
docker-compose -f docker-compose.dev.yml build --no-cache

REM Start services
docker-compose -f docker-compose.dev.yml up -d

call :print_success "Development services rebuilt and started!"
goto :eof

REM Function to show help
:show_help
echo CryptoYield Development Docker Management Script
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   start     Start the development environment with hot reload
echo   stop      Stop the development environment
echo   restart   Restart the development environment
echo   status    Show status of all services
echo   logs      Show logs for all services (or specify service name)
echo   clean     Clean up all development containers and volumes
echo   rebuild   Rebuild and restart all services
echo   help      Show this help message
echo.
echo Examples:
echo   %~nx0 start                 # Start development environment
echo   %~nx0 logs backend          # Show backend logs
echo   %~nx0 logs frontend         # Show frontend logs
echo   %~nx0 status                # Check service status
echo.
echo Services will be available at:
echo   Frontend:      http://localhost:3005
echo   Backend API:   http://localhost:5001/api
echo   Mongo Express: http://localhost:8081
goto :eof

REM Main script logic
if "%~1"=="start" (
    call :start_dev
) else if "%~1"=="stop" (
    call :stop_dev
) else if "%~1"=="restart" (
    call :restart_dev
) else if "%~1"=="status" (
    call :status_dev
) else if "%~1"=="logs" (
    call :logs_dev %*
) else if "%~1"=="clean" (
    call :clean_dev
) else if "%~1"=="rebuild" (
    call :rebuild_dev
) else if "%~1"=="help" (
    call :show_help
) else if "%~1"=="--help" (
    call :show_help
) else if "%~1"=="-h" (
    call :show_help
) else if "%~1"=="" (
    call :print_error "No command specified."
    echo.
    call :show_help
    exit /b 1
) else (
    call :print_error "Unknown command: %~1"
    echo.
    call :show_help
    exit /b 1
)
