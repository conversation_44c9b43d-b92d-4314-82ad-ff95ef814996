import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Badge,
  Divider,
  Icon,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Textarea,
  FormControl,
  FormLabel,
  Spinner,
  IconButton,
  Tooltip
} from '@chakra-ui/react';
import {
  FaCheck,
  FaTimes,
  FaUser,
  FaWallet,
  FaCalendarAlt,
  FaNetworkWired,
  FaHashtag,
  FaMoneyBillWave,
  FaCopy
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';
import adminWithdrawalService, { AdminWithdrawal } from '../../services/adminWithdrawalService';

interface WithdrawalDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  withdrawal: AdminWithdrawal | null;
  onStatusUpdate: () => void;
}

const WithdrawalDetailModal: React.FC<WithdrawalDetailModalProps> = ({
  isOpen,
  onClose,
  withdrawal,
  onStatusUpdate
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  
  const [isUpdating, setIsUpdating] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');

  if (!withdrawal) return null;

  const handleStatusUpdate = async (status: string) => {
    if (!withdrawal) return;

    setIsUpdating(true);
    try {
      const response = await adminWithdrawalService.updateWithdrawalStatus(withdrawal.id, {
        status,
        adminNotes: adminNotes || `Status updated to ${status} by admin`
      });

      toast({
        title: 'Success',
        description: response.message,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      onStatusUpdate();
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'yellow';
      case 'approved': return 'blue';
      case 'completed': return 'green';
      case 'rejected': return 'red';
      case 'failed': return 'red';
      default: return 'gray';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: 'Copied!',
        description: `${label} copied to clipboard`,
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    }).catch(() => {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy to clipboard',
        status: 'error',
        duration: 2000,
        isClosable: true,
      });
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg="#0B0E11"
        borderColor="#2B3139"
        borderWidth="1px"
        maxH="90vh"
        overflowY="auto"
      >
        <ModalHeader color="#F0B90B" borderBottomWidth="1px" borderColor="#2B3139">
          <HStack spacing={3}>
            <Icon
              as={getCryptoIcon(withdrawal.cryptocurrency)}
              color={getCryptoColor(withdrawal.cryptocurrency)}
              boxSize={6}
            />
            <Text>Withdrawal Details</Text>
            <Badge colorScheme={getStatusColor(withdrawal.status)} ml="auto">
              {withdrawal.status.toUpperCase()}
            </Badge>
          </HStack>
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />

        <ModalBody py={6}>
          <VStack spacing={6} align="stretch">
            {/* User Information */}
            <Box p={4} bg="#1E2329" borderRadius="md">
              <HStack mb={3}>
                <Icon as={FaUser} color="#F0B90B" />
                <Text color="#F0B90B" fontWeight="bold">User Information</Text>
              </HStack>
              <VStack align="stretch" spacing={2}>
                <Flex justify="space-between">
                  <Text color="#848E9C">Name:</Text>
                  <Text color="#EAECEF">{withdrawal.user?.name || 'Unknown User'}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Email:</Text>
                  <Text color="#EAECEF">{withdrawal.user?.email || 'No email'}</Text>
                </Flex>
                {withdrawal.user?.phoneNumber && (
                  <Flex justify="space-between">
                    <Text color="#848E9C">Phone:</Text>
                    <Text color="#EAECEF">{withdrawal.user.phoneNumber}</Text>
                  </Flex>
                )}
                {withdrawal.user?.country && (
                  <Flex justify="space-between">
                    <Text color="#848E9C">Country:</Text>
                    <Text color="#EAECEF">{withdrawal.user.country}</Text>
                  </Flex>
                )}
                <Flex justify="space-between" align="center">
                  <Text color="#848E9C">User ID:</Text>
                  <HStack>
                    <Text color="#EAECEF" fontFamily="monospace" fontSize="sm" maxW="200px" isTruncated>
                      {withdrawal.userId}
                    </Text>
                    <Tooltip label="Copy User ID">
                      <IconButton
                        aria-label="Copy user ID"
                        icon={<FaCopy />}
                        size="xs"
                        variant="ghost"
                        color="#848E9C"
                        _hover={{ color: "#F0B90B" }}
                        onClick={() => copyToClipboard(withdrawal.userId, 'User ID')}
                      />
                    </Tooltip>
                  </HStack>
                </Flex>
              </VStack>
            </Box>

            {/* Transaction Information */}
            <Box p={4} bg="#1E2329" borderRadius="md">
              <HStack mb={3}>
                <Icon as={FaMoneyBillWave} color="#F0B90B" />
                <Text color="#F0B90B" fontWeight="bold">Transaction Details</Text>
              </HStack>
              <VStack align="stretch" spacing={2}>
                <Flex justify="space-between" align="center">
                  <Text color="#848E9C">Transaction ID:</Text>
                  <HStack>
                    <Text color="#EAECEF" fontFamily="monospace" fontSize="sm" maxW="200px" isTruncated>
                      {withdrawal.id}
                    </Text>
                    <Tooltip label="Copy Transaction ID">
                      <IconButton
                        aria-label="Copy transaction ID"
                        icon={<FaCopy />}
                        size="xs"
                        variant="ghost"
                        color="#848E9C"
                        _hover={{ color: "#F0B90B" }}
                        onClick={() => copyToClipboard(withdrawal.id, 'Transaction ID')}
                      />
                    </Tooltip>
                  </HStack>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Cryptocurrency:</Text>
                  <HStack>
                    <Icon
                      as={getCryptoIcon(withdrawal.cryptocurrency)}
                      color={getCryptoColor(withdrawal.cryptocurrency)}
                      boxSize={4}
                    />
                    <Text color="#EAECEF">{withdrawal.cryptocurrency}</Text>
                  </HStack>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Withdrawal Type:</Text>
                  <Text color="#EAECEF">
                    {adminWithdrawalService.formatWithdrawalType(withdrawal.withdrawalType)}
                  </Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Amount:</Text>
                  <Text color="#EAECEF" fontWeight="bold">
                    {adminWithdrawalService.formatCurrency(withdrawal.amount || 0, withdrawal.cryptocurrency)}
                  </Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Fee:</Text>
                  <Text color="#EAECEF">
                    {adminWithdrawalService.formatCurrency(withdrawal.fee || 0, withdrawal.cryptocurrency)}
                  </Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Net Amount:</Text>
                  <Text color="#0ECB81" fontWeight="bold">
                    {adminWithdrawalService.formatCurrency(withdrawal.netAmount || 0, withdrawal.cryptocurrency)}
                  </Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">USD Value:</Text>
                  <Text color="#EAECEF">
                    {adminWithdrawalService.formatUSD(withdrawal.usdValue || 0)}
                  </Text>
                </Flex>
              </VStack>
            </Box>

            {/* Wallet Information */}
            <Box p={4} bg="#1E2329" borderRadius="md">
              <HStack mb={3}>
                <Icon as={FaWallet} color="#F0B90B" />
                <Text color="#F0B90B" fontWeight="bold">Wallet Information</Text>
              </HStack>
              <VStack align="stretch" spacing={2}>
                <Flex justify="space-between" align="center">
                  <Text color="#848E9C">Wallet Address:</Text>
                  <HStack>
                    <Text color="#EAECEF" fontFamily="monospace" fontSize="sm" maxW="250px" isTruncated>
                      {withdrawal.walletAddress}
                    </Text>
                    <Tooltip label="Copy Wallet Address">
                      <IconButton
                        aria-label="Copy wallet address"
                        icon={<FaCopy />}
                        size="xs"
                        variant="ghost"
                        color="#848E9C"
                        _hover={{ color: "#F0B90B" }}
                        onClick={() => copyToClipboard(withdrawal.walletAddress, 'Wallet Address')}
                      />
                    </Tooltip>
                  </HStack>
                </Flex>
                {withdrawal.network && (
                  <Flex justify="space-between">
                    <Text color="#848E9C">Network:</Text>
                    <Text color="#EAECEF">{withdrawal.network}</Text>
                  </Flex>
                )}
                {withdrawal.memo && (
                  <Flex justify="space-between">
                    <Text color="#848E9C">Memo/Tag:</Text>
                    <Text color="#EAECEF">{withdrawal.memo}</Text>
                  </Flex>
                )}
                {withdrawal.transactionHash && (
                  <Flex justify="space-between" align="center">
                    <Text color="#848E9C">Transaction Hash:</Text>
                    <HStack>
                      <Text color="#EAECEF" fontFamily="monospace" fontSize="sm" maxW="250px" isTruncated>
                        {withdrawal.transactionHash}
                      </Text>
                      <Tooltip label="Copy Transaction Hash">
                        <IconButton
                          aria-label="Copy transaction hash"
                          icon={<FaCopy />}
                          size="xs"
                          variant="ghost"
                          color="#848E9C"
                          _hover={{ color: "#F0B90B" }}
                          onClick={() => copyToClipboard(withdrawal.transactionHash!, 'Transaction Hash')}
                        />
                      </Tooltip>
                    </HStack>
                  </Flex>
                )}
              </VStack>
            </Box>

            {/* Timestamps */}
            <Box p={4} bg="#1E2329" borderRadius="md">
              <HStack mb={3}>
                <Icon as={FaCalendarAlt} color="#F0B90B" />
                <Text color="#F0B90B" fontWeight="bold">Timeline</Text>
              </HStack>
              <VStack align="stretch" spacing={2}>
                <Flex justify="space-between">
                  <Text color="#848E9C">Created:</Text>
                  <Text color="#EAECEF">{formatDate(withdrawal.createdAt)}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C">Updated:</Text>
                  <Text color="#EAECEF">{formatDate(withdrawal.updatedAt)}</Text>
                </Flex>
                {withdrawal.processedAt && (
                  <Flex justify="space-between">
                    <Text color="#848E9C">Processed:</Text>
                    <Text color="#EAECEF">{formatDate(withdrawal.processedAt)}</Text>
                  </Flex>
                )}
              </VStack>
            </Box>

            {/* Admin Notes */}
            {withdrawal.adminNotes && (
              <Box p={4} bg="#1E2329" borderRadius="md">
                <Text color="#F0B90B" fontWeight="bold" mb={2}>Admin Notes</Text>
                <Text color="#EAECEF" fontSize="sm">{withdrawal.adminNotes}</Text>
              </Box>
            )}

            {/* Metadata Information */}
            {withdrawal.metadata && (
              <Box p={4} bg="#1E2329" borderRadius="md">
                <Text color="#F0B90B" fontWeight="bold" mb={3}>Technical Details</Text>
                <VStack align="stretch" spacing={2}>
                  {withdrawal.metadata.conversionRate && withdrawal.metadata.conversionRate.rate && (
                    <Flex justify="space-between">
                      <Text color="#848E9C">USD Rate:</Text>
                      <Text color="#EAECEF">
                        ${withdrawal.metadata.conversionRate.rate.toLocaleString()}
                        {withdrawal.metadata.conversionRate.timestamp && (
                          <Text as="span" color="#848E9C" fontSize="xs" ml={1}>
                            ({new Date(withdrawal.metadata.conversionRate.timestamp).toLocaleString()})
                          </Text>
                        )}
                      </Text>
                    </Flex>
                  )}
                  {withdrawal.metadata.feeCalculation && (
                    <>
                      <Flex justify="space-between">
                        <Text color="#848E9C">Base Fee:</Text>
                        <Text color="#EAECEF">
                          {withdrawal.metadata.feeCalculation.baseFee || 0} {withdrawal.cryptocurrency}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C">Network Fee:</Text>
                        <Text color="#EAECEF">
                          {withdrawal.metadata.feeCalculation.networkFee || 0} {withdrawal.cryptocurrency}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C">Fee Percentage:</Text>
                        <Text color="#EAECEF">
                          {withdrawal.metadata.feeCalculation.feePercentage || 0}%
                        </Text>
                      </Flex>
                    </>
                  )}
                  {withdrawal.metadata.lockValidation && (
                    <Flex justify="space-between">
                      <Text color="#848E9C">Lock Status:</Text>
                      <Badge colorScheme={withdrawal.metadata.lockValidation.isLocked ? "red" : "green"}>
                        {withdrawal.metadata.lockValidation.isLocked ? "Locked" : "Unlocked"}
                      </Badge>
                    </Flex>
                  )}
                </VStack>
              </Box>
            )}

            {/* Status-specific alerts */}
            {withdrawal.status === 'pending' && (
              <Alert status="warning" borderRadius="md" bg="#F0B90B22">
                <AlertIcon color="#F0B90B" />
                <Box>
                  <AlertTitle color="#F0B90B">Pending Approval</AlertTitle>
                  <AlertDescription color="#EAECEF">
                    This withdrawal is waiting for admin approval. Review the details and approve or reject.
                  </AlertDescription>
                </Box>
              </Alert>
            )}

            {withdrawal.status === 'rejected' && (
              <Alert status="error" borderRadius="md" bg="#F8496022">
                <AlertIcon color="#F84960" />
                <Box>
                  <AlertTitle color="#F84960">Withdrawal Rejected</AlertTitle>
                  <AlertDescription color="#EAECEF">
                    This withdrawal has been rejected by an administrator.
                  </AlertDescription>
                </Box>
              </Alert>
            )}

            {withdrawal.status === 'approved' && (
              <Alert status="info" borderRadius="md" bg="#0ECB8122">
                <AlertIcon color="#0ECB81" />
                <Box>
                  <AlertTitle color="#0ECB81">Withdrawal Approved</AlertTitle>
                  <AlertDescription color="#EAECEF">
                    This withdrawal has been approved and is ready for processing. Mark as completed once the transaction is sent.
                  </AlertDescription>
                </Box>
              </Alert>
            )}

            {withdrawal.status === 'completed' && (
              <Alert status="success" borderRadius="md" bg="#0ECB8122">
                <AlertIcon color="#0ECB81" />
                <Box>
                  <AlertTitle color="#0ECB81">Withdrawal Completed</AlertTitle>
                  <AlertDescription color="#EAECEF">
                    This withdrawal has been successfully processed and completed.
                  </AlertDescription>
                </Box>
              </Alert>
            )}

            {/* Admin Notes Input for Actions */}
            {withdrawal.status === 'pending' && (
              <FormControl>
                <FormLabel color="#848E9C">Admin Notes (Optional)</FormLabel>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add notes about this decision..."
                  bg="#0B0E11"
                  borderColor="#2B3139"
                  color="#EAECEF"
                  _hover={{ borderColor: "#F0B90B" }}
                  rows={3}
                />
              </FormControl>
            )}
          </VStack>
        </ModalBody>

        <ModalFooter borderTopWidth="1px" borderColor="#2B3139">
          <HStack spacing={3} w="full">
            <Button
              variant="outline"
              onClick={onClose}
              borderColor="#2B3139"
              color="#EAECEF"
              _hover={{ borderColor: "#F0B90B" }}
              flex="1"
            >
              Close
            </Button>
            
            {withdrawal.status === 'pending' && (
              <>
                <Button
                  leftIcon={<FaTimes />}
                  colorScheme="red"
                  onClick={() => handleStatusUpdate('rejected')}
                  isLoading={isUpdating}
                  flex="1"
                >
                  Reject
                </Button>
                <Button
                  leftIcon={<FaCheck />}
                  colorScheme="green"
                  onClick={() => handleStatusUpdate('approved')}
                  isLoading={isUpdating}
                  flex="1"
                >
                  Approve
                </Button>
              </>
            )}
            
            {withdrawal.status === 'approved' && (
              <Button
                leftIcon={<FaCheck />}
                colorScheme="blue"
                onClick={() => handleStatusUpdate('completed')}
                isLoading={isUpdating}
                flex="1"
              >
                Mark Completed
              </Button>
            )}
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default WithdrawalDetailModal;
