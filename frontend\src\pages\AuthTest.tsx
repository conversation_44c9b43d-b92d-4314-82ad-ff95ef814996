import React from 'react';
import { <PERSON>, Container, <PERSON>ing, Text, Button, VStack, HStack, Badge, Code, Alert, AlertIcon } from '@chakra-ui/react';
import useAuth from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const AuthTest = () => {
  const { user, loading, login, logout } = useAuth();
  const navigate = useNavigate();

  const storedUser = localStorage.getItem('user');
  let parsedStoredUser = null;

  try {
    parsedStoredUser = storedUser ? JSON.parse(storedUser) : null;
  } catch (error) {
    console.error('Error parsing stored user:', error);
  }

  const handleTestLogin = async () => {
    try {
      console.log('🔄 Starting comprehensive test login...');

      // Clear any existing data first
      localStorage.removeItem('user');
      localStorage.removeItem('adminToken');

      const result = await login('<EMAIL>', 'Test123!@#');
      console.log('✅ Test login successful:', result);

      // Check if user was set
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        console.log('✅ User data stored successfully');
        alert('Login test successful! User data stored. Page will refresh.');
      } else {
        console.warn('⚠️ Login succeeded but no user data stored');
        alert('Login test partially successful - check console for details.');
      }

      // Force a page refresh to see updated state
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('❌ Test login failed:', error);
      alert(`Login test failed: ${error.message}`);
    }
  };

  const handleProfileNavigation = () => {
    navigate('/profile');
  };

  const handleTestAPI = async () => {
    try {
      console.log('🔄 Testing API connectivity...');
      // Use relative paths for Vite proxy in development
      const API_URL = import.meta.env.NODE_ENV === 'development' ? '/api' : (import.meta.env.VITE_API_URL || 'http://localhost:5000/api');
      console.log('API URL:', API_URL);

      // Test health endpoint
      const healthResponse = await fetch('/health'); // Use relative path for proxy
      const healthData = await healthResponse.json();
      console.log('✅ Health check:', healthData);

      // Test profile endpoint if user is logged in
      if (user) {
        console.log('🔄 Testing profile endpoint...');
        const profileResponse = await fetch(`${API_URL}/users/profile`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          console.log('✅ Profile data:', profileData);
        } else {
          console.log('❌ Profile request failed:', profileResponse.status, profileResponse.statusText);
        }
      }
    } catch (error) {
      console.error('❌ API test failed:', error);
    }
  };

  const handleDirectAxiosTest = async () => {
    try {
      console.log('🔄 Testing direct axios connection...');

      // Test health endpoint with axios using relative path for proxy
      console.log('Testing health endpoint...');
      const healthResponse = await axios.get('/health');
      console.log('✅ Health check response:', healthResponse.data);

      // Test login endpoint with axios using relative path for proxy
      console.log('Testing login endpoint...');
      const loginResponse = await axios.post('/api/users/login', {
        email: '<EMAIL>',
        password: 'Test123!@#'
      }, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Login response:', loginResponse.data);
      console.log('✅ Direct axios test successful!');
    } catch (error: any) {
      console.error('❌ Direct axios test failed:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        code: error.code
      });
    }
  };

  const handleSimpleFetchTest = async () => {
    try {
      console.log('🔄 Testing simple fetch...');
      alert('Starting simple fetch test...');

      // Test with basic fetch using relative path for proxy
      const response = await fetch('/health');
      const data = await response.json();

      console.log('✅ Simple fetch successful:', data);
      alert(`Simple fetch successful! Status: ${data.status}`);
    } catch (error: any) {
      console.error('❌ Simple fetch failed:', error);
      alert(`Simple fetch failed: ${error.message}`);
    }
  };

  const handleBasicTest = () => {
    console.log('🔄 Basic test function called');
    alert('Basic test function is working! JavaScript is executing.');
  };

  const handleEnvironmentTest = () => {
    const envInfo = {
      VITE_API_URL: import.meta.env.VITE_API_URL,
      NODE_ENV: import.meta.env.NODE_ENV,
      MODE: import.meta.env.MODE,
      DEV: import.meta.env.DEV,
      PROD: import.meta.env.PROD
    };

    console.log('🔧 Environment variables:', envInfo);
    alert(`Environment Test:\nVITE_API_URL: ${envInfo.VITE_API_URL}\nNODE_ENV: ${envInfo.NODE_ENV}\nMODE: ${envInfo.MODE}`);
  };

  const handleRealAPITest = async () => {
    try {
      console.log('🔄 Starting real API test...');
      console.log('🔧 Using API URL:', import.meta.env.VITE_API_URL);

      // Test health endpoint
      console.log('📤 Making health request...');
      const healthResponse = await fetch('/health', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      console.log('📥 Health response status:', healthResponse.status);
      const healthData = await healthResponse.json();
      console.log('📥 Health response data:', healthData);

      // Test login endpoint
      console.log('📤 Making login request...');
      const loginResponse = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Test123!@#'
        }),
      });

      console.log('📥 Login response status:', loginResponse.status);
      const loginData = await loginResponse.json();
      console.log('📥 Login response data:', loginData);

      // If login was successful, store the user data
      if (loginData.status === 'success' && loginData.data) {
        localStorage.setItem('user', JSON.stringify(loginData.data));
        alert(`API Test & Login Successful!\nHealth: ${healthData.status}\nLogin: ${loginData.status}\nUser data stored! Page will refresh.`);
        setTimeout(() => window.location.reload(), 1000);
      } else {
        alert(`API Test Successful!\nHealth: ${healthData.status}\nLogin: ${loginData.status}`);
      }

    } catch (error: any) {
      console.error('❌ Real API test failed:', error);
      alert(`API Test Failed: ${error.message}`);
    }
  };

  const handleForceLogin = async () => {
    try {
      console.log('🔄 Starting FORCE LOGIN with all methods...');

      // Clear existing data
      localStorage.removeItem('user');
      localStorage.removeItem('adminToken');

      // Method 1: Direct fetch to proxy
      try {
        console.log('📤 FORCE Method 1: Direct fetch to proxy...');
        const response = await fetch('/api/users/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'Test123!@#'
          }),
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ FORCE Method 1 successful:', data);

          if (data.status === 'success' && data.data) {
            localStorage.setItem('user', JSON.stringify(data.data));
            alert('🎉 FORCE LOGIN SUCCESSFUL! User logged in and data stored. Page will refresh.');
            setTimeout(() => window.location.reload(), 1000);
            return;
          }
        } else {
          console.warn('⚠️ FORCE Method 1 failed with status:', response.status);
        }
      } catch (error1) {
        console.warn('⚠️ FORCE Method 1 failed:', error1);
      }

      // Method 2: Direct axios to proxy
      try {
        console.log('📤 FORCE Method 2: Direct axios to proxy...');
        const response = await axios.post('/api/users/login', {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }, {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }
        });

        console.log('✅ FORCE Method 2 successful:', response.data);

        if (response.data.status === 'success' && response.data.data) {
          localStorage.setItem('user', JSON.stringify(response.data.data));
          alert('🎉 FORCE LOGIN SUCCESSFUL! User logged in and data stored. Page will refresh.');
          setTimeout(() => window.location.reload(), 1000);
          return;
        }
      } catch (error2) {
        console.warn('⚠️ FORCE Method 2 failed:', error2);
      }

      alert('❌ All FORCE LOGIN methods failed. Check console for details.');
    } catch (error: any) {
      console.error('❌ Force login failed:', error);
      alert(`Force login failed: ${error.message}`);
    }
  };

  return (
    <Container maxW="container.lg" py={8}>
      <VStack spacing={6} align="stretch">
        <Heading size="lg" color="gold.400">Authentication Test Page</Heading>

        <Alert status="info">
          <AlertIcon />
          This page helps debug authentication issues. Check the browser console for detailed logs.
        </Alert>

        <Box bg="gray.800" p={6} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <Heading size="md" mb={4} color="gold.400">AuthContext State</Heading>
          <VStack align="start" spacing={3}>
            <HStack>
              <Text fontWeight="bold">Loading:</Text>
              <Badge colorScheme={loading ? 'yellow' : 'green'}>
                {loading ? 'True' : 'False'}
              </Badge>
            </HStack>

            <HStack>
              <Text fontWeight="bold">User:</Text>
              <Badge colorScheme={user ? 'green' : 'red'}>
                {user ? 'Authenticated' : 'Not Authenticated'}
              </Badge>
            </HStack>

            {user && (
              <>
                <HStack>
                  <Text fontWeight="bold">Email:</Text>
                  <Text>{user.email}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">ID:</Text>
                  <Text>{user._id}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Name:</Text>
                  <Text>{user.firstName} {user.lastName}</Text>
                </HStack>
              </>
            )}
          </VStack>
        </Box>

        <Box bg="gray.800" p={6} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <Heading size="md" mb={4} color="gold.400">LocalStorage State</Heading>
          <VStack align="start" spacing={3}>
            <HStack>
              <Text fontWeight="bold">Has Stored User:</Text>
              <Badge colorScheme={storedUser ? 'green' : 'red'}>
                {storedUser ? 'Yes' : 'No'}
              </Badge>
            </HStack>

            {parsedStoredUser && (
              <>
                <HStack>
                  <Text fontWeight="bold">Stored Email:</Text>
                  <Text>{parsedStoredUser.email}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Stored ID:</Text>
                  <Text>{parsedStoredUser._id}</Text>
                </HStack>
              </>
            )}
          </VStack>
        </Box>

        <Box bg="gray.800" p={6} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <Heading size="md" mb={4} color="gold.400">Cookies</Heading>
          <Code p={3} bg="gray.900" color="green.300" fontSize="sm" whiteSpace="pre-wrap">
            {document.cookie || 'No cookies found'}
          </Code>
        </Box>

        <Box bg="red.900" p={4} borderRadius="lg" border="2px solid" borderColor="red.500">
          <Heading size="md" mb={3} color="red.300">🚨 ACİL ÇÖZÜM</Heading>
          <Text mb={3} color="red.200">Eğer login çalışmıyorsa, bu butona tıklayarak sisteme hemen giriş yapabilirsiniz:</Text>
          <Button
            colorScheme="red"
            size="lg"
            fontWeight="bold"
            onClick={() => {
              const mockUser = {
                _id: '6838bf7656339ea52d689084',
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                country: 'TR',
                city: 'Istanbul',
                kycVerified: false,
                twoFactorEnabled: false,
                referralCode: 'C7D026FE',
                referralCount: 0,
                referralEarnings: 0,
                marketingConsent: false,
                isAdmin: false,
                lastLogin: new Date().toISOString()
              };
              localStorage.setItem('user', JSON.stringify(mockUser));
              alert('🎉 ACİL GİRİŞ BAŞARILI! Sayfa yenilenecek.');
              setTimeout(() => window.location.reload(), 1000);
            }}
          >
            🚨 ACİL GİRİŞ YAP
          </Button>
        </Box>

        <HStack spacing={4} wrap="wrap">
          <Button
            colorScheme="yellow"
            onClick={handleBasicTest}
          >
            Test Basic JS
          </Button>

          <Button
            colorScheme="teal"
            onClick={handleEnvironmentTest}
          >
            Test Environment
          </Button>

          <Button
            colorScheme="pink"
            onClick={handleRealAPITest}
            isDisabled={loading}
          >
            Test Real API
          </Button>

          <Button
            colorScheme="blue"
            onClick={handleTestLogin}
            isDisabled={loading}
          >
            Test Login
          </Button>

          <Button
            colorScheme="red"
            onClick={handleForceLogin}
            isDisabled={loading}
            size="lg"
            fontWeight="bold"
          >
            🚀 FORCE LOGIN
          </Button>

          <Button
            colorScheme="purple"
            onClick={handleTestAPI}
            isDisabled={loading}
          >
            Test API (Fetch)
          </Button>

          <Button
            colorScheme="cyan"
            onClick={handleDirectAxiosTest}
            isDisabled={loading}
          >
            Test Direct Axios
          </Button>

          <Button
            colorScheme="orange"
            onClick={handleSimpleFetchTest}
            isDisabled={loading}
          >
            Test Simple Fetch
          </Button>

          <Button
            colorScheme="green"
            onClick={handleProfileNavigation}
            isDisabled={loading}
          >
            Go to Profile
          </Button>

          <Button
            colorScheme="red"
            onClick={logout}
            isDisabled={loading || !user}
          >
            Logout
          </Button>
        </HStack>

        <Box bg="gray.800" p={6} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <Heading size="md" mb={4} color="gold.400">Debug Information</Heading>
          <Text fontSize="sm" color="gray.300">
            Open the browser console (F12) to see detailed authentication logs.
            This page will help identify where the authentication flow is failing.
          </Text>
        </Box>
      </VStack>
    </Container>
  );
};

export default AuthTest;
