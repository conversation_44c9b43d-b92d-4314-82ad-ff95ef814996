import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Badge,
  Alert,
  AlertIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useToast,
  Divider,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Code,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { investmentBalanceService, InvestmentBalance } from '../../services/investmentBalanceService';
import { transactionHistoryService, Transaction } from '../../services/transactionHistoryService';
import RealTimeCryptocurrencyCards from '../cards/RealTimeCryptocurrencyCards';
import RealTimeTransactionHistory from '../transactions/RealTimeTransactionHistory';

/**
 * Integration Test Dashboard
 * 
 * This component provides a comprehensive testing interface for:
 * - Real-time WebSocket connections
 * - Investment balance service integration
 * - Transaction history service integration
 * - Enhanced Withdrawal System integration
 * - API endpoint connectivity
 */
const IntegrationTestDashboard: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();

  // Connection status tracking
  const [connectionStatus, setConnectionStatus] = useState({
    investmentBalance: 'disconnected',
    transactionHistory: 'disconnected',
    apiHealth: 'unknown'
  });

  // Test results
  const [testResults, setTestResults] = useState<{
    [key: string]: { status: 'pass' | 'fail' | 'pending'; message: string; timestamp: string; }
  }>({});

  // Real-time data
  const [balances, setBalances] = useState<InvestmentBalance[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [realTimeUpdates, setRealTimeUpdates] = useState<string[]>([]);

  // Add real-time update to log
  const addRealTimeUpdate = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setRealTimeUpdates(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // Run API connectivity test
  const testApiConnectivity = async () => {
    setTestResults(prev => ({
      ...prev,
      apiConnectivity: { status: 'pending', message: 'Testing API connectivity...', timestamp: new Date().toLocaleTimeString() }
    }));

    try {
      // Test investment balance API
      await investmentBalanceService.getInvestmentBalances();
      
      // Test transaction history API
      await transactionHistoryService.getTransactionHistory({ limit: 1 });

      setTestResults(prev => ({
        ...prev,
        apiConnectivity: { status: 'pass', message: 'All API endpoints accessible', timestamp: new Date().toLocaleTimeString() }
      }));

      setConnectionStatus(prev => ({ ...prev, apiHealth: 'healthy' }));

    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        apiConnectivity: { status: 'fail', message: `API connectivity failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
      }));

      setConnectionStatus(prev => ({ ...prev, apiHealth: 'unhealthy' }));
    }
  };

  // Test WebSocket connections
  const testWebSocketConnections = async () => {
    setTestResults(prev => ({
      ...prev,
      websocketConnections: { status: 'pending', message: 'Testing WebSocket connections...', timestamp: new Date().toLocaleTimeString() }
    }));

    try {
      // Initialize WebSocket connections
      investmentBalanceService.initializeRealTimeUpdates();
      transactionHistoryService.initializeRealTimeUpdates();

      // Wait for connections to establish
      setTimeout(() => {
        const balanceConnected = investmentBalanceService.isWebSocketConnected();
        const transactionConnected = transactionHistoryService.isWebSocketConnected();

        if (balanceConnected && transactionConnected) {
          setTestResults(prev => ({
            ...prev,
            websocketConnections: { status: 'pass', message: 'All WebSocket connections established', timestamp: new Date().toLocaleTimeString() }
          }));
          
          setConnectionStatus(prev => ({
            ...prev,
            investmentBalance: 'connected',
            transactionHistory: 'connected'
          }));

          addRealTimeUpdate('WebSocket connections established successfully');
        } else {
          setTestResults(prev => ({
            ...prev,
            websocketConnections: { status: 'fail', message: `WebSocket connections failed - Balance: ${balanceConnected}, Transactions: ${transactionConnected}`, timestamp: new Date().toLocaleTimeString() }
          }));
        }
      }, 3000);

    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        websocketConnections: { status: 'fail', message: `WebSocket test failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
      }));
    }
  };

  // Test Enhanced Withdrawal System integration
  const testWithdrawalSystemIntegration = async () => {
    setTestResults(prev => ({
      ...prev,
      withdrawalIntegration: { status: 'pending', message: 'Testing Enhanced Withdrawal System integration...', timestamp: new Date().toLocaleTimeString() }
    }));

    try {
      // Test withdrawal eligibility check
      const eligibility = await investmentBalanceService.checkWithdrawalEligibility('BTC', 0.001, 'interest');
      
      // Test minimum withdrawals
      const minimums = await investmentBalanceService.getMinimumWithdrawals();

      if (eligibility && minimums) {
        setTestResults(prev => ({
          ...prev,
          withdrawalIntegration: { status: 'pass', message: 'Enhanced Withdrawal System integration working', timestamp: new Date().toLocaleTimeString() }
        }));

        addRealTimeUpdate('Enhanced Withdrawal System integration verified');
      } else {
        throw new Error('Withdrawal system responses invalid');
      }

    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        withdrawalIntegration: { status: 'fail', message: `Withdrawal integration failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
      }));
    }
  };

  // Test real-time data synchronization
  const testRealTimeSync = async () => {
    setTestResults(prev => ({
      ...prev,
      realTimeSync: { status: 'pending', message: 'Testing real-time data synchronization...', timestamp: new Date().toLocaleTimeString() }
    }));

    try {
      // Force refresh balances
      await investmentBalanceService.refreshBalances();
      
      // Force refresh transactions
      await transactionHistoryService.refreshTransactionHistory();

      setTestResults(prev => ({
        ...prev,
        realTimeSync: { status: 'pass', message: 'Real-time data synchronization working', timestamp: new Date().toLocaleTimeString() }
      }));

      addRealTimeUpdate('Real-time data synchronization test completed');

    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        realTimeSync: { status: 'fail', message: `Real-time sync failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
      }));
    }
  };

  // Run all tests
  const runAllTests = async () => {
    toast({
      title: t('integration.testStarted', 'Integration Tests Started'),
      description: t('integration.testDescription', 'Running comprehensive integration tests...'),
      status: 'info',
      duration: 3000,
      isClosable: true
    });

    await testApiConnectivity();
    await testWebSocketConnections();
    await testWithdrawalSystemIntegration();
    await testRealTimeSync();

    const allPassed = Object.values(testResults).every(result => result.status === 'pass');
    
    toast({
      title: allPassed ? t('integration.testsPassed', 'All Tests Passed') : t('integration.testsFailed', 'Some Tests Failed'),
      description: allPassed ? t('integration.allSystemsOperational', 'All systems operational') : t('integration.checkFailedTests', 'Check failed tests for details'),
      status: allPassed ? 'success' : 'warning',
      duration: 5000,
      isClosable: true
    });
  };

  // Setup real-time update listeners
  useEffect(() => {
    const unsubscribeBalance = investmentBalanceService.onBalanceUpdate((updatedBalances) => {
      setBalances(updatedBalances);
      addRealTimeUpdate(`Balance update received: ${updatedBalances.length} currencies`);
    });

    const unsubscribeTransaction = transactionHistoryService.onTransactionUpdate((updatedTransaction) => {
      addRealTimeUpdate(`Transaction update: ${updatedTransaction.type} ${updatedTransaction.status}`);
    });

    return () => {
      unsubscribeBalance();
      unsubscribeTransaction();
    };
  }, []);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': case 'connected': case 'healthy': return '#0ECB81';
      case 'fail': case 'disconnected': case 'unhealthy': return '#F84960';
      case 'pending': case 'connecting': return '#F0B90B';
      default: return '#848E9C';
    }
  };

  return (
    <Box p={6} bg="#0B0E11" minH="100vh">
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Text color="#F0B90B" fontSize="2xl" fontWeight="bold">
              {t('integration.title', 'Integration Test Dashboard')}
            </Text>
            <Text color="#848E9C" fontSize="sm">
              {t('integration.subtitle', 'Comprehensive testing for Shipping Finance platform integration')}
            </Text>
          </VStack>
          
          <Button
            bg="#F0B90B"
            color="#0B0E11"
            _hover={{ bg: "#FCD535" }}
            onClick={runAllTests}
            size="lg"
            fontWeight="bold"
          >
            {t('integration.runAllTests', 'Run All Tests')}
          </Button>
        </HStack>

        {/* Connection Status Overview */}
        <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
          <GridItem>
            <Stat bg="#1E2329" p={4} borderRadius="md" borderWidth="1px" borderColor="#2B3139">
              <StatLabel color="#848E9C">Investment Balance Service</StatLabel>
              <StatNumber color={getStatusColor(connectionStatus.investmentBalance)}>
                {connectionStatus.investmentBalance}
              </StatNumber>
              <StatHelpText color="#848E9C">WebSocket Connection</StatHelpText>
            </Stat>
          </GridItem>
          
          <GridItem>
            <Stat bg="#1E2329" p={4} borderRadius="md" borderWidth="1px" borderColor="#2B3139">
              <StatLabel color="#848E9C">Transaction History Service</StatLabel>
              <StatNumber color={getStatusColor(connectionStatus.transactionHistory)}>
                {connectionStatus.transactionHistory}
              </StatNumber>
              <StatHelpText color="#848E9C">WebSocket Connection</StatHelpText>
            </Stat>
          </GridItem>
          
          <GridItem>
            <Stat bg="#1E2329" p={4} borderRadius="md" borderWidth="1px" borderColor="#2B3139">
              <StatLabel color="#848E9C">API Health</StatLabel>
              <StatNumber color={getStatusColor(connectionStatus.apiHealth)}>
                {connectionStatus.apiHealth}
              </StatNumber>
              <StatHelpText color="#848E9C">Backend Connectivity</StatHelpText>
            </Stat>
          </GridItem>
        </Grid>

        {/* Test Results */}
        <Box bg="#1E2329" p={4} borderRadius="md" borderWidth="1px" borderColor="#2B3139">
          <Text color="#F0B90B" fontSize="lg" fontWeight="bold" mb={4}>
            {t('integration.testResults', 'Test Results')}
          </Text>
          
          <VStack spacing={3} align="stretch">
            {Object.entries(testResults).map(([testName, result]) => (
              <HStack key={testName} justify="space-between" p={3} bg="#0B0E11" borderRadius="md">
                <VStack align="start" spacing={1}>
                  <Text color="#EAECEF" fontWeight="bold">
                    {testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </Text>
                  <Text color="#848E9C" fontSize="sm">
                    {result.message}
                  </Text>
                </VStack>
                
                <VStack align="end" spacing={1}>
                  <Badge
                    colorScheme={result.status === 'pass' ? 'green' : result.status === 'fail' ? 'red' : 'yellow'}
                    variant="subtle"
                  >
                    {result.status}
                  </Badge>
                  <Text color="#848E9C" fontSize="xs">
                    {result.timestamp}
                  </Text>
                </VStack>
              </HStack>
            ))}
          </VStack>
        </Box>

        {/* Real-Time Updates Log */}
        <Box bg="#1E2329" p={4} borderRadius="md" borderWidth="1px" borderColor="#2B3139">
          <Text color="#F0B90B" fontSize="lg" fontWeight="bold" mb={4}>
            {t('integration.realTimeUpdates', 'Real-Time Updates Log')}
          </Text>
          
          <Box maxH="200px" overflowY="auto">
            {realTimeUpdates.length === 0 ? (
              <Text color="#848E9C" fontSize="sm">
                {t('integration.noUpdates', 'No real-time updates received yet')}
              </Text>
            ) : (
              <VStack spacing={1} align="stretch">
                {realTimeUpdates.map((update, index) => (
                  <Code key={index} bg="#0B0E11" color="#0ECB81" fontSize="xs" p={2}>
                    {update}
                  </Code>
                ))}
              </VStack>
            )}
          </Box>
        </Box>

        {/* Live Components Testing */}
        <Tabs variant="enclosed" colorScheme="yellow">
          <TabList>
            <Tab color="#848E9C" _selected={{ color: "#F0B90B", borderColor: "#F0B90B" }}>
              {t('integration.cryptocurrencyCards', 'Cryptocurrency Cards')}
            </Tab>
            <Tab color="#848E9C" _selected={{ color: "#F0B90B", borderColor: "#F0B90B" }}>
              {t('integration.transactionHistory', 'Transaction History')}
            </Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <RealTimeCryptocurrencyCards
                onBalanceUpdate={(balances) => {
                  setBalances(balances);
                  addRealTimeUpdate(`Cryptocurrency cards updated: ${balances.length} currencies`);
                }}
                compactMode={true}
              />
            </TabPanel>
            
            <TabPanel>
              <RealTimeTransactionHistory
                compactMode={true}
                maxHeight="400px"
                onTransactionClick={(transaction) => {
                  addRealTimeUpdate(`Transaction clicked: ${transaction.id}`);
                }}
              />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Box>
  );
};

export default IntegrationTestDashboard;
