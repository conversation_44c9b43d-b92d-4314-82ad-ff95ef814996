# 🚀 CryptoYield Development Environment

Complete development environment setup for the CryptoYield project with hot-reload, transaction support, and database administration.

## 🎯 Features

✅ **Backend Development with Hot-Reload**
- Source code mounted as volume for live editing
- Automatic restart on file changes (nodemon + ts-node)
- TypeScript compilation on-the-fly
- Debug-friendly logging

✅ **MongoDB with Transaction Support**
- MongoDB 7.0 with replica set (rs0)
- Full ACID transaction capabilities
- Authentication enabled (cryptoyield_admin/secure_password123)
- Keyfile security for replica set

✅ **Database Administration**
- Mongo Express web interface at http://localhost:8081
- Easy database browsing and management
- Authentication: admin/admin123

✅ **Redis Caching**
- Redis 7 for session management and caching
- Persistent data storage
- Health monitoring

✅ **Proper Networking**
- Isolated Docker network for services
- Service discovery between containers
- External access from host machine

## 🚀 Quick Start

### 1. Initial Setup
```bash
# Run the automated setup script
./setup-dev-environment.sh
```

### 2. Start Development
```bash
# Start all services
./dev-workflow.sh start

# Follow backend logs
./dev-workflow.sh logs
```

### 3. Verify Setup
```bash
# Run comprehensive tests
./verify-dev-environment.sh
```

## 📋 Service Information

| Service | URL | Purpose |
|---------|-----|---------|
| Backend API | http://localhost:5000 | Main application API |
| Mongo Express | http://localhost:8081 | Database admin interface |
| MongoDB | localhost:27017 | Database server |
| Redis | localhost:6379 | Cache and session store |

## 🔧 Development Workflow

### Common Commands
```bash
# Start development environment
./dev-workflow.sh start

# View backend logs
./dev-workflow.sh logs

# Open MongoDB shell
./dev-workflow.sh mongo

# Open backend container shell
./dev-workflow.sh shell

# Check service health
./dev-workflow.sh health

# Stop environment
./dev-workflow.sh stop
```

### Manual Docker Compose Commands
```bash
# Start services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f backend

# Stop services
docker-compose -f docker-compose.dev.yml down

# Rebuild backend
docker-compose -f docker-compose.dev.yml build --no-cache backend
```

## 📁 File Structure

```
cryptoyield/
├── docker-compose.dev.yml          # Development environment config
├── setup-dev-environment.sh        # Automated setup script
├── verify-dev-environment.sh       # Environment verification
├── dev-workflow.sh                 # Development workflow helper
├── mongo-init-replica.js           # MongoDB replica set initialization
├── mongodb-keyfile/                # MongoDB security keyfile
│   └── mongodb-keyfile
├── backend/
│   ├── Dockerfile.dev              # Development Dockerfile
│   ├── src/                        # Source code (mounted as volume)
│   ├── package.json
│   ├── tsconfig.json
│   ├── nodemon.json
│   └── .env.docker                 # Development environment variables
└── ...
```

## 🔗 Connection Details

### MongoDB
- **Host**: localhost:27017
- **Username**: cryptoyield_admin
- **Password**: secure_password123
- **Database**: cryptoyield
- **Replica Set**: rs0
- **Connection String**: 
  ```
  **********************************************************************************************************
  ```

### Redis
- **Host**: localhost:6379
- **Connection String**: `redis://localhost:6379`

### Mongo Express
- **URL**: http://localhost:8081
- **Username**: admin
- **Password**: admin123

## 🧪 Testing Transaction Support

```bash
# Test transactions via MongoDB shell
./dev-workflow.sh mongo

# In MongoDB shell:
use cryptoyield
const session = db.getMongo().startSession();
session.startTransaction();
db.test.insertOne({test: "transaction"}, {session: session});
session.commitTransaction();
session.endSession();
```

## 🔄 Hot-Reload Development

1. **Edit files** in `./backend/src/` on your host machine
2. **Changes are automatically detected** by nodemon
3. **Backend restarts automatically** with new code
4. **No manual rebuild required**

Example workflow:
```bash
# Start development environment
./dev-workflow.sh start

# Edit a file
vim ./backend/src/controllers/userController.ts

# Watch logs to see automatic restart
./dev-workflow.sh logs
```

## 🐛 Debugging

### View Logs
```bash
# Backend logs only
./dev-workflow.sh logs

# All service logs
./dev-workflow.sh logs-all

# Specific service logs
docker-compose -f docker-compose.dev.yml logs mongodb
```

### Access Containers
```bash
# Backend container shell
./dev-workflow.sh shell

# MongoDB shell
./dev-workflow.sh mongo

# Redis CLI
./dev-workflow.sh redis
```

### Health Checks
```bash
# Quick health check
./dev-workflow.sh health

# Comprehensive verification
./verify-dev-environment.sh

# Container status
./dev-workflow.sh status
```

## 🔧 Troubleshooting

### Common Issues

1. **MongoDB not starting**
   ```bash
   # Check keyfile permissions
   ls -la mongodb-keyfile/mongodb-keyfile
   
   # Recreate keyfile if needed
   rm -rf mongodb-keyfile
   ./setup-dev-environment.sh
   ```

2. **Backend not connecting to MongoDB**
   ```bash
   # Check environment variables
   docker exec cryptoyield-backend-dev env | grep MONGO
   
   # Test MongoDB connectivity
   docker exec cryptoyield-backend-dev nc -z mongodb 27017
   ```

3. **Hot-reload not working**
   ```bash
   # Check volume mounting
   docker exec cryptoyield-backend-dev ls -la /app/src
   
   # Restart backend service
   docker-compose -f docker-compose.dev.yml restart backend
   ```

4. **Port conflicts**
   ```bash
   # Check if ports are in use
   netstat -tulpn | grep -E "(5000|27017|6379|8081)"
   
   # Stop conflicting services
   ./dev-workflow.sh stop
   ```

### Reset Environment
```bash
# Complete cleanup and restart
./dev-workflow.sh clean
./setup-dev-environment.sh
```

## 📊 Environment Variables

The backend container uses these key environment variables:

```env
NODE_ENV=development
PORT=5000
MONGO_URI=********************************************************************************************************
REDIS_URL=redis://redis:6379
JWT_SECRET=crypto_yield_hub_dev_jwt_secret
LOG_LEVEL=debug
```

## 🎯 Next Steps

1. **Start developing**: Edit files in `./backend/src/`
2. **Test your changes**: Use the API endpoints
3. **Monitor logs**: Keep an eye on `./dev-workflow.sh logs`
4. **Use database admin**: Access http://localhost:8081
5. **Run tests**: Execute `./verify-dev-environment.sh`

## 📞 Support

If you encounter issues:

1. **Check logs**: `./dev-workflow.sh logs-all`
2. **Verify setup**: `./verify-dev-environment.sh`
3. **Reset environment**: `./dev-workflow.sh clean && ./setup-dev-environment.sh`
4. **Check documentation**: Review this README

Happy coding! 🎉
