import mongoose, { Document, Schema } from 'mongoose';
import crypto from 'crypto';

export interface IWithdrawalAddress extends Document {
  userId: mongoose.Types.ObjectId;
  currency: string;
  address: string;
  label: string;
  network: string;
  isDefault: boolean;
  isActive: boolean;
  isVerified: boolean;
  verificationCode?: string;
  verificationCodeExpires?: Date;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  generateVerificationCode(): string;
  verifyAddress(code: string): boolean;
  markAsUsed(): Promise<IWithdrawalAddress>;
  validateAddress(): boolean;
}

const withdrawalAddressSchema = new Schema<IWithdrawalAddress>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    currency: {
      type: String,
      required: [true, 'Currency is required'],
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'],
      index: true
    },
    address: {
      type: String,
      required: [true, 'Wallet address is required'],
      trim: true,
      index: true
    },
    label: {
      type: String,
      required: [true, 'Label is required'],
      trim: true,
      maxlength: 50
    },
    network: {
      type: String,
      required: [true, 'Network is required'],
      trim: true,
      default: 'mainnet'
    },
    isDefault: {
      type: Boolean,
      default: false,
      index: true
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    isVerified: {
      type: Boolean,
      default: false,
      index: true
    },
    verificationCode: {
      type: String,
      trim: true,
      select: false // Don't include in queries by default
    },
    verificationCodeExpires: {
      type: Date,
      select: false
    },
    lastUsed: {
      type: Date,
      index: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Compound indexes for performance and uniqueness
withdrawalAddressSchema.index({ userId: 1, currency: 1, address: 1 }, { unique: true });
withdrawalAddressSchema.index({ userId: 1, currency: 1, isDefault: 1 });
withdrawalAddressSchema.index({ currency: 1, isActive: 1, isVerified: 1 });

// Virtual for formatted address (truncated)
withdrawalAddressSchema.virtual('formattedAddress').get(function() {
  if (!this.address) return '';
  if (this.address.length <= 10) return this.address;
  return `${this.address.slice(0, 6)}...${this.address.slice(-4)}`;
});

// Instance method to generate verification code
withdrawalAddressSchema.methods.generateVerificationCode = function(): string {
  const code = crypto.randomBytes(3).toString('hex').toUpperCase();
  this.verificationCode = code;
  this.verificationCodeExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  return code;
};

// Instance method to verify address
withdrawalAddressSchema.methods.verifyAddress = function(code: string): boolean {
  if (!this.verificationCode || !this.verificationCodeExpires) {
    return false;
  }
  
  if (new Date() > this.verificationCodeExpires) {
    return false;
  }
  
  if (this.verificationCode !== code.toUpperCase()) {
    return false;
  }
  
  this.isVerified = true;
  this.verificationCode = undefined;
  this.verificationCodeExpires = undefined;
  return true;
};

// Instance method to mark as used
withdrawalAddressSchema.methods.markAsUsed = async function(): Promise<IWithdrawalAddress> {
  this.lastUsed = new Date();
  return await this.save();
};

// Instance method to validate address format
withdrawalAddressSchema.methods.validateAddress = function(): boolean {
  const address = this.address;
  const currency = this.currency;
  
  switch (currency) {
    case 'BTC':
      // Bitcoin address validation (simplified)
      return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(address);
    case 'ETH':
    case 'USDT':
    case 'BNB':
    case 'LINK':
    case 'UNI':
      // Ethereum-based address validation
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    case 'ADA':
      // Cardano address validation (simplified)
      return /^addr1[a-z0-9]{58}$/.test(address);
    case 'DOT':
      // Polkadot address validation (simplified)
      return /^1[a-zA-Z0-9]{47}$/.test(address);
    default:
      return false;
  }
};

// Static methods interface
interface IWithdrawalAddressModel extends mongoose.Model<IWithdrawalAddress> {
  getUserAddresses(userId: string, currency?: string): Promise<IWithdrawalAddress[]>;
  getDefaultAddress(userId: string, currency: string): Promise<IWithdrawalAddress | null>;
  setDefaultAddress(userId: string, addressId: string): Promise<boolean>;
  createAddress(userId: string, currency: string, address: string, label: string, network?: string): Promise<IWithdrawalAddress>;
  validateUniqueAddress(userId: string, currency: string, address: string, excludeId?: string): Promise<boolean>;
}

// Static method to get user addresses
withdrawalAddressSchema.statics.getUserAddresses = function(userId: string, currency?: string) {
  const query: any = { userId, isActive: true };
  if (currency) {
    query.currency = currency.toUpperCase();
  }
  return this.find(query).sort({ isDefault: -1, createdAt: -1 });
};

// Static method to get default address
withdrawalAddressSchema.statics.getDefaultAddress = function(userId: string, currency: string) {
  return this.findOne({
    userId,
    currency: currency.toUpperCase(),
    isDefault: true,
    isActive: true,
    isVerified: true
  });
};

// Static method to set default address
withdrawalAddressSchema.statics.setDefaultAddress = async function(userId: string, addressId: string): Promise<boolean> {
  const address = await this.findOne({ _id: addressId, userId, isActive: true });
  if (!address) return false;

  // Remove default from other addresses of the same currency
  await this.updateMany(
    { userId, currency: address.currency, _id: { $ne: addressId } },
    { isDefault: false }
  );

  // Set this address as default
  address.isDefault = true;
  await address.save();
  return true;
};

// Static method to create address
withdrawalAddressSchema.statics.createAddress = async function(
  userId: string,
  currency: string,
  address: string,
  label: string,
  network: string = 'mainnet'
): Promise<IWithdrawalAddress> {
  // Check if address already exists for this user and currency
  const existingAddress = await this.findOne({
    userId,
    currency: currency.toUpperCase(),
    address,
    isActive: true
  });
  
  if (existingAddress) {
    throw new Error('This address already exists in your wallet list');
  }

  const newAddress = new this({
    userId,
    currency: currency.toUpperCase(),
    address,
    label,
    network,
    isActive: true,
    isVerified: false,
    isDefault: false
  });

  // Validate address format
  if (!newAddress.validateAddress()) {
    throw new Error('Invalid address format for the selected currency');
  }

  return await newAddress.save();
};

// Static method to validate unique address
withdrawalAddressSchema.statics.validateUniqueAddress = async function(
  userId: string,
  currency: string,
  address: string,
  excludeId?: string
): Promise<boolean> {
  const query: any = {
    userId,
    currency: currency.toUpperCase(),
    address,
    isActive: true
  };
  
  if (excludeId) {
    query._id = { $ne: excludeId };
  }
  
  const existingAddress = await this.findOne(query);
  return !existingAddress;
};

// Pre-save middleware
withdrawalAddressSchema.pre('save', function(next) {
  // Ensure only one default address per currency per user
  if (this.isDefault && this.isModified('isDefault')) {
    this.constructor.updateMany(
      { 
        userId: this.userId, 
        currency: this.currency, 
        _id: { $ne: this._id } 
      },
      { isDefault: false }
    ).exec();
  }
  next();
});

const WithdrawalAddress = mongoose.model<IWithdrawalAddress, IWithdrawalAddressModel>(
  'WithdrawalAddress',
  withdrawalAddressSchema
);

export default WithdrawalAddress;
