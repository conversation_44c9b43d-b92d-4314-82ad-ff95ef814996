import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import User from '../models/userModel';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import { cacheService } from '../services/cacheService';
import { createTransaction } from '../utils/transactionUtils';
import { CRYPTO_NETWORKS, getNetworkById } from '../utils/cryptoNetworks';
import walletService from '../services/walletService';
import depositMonitorService from '../services/depositMonitorService';
import { applyInvestmentCommissions } from '../services/simpleCommissionService';

// @desc    Initialize user wallet
// @route   POST /api/wallets/connect
// @access  Private
export const connectWallet = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address, asset, network } = req.body;

    // Check if wallet already exists
    let wallet = await Wallet.findOne({ userId: req.user._id });

    if (wallet) {
      // If asset and network are provided, add or update the asset
      if (asset && network) {
        const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
        if (assetIndex === -1) {
          // Add new asset
          wallet.assets.push({
            symbol: asset,
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            mode: 'commission',
            network,
            address: address || undefined
          });
        } else {
          // Update existing asset
          if (address) wallet.assets[assetIndex].address = address;
          if (network) wallet.assets[assetIndex].network = network;
        }
        await wallet.save();
        logger.info(`Asset ${asset} updated for user: ${req.user._id}`);
      }
    } else {
      // Create new wallet
      const assets = [];
      if (asset && network) {
        assets.push({
          symbol: asset,
          balance: 0,
          commissionBalance: 0,
          interestBalance: 0,
          mode: 'commission',
          network,
          address: address || undefined
        });
      }

      wallet = await Wallet.create({
        userId: req.user._id,
        assets,
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
      logger.info(`New wallet created for user: ${req.user._id}`);
    }

    // If address is provided, update user's wallet address
    if (address && /^0x[a-fA-F0-9]{40}$/.test(address)) {
      await User.findByIdAndUpdate(req.user._id, { walletAddress: address });
    }

    res.status(201).json(wallet);
  } catch (error: any) {
    logger.error('Wallet connection error:', error);
    res.status(500).json({
      message: 'An error occurred while connecting the wallet',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get wallet info
// @route   GET /api/wallets/info
// @access  Private
export const getWalletBalance = catchAsync(async (req: Request, res: Response) => {
  const cacheKey = `wallet:info:${req.user._id}`;

  // Skip cache if cache-busting parameter is present
  const skipCache = req.query.t;

  // Try to get from cache first (unless cache-busting)
  if (!skipCache) {
    const cachedWallet = cacheService.get(cacheKey);
    if (cachedWallet) {
      return res.json(cachedWallet);
    }
  }

  // Find or create wallet
  let wallet = await Wallet.findOne({ userId: req.user._id });

  if (!wallet) {
    // Create a new wallet with demo crypto assets for testing
    wallet = await Wallet.create({
      userId: req.user._id,
      assets: [
      ],
      totalCommissionEarned: 0,
      totalInterestEarned: 0
    });

    logger.info(`Auto-created wallet with demo assets for user: ${req.user._id}`);
  }

  // Cache the wallet for 5 minutes
  cacheService.set(cacheKey, wallet, 300);

  res.json(wallet);
});

// @desc    Get wallet with earned balance breakdown
// @route   GET /api/wallets/earned-balance
// @access  Private
export const getWalletEarnedBalance = catchAsync(async (req: Request, res: Response) => {
  const wallet = await Wallet.findOne({ userId: req.user._id });

  if (!wallet) {
    throw new AppError('Wallet not found', 404);
  }

  // Get assets with earned balance using the new instance method
  const assetsWithEarned = wallet.getAssetsWithEarnedBalance();

  // Calculate summary statistics
  const summary = {
    totalAssets: assetsWithEarned.length,
    assetsWithEarnings: assetsWithEarned.filter(asset => asset.totalEarnings > 0).length,
    totalCommissionEarned: wallet.totalCommissionEarned,
    totalInterestEarned: wallet.totalInterestEarned,
    assetBreakdown: assetsWithEarned.map(asset => ({
      symbol: asset.symbol,
      balance: asset.balance,
      commissionBalance: asset.commissionBalance || 0,
      interestBalance: asset.interestBalance || 0,
      totalEarnings: asset.totalEarnings,
      earnedPercentage: asset.balance > 0 ?
        ((asset.totalEarnings / asset.balance) * 100).toFixed(2) : '0.00',
      hasEarnings: asset.totalEarnings > 0,
      mode: asset.mode,
      network: asset.network
    }))
  };

  res.json({
    status: 'success',
    data: {
      wallet: wallet.toJSON(), // This will include earned_balance virtual property
      summary
    }
  });
});

// @desc    Get earned balance for specific asset
// @route   GET /api/wallets/earned-balance/:asset
// @access  Private
export const getAssetEarnedBalance = catchAsync(async (req: Request, res: Response) => {
  const { asset } = req.params;
  const wallet = await Wallet.findOne({ userId: req.user._id });

  if (!wallet) {
    throw new AppError('Wallet not found', 404);
  }

  // Use the new instance method to get earned balance for specific asset
  const earnedBalance = wallet.getEarnedBalance(asset.toUpperCase());
  const assetData = wallet.assets.find(a => a.symbol === asset.toUpperCase());

  if (!assetData) {
    throw new AppError(`Asset ${asset.toUpperCase()} not found in wallet`, 404);
  }

  res.json({
    status: 'success',
    data: {
      asset: asset.toUpperCase(),
      balance: assetData.balance,
      commissionBalance: assetData.commissionBalance || 0,
      interestBalance: assetData.interestBalance || 0,
      totalEarnings: earnedBalance, // Calculated using getEarnedBalance method
      earnedPercentage: assetData.balance > 0 ?
        ((earnedBalance / assetData.balance) * 100).toFixed(2) : '0.00',
      hasEarnings: earnedBalance > 0,
      mode: assetData.mode,
      network: assetData.network,
      breakdown: {
        commission: {
          amount: assetData.commissionBalance || 0,
          percentage: earnedBalance > 0 ?
            (((assetData.commissionBalance || 0) / earnedBalance) * 100).toFixed(2) : '0.00'
        },
        interest: {
          amount: assetData.interestBalance || 0,
          percentage: earnedBalance > 0 ?
            (((assetData.interestBalance || 0) / earnedBalance) * 100).toFixed(2) : '0.00'
        }
      }
    }
  });
});

// @desc    Toggle between commission and interest mode
// @route   POST /api/wallets/toggle-mode
// @access  Private
export const toggleMode = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset, mode } = req.body;

    if (!['commission', 'interest'].includes(mode)) {
      res.status(400).json({ message: 'Invalid mode' });
      return;
    }

    // Find or create wallet
    let wallet = await Wallet.findOne({ userId: req.user._id });

    if (!wallet) {
      // Create a new wallet with default values
      wallet = await Wallet.create({
        userId: req.user._id,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });

      logger.info(`Auto-created wallet for user: ${req.user._id} during toggle mode request`);
    }

    // Find the asset in the wallet
    const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);

    if (assetIndex === -1) {
      res.status(404).json({ message: 'Asset not found in wallet' });
      return;
    }

    // Update the mode
    wallet.assets[assetIndex].mode = mode;
    await wallet.save();

    res.json({ message: `Mode changed to ${mode} for ${asset}` });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Deposit asset to wallet
// @route   POST /api/wallets/deposit
// @access  Private
export const depositAsset = async (req: Request, res: Response): Promise<void> => {
  // Start transaction session for data consistency
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { asset, amount, txHash, blockchainNetwork } = req.body;

    // Enhanced validation
    if (!asset || !amount || amount <= 0 || !blockchainNetwork) {
      await session.abortTransaction();
      session.endSession();
      res.status(400).json({
        message: 'Missing or invalid information',
        errors: {
          asset: !asset ? 'Cryptocurrency is required' : undefined,
          amount: !amount ? 'Amount is required' : amount <= 0 ? 'Amount must be greater than 0' : undefined,
          blockchainNetwork: !blockchainNetwork ? 'Blockchain network is required' : undefined
        }
      });
      return;
    }

    // Find or create wallet with session
    let wallet = await Wallet.findOne({ userId: req.user._id }).session(session);

    if (!wallet) {
      // Create a new wallet with default values using session
      const newWallet = await Wallet.create([{
        userId: req.user._id,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      }], { session });

      wallet = newWallet[0]; // Mongoose create with session returns an array
      logger.info(`Auto-created wallet for user: ${req.user._id} during deposit request`);
    }

    // Find or create asset
    let assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
    if (assetIndex === -1) {
      wallet.assets.push({
        symbol: asset,
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'commission',
        network: blockchainNetwork
      });
      assetIndex = wallet.assets.length - 1;
    }

    // Apply simple commission system (1% platform + 3% referral) with session
    const commissionResult = await applyInvestmentCommissions(
      req.user._id,
      amount,
      asset,
      session
    );

    // Update balances with net amount (after platform commission)
    wallet.assets[assetIndex].balance += commissionResult.finalAmount;

    // Create investment package automatically if amount >= 0.000001 USDT
    let investmentPackage = null;
    if (commissionResult.finalAmount >= 0.000001) {
      try {
        const InvestmentPackage = (await import('../models/investmentPackageModel')).default;

        investmentPackage = new InvestmentPackage({
          userId: req.user._id,
          amount: commissionResult.finalAmount,
          currency: asset.toUpperCase(),
          status: 'pending',
          compoundEnabled: false,
          autoCreated: true,
          originalUSDTValue: commissionResult.finalAmount,
          minimumWithdrawalUSDT: 50
        });

        // Activate the package (sets activation time to next 03:00)
        await investmentPackage.activate();
        await investmentPackage.save({ session });

        logger.info(`Auto-created investment package for user: ${req.user._id}, amount: ${commissionResult.finalAmount}`);
      } catch (packageError) {
        logger.error('Error creating investment package:', packageError);
        // Continue without failing the deposit
      }
    }

    await wallet.save({ session });
    logger.info(`Deposit processed for user: ${req.user._id}, amount: ${amount} ${asset}, net: ${commissionResult.finalAmount}`);

    // Create deposit transaction
    const depositTx = await createTransaction({
      userId: req.user._id,
      walletId: wallet._id,
      type: 'deposit',
      asset,
      amount: commissionResult.finalAmount, // Net amount after commission
      status: 'completed',
      txHash,
      blockchainNetwork,
      userName: `${req.user.firstName} ${req.user.lastName}`,
      userEmail: req.user.email,
      metadata: {
        originalAmount: amount,
        platformCommission: commissionResult.platformCommission.commissionAmount,
        referralCommission: commissionResult.referralCommission?.commissionAmount || 0,
        commissionSystem: 'simple'
      }
    });

    res.status(201).json({
      message: 'Deposit successful with simple commission system',
      deposit: depositTx,
      investmentPackage: investmentPackage ? {
        id: investmentPackage._id,
        amount: investmentPackage.amount,
        status: investmentPackage.status,
        activatedAt: investmentPackage.activatedAt
      } : null,
      commissions: {
        platform: commissionResult.platformCommission,
        referral: commissionResult.referralCommission
      },
      newBalance: wallet.assets[assetIndex].balance,
      summary: {
        originalAmount: amount,
        platformCommission: commissionResult.platformCommission.commissionAmount,
        referralCommission: commissionResult.referralCommission?.commissionAmount || 0,
        finalAmount: commissionResult.finalAmount,
        autoInvestmentCreated: !!investmentPackage
      }
    });

    // Commit transaction
    await session.commitTransaction();
    session.endSession();

  } catch (error: any) {
    // Rollback transaction on error
    await session.abortTransaction();
    session.endSession();

    logger.error('Deposit error:', error);
    res.status(500).json({
      message: 'An error occurred during the deposit process',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};





// @desc    Get transaction history
// @route   GET /api/wallets/transactions
// @access  Private
export const getTransactionHistory = catchAsync(async (req: Request, res: Response): Promise<void> => {
  const cacheKey = `wallet:transactions:${req.user._id}`;

  // Try to get from cache first
  const cachedTransactions = cacheService.get(cacheKey);
  if (cachedTransactions) {
    res.json(cachedTransactions);
    return;
  }

  // Find or create wallet
  let wallet = await Wallet.findOne({ userId: req.user._id });

  if (!wallet) {
    // Create a new wallet with default values
    wallet = await Wallet.create({
      userId: req.user._id,
      assets: [],
      totalCommissionEarned: 0,
      totalInterestEarned: 0
    });

    logger.info(`Auto-created wallet for user: ${req.user._id} during transaction history request`);
  }

  // Get pagination parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const skip = (page - 1) * limit;

  // Get filter parameters
  const type = req.query.type as string;
  const asset = req.query.asset as string;
  const status = req.query.status as string;

  // Build filter
  const filter: any = { walletId: wallet._id };
  if (type) filter.type = type;
  if (asset) filter.asset = asset;
  if (status) filter.status = status;

  // Get total count for pagination
  const total = await Transaction.countDocuments(filter);

  // Get transactions with pagination
  const transactions = await Transaction.find(filter)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const result = {
    transactions,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };

  // Cache the transactions for 5 minutes
  cacheService.set(cacheKey, result, 300);

  res.json(result);
});

// @desc    Get crypto deposit address for a currency
// @route   GET /api/wallets/deposit-address/:currency
// @access  Private
export const getDepositAddress = catchAsync(async (req: Request, res: Response) => {
  const { currency } = req.params;
  const { network } = req.query;

  if (!currency) {
    throw new AppError('Currency is required', 400);
  }

  // Import SystemConfig model
  const SystemConfig = require('../models/systemConfigModel').default;

  // Find system config
  const config = await SystemConfig.findOneOrCreate();

  // Find the crypto address configuration for the currency and network if specified
  let cryptoAddressConfig: any = null;

  if (network) {
    // Tìm địa chỉ theo network cụ thể
    cryptoAddressConfig = config.cryptoAddresses.find(
      (ca: any) => ca.currency === currency.toUpperCase() && ca.enabled && ca.network === network
    );
  }

  // Nếu không tìm thấy địa chỉ theo network hoặc không có network được chỉ định
  if (!cryptoAddressConfig) {
    cryptoAddressConfig = config.cryptoAddresses.find(
      (ca: any) => ca.currency === currency.toUpperCase() && ca.enabled
    );
  }

  if (!cryptoAddressConfig || cryptoAddressConfig.addresses.length === 0) {
    throw new AppError(`No addresses available for ${currency}${network ? ` on network ${network}` : ''}`, 404);
  }

  // Get the next address
  let address: string = '';
  let addressNetwork: string = cryptoAddressConfig.network || 'default';

  // Kiểm tra cấu trúc của addresses
  const addressItem = cryptoAddressConfig.addresses[cryptoAddressConfig.currentIndex];
  if (typeof addressItem === 'string') {
    // Cấu trúc cũ: mảng chuỗi
    address = addressItem;
  } else if (typeof addressItem === 'object' && addressItem !== null) {
    // Cấu trúc mới: mảng đối tượng
    address = addressItem.address;
    addressNetwork = addressItem.network || addressNetwork;
  }

  // Update the current index
  cryptoAddressConfig.currentIndex =
    (cryptoAddressConfig.currentIndex + 1) % cryptoAddressConfig.addresses.length;

  // Save the updated config
  await config.save();

  // Return the address
  res.status(200).json({
    success: true,
    data: {
      currency: currency.toUpperCase(),
      address,
      network: addressNetwork
    }
  });
});

// @desc    Get available wallet addresses for all currencies or a specific currency
// @route   GET /api/wallets/available
// @access  Private
export const getAvailableWallets = catchAsync(async (req: Request, res: Response) => {
  const { currency } = req.query;

  // Danh sách các loại tiền điện tử được hỗ trợ
  const supportedCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'TRX'];

  // Import SystemConfig model
  const SystemConfig = require('../models/systemConfigModel').default;

  // Find system config
  const config = await SystemConfig.findOneOrCreate();

  // Tìm tất cả các cấu hình địa chỉ cho loại tiền được chỉ định hoặc tất cả các loại tiền
  let cryptoAddressConfigs: any[] = [];

  if (currency) {
    // Nếu có tham số currency, chỉ lấy địa chỉ cho loại tiền đó
    const currencyUpper = (currency as string).toUpperCase();

    // Kiểm tra xem loại tiền có được hỗ trợ không
    if (!supportedCurrencies.includes(currencyUpper)) {
      throw new AppError(`Currency ${currency} is not supported`, 400);
    }

    cryptoAddressConfigs = config.cryptoAddresses.filter(
      (ca: any) => ca.currency === currencyUpper && ca.enabled
    );

    // Nếu không có địa chỉ nào cho loại tiền này, trả về mảng rỗng thay vì lỗi
    if (!cryptoAddressConfigs || cryptoAddressConfigs.length === 0) {
      // Tạo cấu trúc dữ liệu rỗng cho loại tiền này
      cryptoAddressConfigs = [];
    }
  } else {
    // Nếu không có tham số currency, lấy tất cả các địa chỉ đã bật
    cryptoAddressConfigs = config.cryptoAddresses.filter(
      (ca: any) => ca.enabled
    );

    // Đảm bảo tất cả các loại tiền được hỗ trợ đều có trong kết quả
    const existingCurrencies = cryptoAddressConfigs.map((ca: any) => ca.currency);
    const missingCurrencies = supportedCurrencies.filter(c => !existingCurrencies.includes(c));

    // Thêm các loại tiền còn thiếu vào kết quả với mảng địa chỉ rỗng
    missingCurrencies.forEach(currency => {
      cryptoAddressConfigs.push({
        currency,
        addresses: [],
        currentIndex: 0,
        enabled: true
      });
    });
  }

  // Tạo đối tượng chứa danh sách địa chỉ theo network với thông tin chi tiết
  interface NetworkInfo {
    id: string;
    name: string;
    description: string;
    fee: number;
    processingTime: string;
    warningMessage?: string;
    addresses: string[];
  }

  // Tạo đối tượng kết quả theo cấu trúc: { currency: { networkId: NetworkInfo } }
  const result: { [currency: string]: { [networkId: string]: NetworkInfo } } = {};

  // Đảm bảo tất cả các loại tiền được hỗ trợ đều có trong kết quả
  supportedCurrencies.forEach(currencyCode => {
    if (!result[currencyCode]) {
      result[currencyCode] = {};
    }

    // Lấy danh sách network cho loại tiền này từ CRYPTO_NETWORKS
    const networks = CRYPTO_NETWORKS[currencyCode] || [];

    // Khởi tạo tất cả các network với mảng địa chỉ rỗng
    networks.forEach(network => {
      result[currencyCode][network.id] = {
        id: network.id,
        name: network.name,
        description: network.description,
        fee: network.fee,
        processingTime: network.processingTime,
        warningMessage: network.warningMessage,
        addresses: []
      };
    });
  });

  // Phân loại địa chỉ theo currency và network
  cryptoAddressConfigs.forEach((config: any) => {
    const currencyCode = config.currency;

    // Bỏ qua nếu loại tiền không được hỗ trợ
    if (!supportedCurrencies.includes(currencyCode)) {
      return;
    }

    // Xử lý cấu trúc cũ (mảng chuỗi với network ở cấp độ config)
    if (config.addresses && config.addresses.length > 0 && typeof config.addresses[0] === 'string') {
      const networkId = config.network || 'default';

      // Kiểm tra xem network có tồn tại trong kết quả không
      if (!result[currencyCode][networkId]) {
        // Lấy thông tin network từ CRYPTO_NETWORKS
        const networkInfo = getNetworkById(currencyCode, networkId);

        result[currencyCode][networkId] = {
          id: networkId,
          name: networkInfo?.name || networkId,
          description: networkInfo?.description || '',
          fee: networkInfo?.fee || 0,
          processingTime: networkInfo?.processingTime || '',
          warningMessage: networkInfo?.warningMessage,
          addresses: []
        };
      }

      result[currencyCode][networkId].addresses = config.addresses;
    }
    // Xử lý cấu trúc mới (mảng đối tượng với network ở cấp độ địa chỉ)
    else if (config.addresses && config.addresses.length > 0 && typeof config.addresses[0] === 'object') {
      config.addresses.forEach((item: any) => {
        const networkId = item.network || 'default';

        // Kiểm tra xem network có tồn tại trong kết quả không
        if (!result[currencyCode][networkId]) {
          // Lấy thông tin network từ CRYPTO_NETWORKS
          const networkInfo = getNetworkById(currencyCode, networkId);

          result[currencyCode][networkId] = {
            id: networkId,
            name: networkInfo?.name || networkId,
            description: networkInfo?.description || '',
            fee: networkInfo?.fee || 0,
            processingTime: networkInfo?.processingTime || '',
            warningMessage: networkInfo?.warningMessage,
            addresses: []
          };
        }

        result[currencyCode][networkId].addresses.push(item.address);
      });
    }
  });

  // Nếu có tham số currency, chỉ trả về dữ liệu cho loại tiền đó
  const responseData = currency ? result[(currency as string).toUpperCase()] : result;

  // Return the available wallets with network info
  res.status(200).json({
    success: true,
    data: responseData
  });
});

// ===== NEW CRYPTO DEPOSIT SYSTEM ENDPOINTS =====

/**
 * @desc    Get user's crypto wallet addresses
 * @route   GET /api/wallets/user-addresses
 * @access  Private
 */
export const getUserAddresses = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    const wallets = await walletService.getUserWallets(userId);

    const addresses = wallets.map(wallet => ({
      currency: wallet.currency,
      address: wallet.address,
      qrCodeUrl: walletService.generateQRCode(wallet.address, wallet.currency),
      balance: wallet.balance,
      lastUpdated: wallet.lastUpdated,
      network: wallet.network
    }));

    res.json({
      status: 'success',
      data: {
        addresses,
        totalWallets: addresses.length
      }
    });

  } catch (error: any) {
    logger.error('Get user addresses error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user addresses',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get balance for a specific currency
 * @route   GET /api/wallets/:currency/balance
 * @access  Private
 */
export const getCurrencyBalance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currency } = req.params;
    const userId = req.user?._id || 'mock-user-id';

    if (!currency) {
      res.status(400).json({
        status: 'error',
        message: 'Currency parameter is required'
      });
      return;
    }

    const wallets = await walletService.getUserWallets(userId);
    const wallet = wallets.find(w => w.currency === currency.toUpperCase());

    if (!wallet) {
      res.status(404).json({
        status: 'error',
        message: `Wallet not found for currency: ${currency}`
      });
      return;
    }

    // Get USDT value
    const balances = await walletService.getWalletBalances(userId);
    const balanceInfo = balances.find(b => b.currency === currency.toUpperCase());

    res.json({
      status: 'success',
      data: {
        currency: wallet.currency,
        balance: wallet.balance,
        usdtValue: balanceInfo?.usdtValue || 0,
        lastUpdated: wallet.lastUpdated,
        address: wallet.address
      }
    });

  } catch (error: any) {
    logger.error('Get currency balance error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get currency balance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get all wallet balances with USDT values
 * @route   GET /api/wallets/balances
 * @access  Private
 */
export const getAllBalances = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    const balances = await walletService.getWalletBalances(userId);

    // Calculate total USDT value
    const totalUSDTValue = balances.reduce((sum, balance) => sum + balance.usdtValue, 0);

    res.json({
      status: 'success',
      data: {
        balances,
        totalUSDTValue,
        lastUpdated: new Date()
      }
    });

  } catch (error: any) {
    logger.error('Get all balances error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get wallet balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Start monitoring deposits for user addresses
 * @route   POST /api/wallets/monitor
 * @access  Private
 */
export const startDepositMonitoring = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    // Ensure user has wallets
    await walletService.getUserWallets(userId);

    // Start monitoring if not already running
    const status = depositMonitorService.getStatus();
    if (!status.isRunning) {
      await depositMonitorService.startMonitoring();
    }

    res.json({
      status: 'success',
      message: 'Deposit monitoring started',
      data: {
        monitoringStatus: depositMonitorService.getStatus(),
        userId
      }
    });

  } catch (error: any) {
    logger.error('Start deposit monitoring error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to start deposit monitoring',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get deposit history for user
 * @route   GET /api/wallets/deposits/history
 * @access  Private
 */
export const getDepositHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';
    const limit = parseInt(req.query.limit as string) || 10;
    const page = parseInt(req.query.page as string) || 1;

    const deposits = await walletService.getDepositHistory(userId, limit * page);
    const paginatedDeposits = deposits.slice((page - 1) * limit, page * limit);

    res.json({
      status: 'success',
      data: {
        deposits: paginatedDeposits,
        pagination: {
          page,
          limit,
          total: deposits.length,
          pages: Math.ceil(deposits.length / limit)
        }
      }
    });

  } catch (error: any) {
    logger.error('Get deposit history error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get deposit history',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};




