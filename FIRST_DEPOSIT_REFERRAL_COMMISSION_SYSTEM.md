# First-Time Deposit Referral Commission System

## 📋 OVERVIEW

This document describes the implementation of a first-time deposit referral commission system that awards a 3% commission to referrers when their referred users make their first approved deposit.

## 🎯 SYSTEM SPECIFICATIONS

### **Trigger Condition**
- **Event:** When a newly registered user's first deposit is approved (status changes to "approved")
- **Frequency:** Only once per user (first deposit only)
- **Validation:** Prevents duplicate commissions for the same user

### **Commission Details**
- **Rate:** 3% of the approved deposit amount
- **Scope:** Only applies to the very first approved deposit per user
- **Exclusion:** Subsequent deposits from the same user do not generate referral commissions
- **Currency:** Same cryptocurrency as the deposit (USDT, BTC, ETH, etc.)

### **Referrer Identification**
- **Source:** User's `referrerId` field in the database
- **Validation:** Verify that the referrer exists and is an active user
- **Fallback:** If no referrer exists, mark user as having first deposit approved without commission

### **Commission Distribution**
- **Target:** Add 3% commission to the referrer's wallet `commissionBalance`
- **Asset:** Use the same cryptocurrency as the deposit
- **Transaction:** Create a transaction record for the commission payment
- **Update:** Update the referrer's `totalCommissionEarned`

## 🏗️ IMPLEMENTATION ARCHITECTURE

### **1. Database Schema Updates**

#### **User Model (`userModel.ts`)**
```typescript
interface IUser extends Document {
  // ... existing fields
  hasFirstDepositApproved?: boolean; // Track if user's first deposit has been approved
  firstDepositApprovedAt?: Date; // When the first deposit was approved
}
```

#### **Transaction Model (existing)**
```typescript
interface ITransaction extends Document {
  // ... existing fields
  metadata?: Record<string, any>; // Used to store referral commission metadata
}
```

### **2. Service Layer**

#### **FirstDepositCommissionService (`firstDepositCommissionService.ts`)**
```typescript
export class FirstDepositCommissionService {
  private static readonly COMMISSION_RATE = 0.03; // 3%

  // Main commission processing method
  static async processFirstDepositCommission(
    userId: mongoose.Types.ObjectId,
    depositAmount: number,
    cryptocurrency: string,
    depositTransactionId: mongoose.Types.ObjectId
  ): Promise<ReferralCommissionResult>

  // Check eligibility for commission
  static async checkFirstDepositEligibility(
    userId: mongoose.Types.ObjectId
  ): Promise<{ eligible: boolean; reason: string; referrerId?: string; }>

  // Get referral statistics
  static async getReferralStats(
    userId: mongoose.Types.ObjectId
  ): Promise<ReferralStats>
}
```

### **3. Controller Integration**

#### **Transaction Controller (`transactionController.ts`)**
- **Integration Point:** Deposit approval process in `updateTransactionStatus`
- **Trigger:** When `transaction.type === 'deposit' && status === 'approved'`
- **Process:** Call `FirstDepositCommissionService.processFirstDepositCommission`

### **4. API Endpoints**

#### **User Endpoints**
```
GET /api/referrals/first-deposit-stats/:userId
- Get first deposit commission statistics for a user
- Access: Private (User can view their own stats, Admin can view any)
```

#### **Admin Endpoints**
```
GET /api/referrals/admin/first-deposit-eligibility/:userId
- Check first deposit eligibility for a user
- Access: Admin only

GET /api/referrals/admin/first-deposit-commissions
- Get all first deposit commission transactions
- Access: Admin only

GET /api/referrals/admin/first-deposit-summary
- Get first deposit commission summary statistics
- Access: Admin only
```

## 🔄 PROCESS FLOW

### **1. User Registration with Referral**
```
1. New user registers with referral code
2. System sets user.referrerId = referrer._id
3. User makes first deposit
4. Admin approves deposit
```

### **2. Commission Processing**
```
1. Deposit status changes to "approved"
2. System checks if this is user's first approved deposit
3. If yes, and user has referrer:
   a. Calculate 3% commission
   b. Update referrer's wallet commissionBalance
   c. Create commission transaction record
   d. Update referrer's totalCommissionEarned
   e. Mark user as hasFirstDepositApproved = true
4. If no, skip commission processing
```

### **3. Database Transaction Flow**
```
START TRANSACTION
├── Find user and validate first deposit status
├── Find and validate referrer
├── Calculate commission amount (3%)
├── Update referrer's wallet
│   ├── Find or create cryptocurrency asset
│   ├── Add commission to commissionBalance
│   └── Update totalCommissionEarned
├── Update referrer's user record
├── Create commission transaction record
├── Mark user as having first deposit approved
└── COMMIT TRANSACTION
```

## 🛡️ VALIDATION & EDGE CASES

### **Validation Rules**
- ✅ **Positive Amount:** Deposit amount must be > 0
- ✅ **User Exists:** User making deposit must exist
- ✅ **Referrer Exists:** Referrer must exist and be active
- ✅ **First Deposit Only:** Commission only on first approved deposit
- ✅ **No Self-Referral:** Users cannot refer themselves

### **Edge Cases Handled**
- ✅ **No Referrer:** User has no referrer - mark as first deposit approved, no commission
- ✅ **Referrer Not Found:** Referrer deleted - mark as first deposit approved, no commission
- ✅ **Duplicate Processing:** Prevent duplicate commissions for same user
- ✅ **Transaction Failure:** Rollback all changes if any step fails
- ✅ **Invalid Amount:** Handle zero or negative deposit amounts

## 📊 COMMISSION TRACKING

### **Transaction Metadata**
```typescript
{
  referralCommission: true,
  referredUserId: "user_id",
  referredUserName: "John Doe",
  originalDepositAmount: 1000,
  commissionRate: 0.03,
  depositTransactionId: "deposit_transaction_id",
  isFirstDepositCommission: true
}
```

### **Wallet Updates**
```typescript
// Referrer's wallet asset
{
  symbol: "USDT",
  balance: 0,
  commissionBalance: 30, // 3% of $1000 = $30
  interestBalance: 0,
  mode: "commission"
}
```

## 🔍 MONITORING & ANALYTICS

### **Key Metrics**
- **Total Commissions Paid:** Sum of all first deposit commissions
- **Commission by Asset:** Breakdown by cryptocurrency
- **Users with First Deposits:** Count of users who completed first deposit
- **Eligible Users:** Users with referrers who haven't made first deposit yet
- **Conversion Rate:** Percentage of referred users who make first deposit

### **Admin Dashboard Data**
```typescript
{
  firstDepositCommissionsByAsset: [
    { _id: "USDT", totalAmount: 1500, count: 50 },
    { _id: "BTC", totalAmount: 0.05, count: 5 }
  ],
  usersWithFirstDepositCommissions: 55,
  usersWithReferrerNoFirstDeposit: 120,
  commissionRate: 0.03,
  totalEligibleUsers: 175
}
```

## 🧪 TESTING

### **Test Script**
- **Location:** `backend/src/scripts/testFirstDepositCommission.ts`
- **Coverage:** End-to-end testing of commission system
- **Scenarios:** 
  - ✅ Normal commission processing
  - ✅ Duplicate prevention
  - ✅ Edge cases (no referrer, invalid amounts)
  - ✅ Database consistency

### **Test Execution**
```bash
cd backend
npm run test:first-deposit-commission
```

## 🚀 DEPLOYMENT CHECKLIST

### **Database Migration**
- ✅ Add `hasFirstDepositApproved` field to User model
- ✅ Add `firstDepositApprovedAt` field to User model
- ✅ Create indexes for referral lookups

### **Code Deployment**
- ✅ Deploy `FirstDepositCommissionService`
- ✅ Update `transactionController.ts` with commission logic
- ✅ Deploy new API endpoints
- ✅ Update route configurations

### **Monitoring Setup**
- ✅ Set up logging for commission processing
- ✅ Monitor commission transaction creation
- ✅ Track system performance impact
- ✅ Set up alerts for commission failures

## 📈 BUSINESS IMPACT

### **Benefits**
- **Incentivized Referrals:** Encourages users to refer new members
- **User Acquisition:** Drives new user registration and deposits
- **Revenue Growth:** Increases platform deposits and activity
- **Community Building:** Builds network effects

### **Financial Impact**
- **Commission Cost:** 3% of first deposits (one-time cost per user)
- **Revenue Increase:** Higher user acquisition and deposit volumes
- **ROI:** Positive return through increased platform activity

## 🔧 MAINTENANCE

### **Regular Tasks**
- Monitor commission processing logs
- Review commission statistics monthly
- Validate referral data integrity
- Update commission rates if needed

### **Troubleshooting**
- Check transaction logs for failed commissions
- Verify user referral relationships
- Monitor database transaction rollbacks
- Review system performance metrics

---

## 📞 SUPPORT

For technical issues or questions about the First Deposit Referral Commission System:
- Check system logs in `/var/log/cryptoyield/`
- Review commission transactions in admin dashboard
- Contact development team for system modifications

**System Status:** ✅ Active and Operational
**Last Updated:** 2025-01-31
**Version:** 1.0.0
