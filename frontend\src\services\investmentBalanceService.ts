import axios from 'axios';
import { io, Socket } from 'socket.io-client';

const API_URL = import.meta.env.VITE_API_URL || '/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface InvestmentBalance {
  currency: string;
  totalEarnings: number;
  availableForWithdrawal: number;
  totalWithdrawn: number;
  activePackages: number;
  lastEarningDate?: string;
  // Enhanced fields for real-time integration
  principalAmount: number;
  interestAmount: number;
  commissionAmount: number;
  totalBalance: number;
  usdValue: number;
  dailyInterest: number;
  isLocked: boolean;
  lockExpiryDate?: string;
  daysUntilUnlock: number;
  canWithdraw: boolean;
  minimumThreshold: number;
  realTimePrice?: number;
  lastUpdated: string;
}

export interface WithdrawalEligibility {
  isEligible: boolean;
  availableBalance: number;
  minimumRequired: number;
  timeLockStatus: {
    isLocked: boolean;
    nextUnlockTime?: string;
    message?: string;
  };
  currency: string;
}

export interface InvestmentPackage {
  _id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'active' | 'completed' | 'cancelled';
  totalEarned: number;
  totalWithdrawn: number;
  dailyRate: number;
  createdAt: string;
  activatedAt?: string;
  activeDays: number; // Number of days since package was activated
  totalDays?: number; // Total investment period
  interestRate?: number;
  lastEarningDate?: string;
  lastWithdrawalDate?: string;
}

class InvestmentBalanceService {
  private socket: Socket | null = null;
  private balanceUpdateCallbacks: ((balances: InvestmentBalance[]) => void)[] = [];
  private priceUpdateCallbacks: ((prices: {[key: string]: number}) => void)[] = [];
  private isConnected = false;

  /**
   * Initialize WebSocket connection for real-time updates
   */
  initializeRealTimeUpdates(): void {
    const token = localStorage.getItem('token');
    if (!token || this.socket?.connected) return;

    try {
      this.socket = io(API_URL.replace('/api', ''), {
        path: '/ws',
        auth: { token },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000
      });

      this.socket.on('connect', () => {
        console.log('✅ Investment balance service connected to WebSocket');
        this.isConnected = true;
        this.socket?.emit('join_balance_updates');
      });

      this.socket.on('balance_update', (data: InvestmentBalance[]) => {
        console.log('📊 Real-time balance update received:', data);
        this.balanceUpdateCallbacks.forEach(callback => callback(data));
      });

      this.socket.on('price_update', (data: {[key: string]: number}) => {
        console.log('💰 Real-time price update received:', data);
        this.priceUpdateCallbacks.forEach(callback => callback(data));
      });

      this.socket.on('interest_distributed', (data: {
        userId: string;
        currency: string;
        amount: number;
        newBalance: number;
      }) => {
        console.log('🎯 Interest distribution received:', data);
        // Trigger balance refresh
        this.getInvestmentBalances().then(balances => {
          this.balanceUpdateCallbacks.forEach(callback => callback(balances));
        });
      });

      this.socket.on('disconnect', () => {
        console.log('❌ Investment balance service disconnected from WebSocket');
        this.isConnected = false;
      });

    } catch (error) {
      console.error('Error initializing WebSocket connection:', error);
    }
  }

  /**
   * Subscribe to real-time balance updates
   */
  onBalanceUpdate(callback: (balances: InvestmentBalance[]) => void): () => void {
    this.balanceUpdateCallbacks.push(callback);

    // Initialize WebSocket if not already connected
    if (!this.isConnected) {
      this.initializeRealTimeUpdates();
    }

    // Return unsubscribe function
    return () => {
      const index = this.balanceUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.balanceUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to real-time price updates
   */
  onPriceUpdate(callback: (prices: {[key: string]: number}) => void): () => void {
    this.priceUpdateCallbacks.push(callback);

    // Initialize WebSocket if not already connected
    if (!this.isConnected) {
      this.initializeRealTimeUpdates();
    }

    // Return unsubscribe function
    return () => {
      const index = this.priceUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.priceUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Get investment balances for all currencies with enhanced data
   */
  async getInvestmentBalances(): Promise<InvestmentBalance[]> {
    try {
      const response = await api.get('/investment-packages/balances');
      const balances = response.data.data || [];

      // Enhance balances with real-time data
      const enhancedBalances = balances.map((balance: any) => ({
        ...balance,
        lastUpdated: new Date().toISOString(),
        canWithdraw: balance.availableForWithdrawal >= balance.minimumThreshold,
        daysUntilUnlock: balance.lockExpiryDate
          ? Math.max(0, Math.ceil((new Date(balance.lockExpiryDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
          : 0
      }));

      return enhancedBalances;
    } catch (error) {
      console.error('Error fetching investment balances:', error);
      throw error;
    }
  }

  /**
   * Get investment balance for a specific currency
   */
  async getInvestmentBalance(currency: string): Promise<InvestmentBalance | null> {
    try {
      const response = await api.get(`/investment-packages/balances/${currency.toUpperCase()}`);
      return response.data.data || null;
    } catch (error) {
      console.error(`Error fetching investment balance for ${currency}:`, error);
      throw error;
    }
  }

  /**
   * Check withdrawal eligibility for a specific amount and currency
   * Enhanced for integration with Enhanced Withdrawal System
   */
  async checkWithdrawalEligibility(
    currency: string,
    amount: number,
    withdrawalType: 'interest' | 'commission' | 'referral' = 'interest'
  ): Promise<WithdrawalEligibility> {
    try {
      const response = await api.post('/investment-packages/withdrawal-eligibility', {
        currency: currency.toUpperCase(),
        amount,
        withdrawalType
      });

      const eligibility = response.data.data;

      // Enhanced eligibility checking with time-based restrictions
      const now = new Date();
      const utc3Offset = 3 * 60; // UTC+3 in minutes
      const utc3Time = new Date(now.getTime() + (utc3Offset * 60 * 1000));
      const currentHour = utc3Time.getUTCHours();

      // Check time-based restrictions (03:00 UTC+3)
      if (currentHour < 3) {
        return {
          ...eligibility,
          isEligible: false,
          timeLockStatus: {
            ...eligibility.timeLockStatus,
            isLocked: true,
            message: 'Withdrawals are only available after 03:00 UTC+3 daily interest distribution'
          }
        };
      }

      return eligibility;
    } catch (error) {
      console.error('Error checking withdrawal eligibility:', error);
      throw error;
    }
  }

  /**
   * Get user's active investment packages
   */
  async getActiveInvestmentPackages(): Promise<InvestmentPackage[]> {
    try {
      const response = await api.get('/investment-packages/packages');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching active investment packages:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive investment data including packages and balances
   */
  async getComprehensiveInvestmentData(): Promise<{
    balances: InvestmentBalance[];
    packages: InvestmentPackage[];
    totalEarnings: number;
    totalWithdrawn: number;
  }> {
    try {
      const response = await api.get('/investment-packages/comprehensive');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching comprehensive investment data:', error);
      throw error;
    }
  }

  /**
   * Request withdrawal from investment earnings
   */
  async requestWithdrawal(params: {
    amount: number;
    currency: string;
    targetAddress: string;
    withdrawalType: 'interest' | 'commission';
    network?: string;
    memo?: string;
  }): Promise<{
    success: boolean;
    transactionId: string;
    estimatedProcessingTime: string;
    message: string;
  }> {
    try {
      const response = await api.post('/investment-packages/withdraw', params);
      return response.data;
    } catch (error) {
      console.error('Error requesting withdrawal:', error);
      throw error;
    }
  }

  /**
   * Get withdrawal history
   */
  async getWithdrawalHistory(params?: {
    page?: number;
    limit?: number;
    currency?: string;
    status?: string;
  }): Promise<{
    withdrawals: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      const response = await api.get('/investment-packages/withdrawals', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching withdrawal history:', error);
      throw error;
    }
  }

  /**
   * Calculate potential earnings for an amount
   */
  async calculatePotentialEarnings(amount: number, currency: string, days: number = 30): Promise<{
    dailyEarnings: number;
    monthlyEarnings: number;
    annualEarnings: number;
    dailyRate: number;
  }> {
    try {
      const response = await api.post('/investment-packages/calculate-earnings', {
        amount,
        currency: currency.toUpperCase(),
        days
      });
      return response.data.data;
    } catch (error) {
      console.error('Error calculating potential earnings:', error);
      throw error;
    }
  }

  /**
   * Get time lock status for withdrawals
   */
  async getTimeLockStatus(): Promise<{
    isLocked: boolean;
    nextUnlockTime?: string;
    currentTime: string;
    message: string;
  }> {
    try {
      const response = await api.get('/investment-packages/time-lock-status');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching time lock status:', error);
      throw error;
    }
  }

  /**
   * Get minimum withdrawal amounts for all currencies
   */
  async getMinimumWithdrawals(): Promise<Record<string, number>> {
    try {
      const response = await api.get('/investment-packages/minimum-withdrawals');
      return response.data.data || {};
    } catch (error) {
      console.error('Error fetching minimum withdrawals:', error);
      // Return default minimums if API fails
      return {
        USDT: 50,
        BTC: 0.001,
        ETH: 0.01,
        BNB: 0.1,
        DOGE: 100,
        TRX: 100
      };
    }
  }

  /**
   * Validate withdrawal request before submission
   */
  async validateWithdrawalRequest(params: {
    amount: number;
    currency: string;
    targetAddress: string;
    withdrawalType: 'interest' | 'commission';
  }): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    estimatedFee: number;
    netAmount: number;
  }> {
    try {
      const response = await api.post('/investment-packages/validate-withdrawal', params);
      return response.data.data;
    } catch (error) {
      console.error('Error validating withdrawal request:', error);
      throw error;
    }
  }

  /**
   * Get real-time cryptocurrency prices
   */
  async getCryptocurrencyPrices(currencies?: string[]): Promise<{[key: string]: number}> {
    try {
      const params = currencies ? { currencies: currencies.join(',') } : {};
      const response = await api.get('/crypto/prices', { params });
      return response.data.data || {};
    } catch (error) {
      console.error('Error fetching cryptocurrency prices:', error);
      throw error;
    }
  }

  /**
   * Get investment summary with real-time data
   */
  async getInvestmentSummary(): Promise<{
    totalInvested: number;
    totalEarned: number;
    dailyEarnings: number;
    activePackages: number;
    totalUSDValue: number;
    canWithdraw: boolean;
    nextInterestTime: string;
    timeUntilNext: string;
    currencies: {[key: string]: InvestmentBalance};
  }> {
    try {
      const response = await api.get('/investment-packages/summary');
      const summary = response.data.data;

      // Get current balances for detailed breakdown
      const balances = await this.getInvestmentBalances();
      const currencyMap: {[key: string]: InvestmentBalance} = {};
      balances.forEach(balance => {
        currencyMap[balance.currency] = balance;
      });

      return {
        ...summary,
        currencies: currencyMap,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching investment summary:', error);
      throw error;
    }
  }

  /**
   * Disconnect WebSocket connection
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.balanceUpdateCallbacks = [];
      this.priceUpdateCallbacks = [];
      console.log('🔌 Investment balance service disconnected');
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Force refresh balances and notify subscribers
   */
  async refreshBalances(): Promise<void> {
    try {
      const balances = await this.getInvestmentBalances();
      this.balanceUpdateCallbacks.forEach(callback => callback(balances));
    } catch (error) {
      console.error('Error refreshing balances:', error);
    }
  }
}

export const investmentBalanceService = new InvestmentBalanceService();
export default investmentBalanceService;
