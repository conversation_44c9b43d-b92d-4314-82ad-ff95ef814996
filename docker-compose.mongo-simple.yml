version: '3.8'

services:
  # MongoDB service with replica set for transaction support (simplified)
  mongodb:
    image: mongo:7.0
    container_name: cryptoyield-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: cryptoyield_admin
      MONGO_INITDB_ROOT_PASSWORD: secure_password123
      MONGO_INITDB_DATABASE: cryptoyield
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongo-init-replica.js:/docker-entrypoint-initdb.d/mongo-init-replica.js:ro
    ports:
      - "27017:27017"
    networks:
      - cryptoyield-network
    command: mongod --replSet rs0 --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  # Redis service for caching and session management
  redis:
    image: redis:7-alpine
    container_name: cryptoyield-redis
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cryptoyield-network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # Mongo Express service (MongoDB admin interface)
  mongo-express:
    image: mongo-express:latest
    container_name: cryptoyield-mongo-express
    restart: always
    depends_on:
      - mongodb
    environment:
      # MongoDB connection settings
      ME_CONFIG_MONGODB_ADMINUSERNAME: cryptoyield_admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: secure_password123
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_MONGODB_AUTH_DATABASE: admin
      
      # Basic authentication for Mongo Express web interface
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
      
      # Mongo Express settings
      ME_CONFIG_MONGODB_ENABLE_ADMIN: "true"
      ME_CONFIG_OPTIONS_EDITORTHEME: ambiance
      
      # Connection URL (simplified without replica set initially)
      ME_CONFIG_MONGODB_URL: ***********************************************************************************
    ports:
      - "8081:8081"
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 45s

networks:
  cryptoyield-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
