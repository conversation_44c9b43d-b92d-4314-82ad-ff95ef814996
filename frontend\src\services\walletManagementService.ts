import { api } from './api';

export interface WithdrawalAddress {
  _id: string;
  currency: string;
  address: string;
  formattedAddress: string;
  label: string;
  network: string;
  isDefault: boolean;
  isActive: boolean;
  isVerified: boolean;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface SupportedCurrency {
  currency: string;
  name: string;
  networks: string[];
  addressFormat: string;
}

export interface CreateAddressRequest {
  currency: string;
  address: string;
  label: string;
  network?: string;
}

export interface UpdateAddressRequest {
  label?: string;
  isDefault?: boolean;
}

export interface VerifyAddressRequest {
  verificationCode: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  count?: number;
}

export const walletManagementService = {
  // Get all withdrawal addresses for user
  async getUserAddresses(currency?: string): Promise<ApiResponse<WithdrawalAddress[]>> {
    const params = currency ? { currency } : {};
    const response = await api.get('/wallet-management/addresses', { params });
    return response.data;
  },

  // Get withdrawal address by ID
  async getAddressById(id: string): Promise<ApiResponse<WithdrawalAddress>> {
    const response = await api.get(`/wallet-management/addresses/${id}`);
    return response.data;
  },

  // Add new withdrawal address
  async addAddress(data: CreateAddressRequest): Promise<ApiResponse<WithdrawalAddress & { verificationRequired: boolean }>> {
    const response = await api.post('/wallet-management/addresses', data);
    return response.data;
  },

  // Update withdrawal address
  async updateAddress(id: string, data: UpdateAddressRequest): Promise<ApiResponse<WithdrawalAddress>> {
    const response = await api.put(`/wallet-management/addresses/${id}`, data);
    return response.data;
  },

  // Delete withdrawal address
  async deleteAddress(id: string): Promise<ApiResponse<void>> {
    const response = await api.delete(`/wallet-management/addresses/${id}`);
    return response.data;
  },

  // Verify withdrawal address
  async verifyAddress(id: string, data: VerifyAddressRequest): Promise<ApiResponse<{ id: string; isVerified: boolean }>> {
    const response = await api.post(`/wallet-management/addresses/${id}/verify`, data);
    return response.data;
  },

  // Set default withdrawal address
  async setDefaultAddress(id: string): Promise<ApiResponse<void>> {
    const response = await api.post(`/wallet-management/addresses/${id}/set-default`);
    return response.data;
  },

  // Resend verification code
  async resendVerificationCode(id: string): Promise<ApiResponse<void>> {
    const response = await api.post(`/wallet-management/addresses/${id}/resend-verification`);
    return response.data;
  },

  // Get supported currencies and networks
  async getSupportedCurrencies(): Promise<ApiResponse<SupportedCurrency[]>> {
    const response = await api.get('/wallet-management/supported-currencies');
    return response.data;
  },

  // Validate address format (client-side helper)
  validateAddressFormat(address: string, currency: string): boolean {
    switch (currency.toUpperCase()) {
      case 'BTC':
        // Bitcoin address validation (Legacy, SegWit)
        return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(address);
      case 'ETH':
      case 'USDT':
      case 'BNB':
      case 'LINK':
      case 'UNI':
        // Ethereum-based address validation
        return /^0x[a-fA-F0-9]{40}$/.test(address);
      case 'ADA':
        // Cardano address validation (simplified)
        return /^addr1[a-z0-9]{58}$/.test(address);
      case 'DOT':
        // Polkadot address validation (simplified)
        return /^1[a-zA-Z0-9]{47}$/.test(address);
      default:
        return false;
    }
  },

  // Get address format hint for currency
  getAddressFormatHint(currency: string): string {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return 'Legacy (1...) or SegWit (bc1...) format';
      case 'ETH':
      case 'USDT':
      case 'BNB':
      case 'LINK':
      case 'UNI':
        return '0x... format (42 characters)';
      case 'ADA':
        return 'addr1... format (Cardano address)';
      case 'DOT':
        return '1... format (Polkadot address)';
      default:
        return 'Please enter a valid address for this currency';
    }
  },

  // Get network options for currency
  getNetworkOptions(currency: string): string[] {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return ['mainnet'];
      case 'ETH':
        return ['mainnet', 'testnet'];
      case 'USDT':
        return ['ethereum', 'tron', 'bsc'];
      case 'BNB':
        return ['bsc', 'binance-chain'];
      case 'ADA':
        return ['mainnet'];
      case 'DOT':
        return ['mainnet'];
      case 'LINK':
        return ['ethereum', 'bsc'];
      case 'UNI':
        return ['ethereum'];
      default:
        return ['mainnet'];
    }
  }
};

// Enhanced withdrawal service
export interface WithdrawalRequest {
  addressId: string;
  amount: number;
  currency: string;
  twoFactorCode?: string;
}

export interface WithdrawalResponse {
  transactionId: string;
  amount: number;
  originalAmount: number;
  withdrawalFee: number;
  currency: string;
  address: string;
  addressLabel: string;
  network: string;
  status: string;
  estimatedProcessingTime: string;
}

export interface WithdrawalHistory {
  id: string;
  amount: number;
  currency: string;
  address: string;
  status: string;
  txHash?: string;
  network: string;
  description: string;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface WithdrawalLimits {
  minimum: number;
  maximum: number;
  dailyLimit: number;
  feePercentage: number;
  minimumFee: number;
  estimatedTime: string;
}

export interface FeeEstimation {
  originalAmount: number;
  withdrawalFee: number;
  netAmount: number;
  currency: string;
  feePercentage: number;
  limits: WithdrawalLimits;
}

export const enhancedWithdrawalService = {
  // Create withdrawal request
  async createWithdrawal(data: WithdrawalRequest): Promise<ApiResponse<WithdrawalResponse>> {
    const response = await api.post('/enhanced-withdrawals/create', data);
    return response.data;
  },

  // Get withdrawal history
  async getWithdrawalHistory(params?: {
    page?: number;
    limit?: number;
    currency?: string;
    status?: string;
  }): Promise<ApiResponse<WithdrawalHistory[]> & { pagination: any }> {
    const response = await api.get('/enhanced-withdrawals/history', { params });
    return response.data;
  },

  // Get withdrawal limits
  async getWithdrawalLimits(currency?: string): Promise<ApiResponse<WithdrawalLimits | Record<string, WithdrawalLimits>>> {
    const params = currency ? { currency } : {};
    const response = await api.get('/enhanced-withdrawals/limits', { params });
    return response.data;
  },

  // Estimate withdrawal fee
  async estimateWithdrawalFee(amount: number, currency: string): Promise<ApiResponse<FeeEstimation>> {
    const response = await api.post('/enhanced-withdrawals/estimate-fee', { amount, currency });
    return response.data;
  },

  // Cancel withdrawal
  async cancelWithdrawal(id: string): Promise<ApiResponse<{ transactionId: string; refundedAmount: number; currency: string }>> {
    const response = await api.post(`/enhanced-withdrawals/${id}/cancel`);
    return response.data;
  }
};
