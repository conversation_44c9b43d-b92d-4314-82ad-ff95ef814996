# MongoDB Docker Setup for CryptoYield

This document provides comprehensive instructions for setting up MongoDB with <PERSON><PERSON> to support transactions for the CryptoYield backend application.

## 🎯 Overview

The MongoDB setup includes:
- **MongoDB 7.0** with replica set (`rs0`) for transaction support
- **Redis 7** for caching and session management
- **Mongo Express** for database administration
- **Keyfile authentication** for security
- **Health checks** for reliable startup
- **Persistent volumes** for data storage
- **Network isolation** with custom bridge network

## 📋 Requirements

- Docker and Docker Compose installed
- At least 2GB RAM available for containers
- Ports 27017, 6379, and 8081 available on host machine
- Sufficient disk space for data persistence

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**For Linux/Mac:**
```bash
chmod +x setup-mongodb-docker.sh
./setup-mongodb-docker.sh
```

**For Windows:**
```cmd
setup-mongodb-docker.bat
```

### Option 2: Manual Setup

1. **Create data directories:**
```bash
mkdir -p ./data/mongodb ./data/mongodb-config ./data/redis
```

2. **Start services:**
```bash
docker-compose -f docker-compose.mongo.yml up -d
```

3. **Wait for services to be ready:**
```bash
# Check MongoDB health
docker-compose -f docker-compose.mongo.yml ps

# Test transactions
./test-mongodb-transactions.sh
```

## 🔧 Configuration Details

### MongoDB Container

- **Container Name**: cryptoyield-mongodb
- **Port**: 27017 (mapped to host port 27017)
- **Username**: admin
- **Password**: password
- **Database**: cryptoyieldhub
- **Connection String**: `**********************************************************************`

### Mongo Express (MongoDB Admin Interface)

- **URL**: http://localhost:8081
- **Username**: admin
- **Password**: admin123

## Troubleshooting

### Connection Issues

If the backend cannot connect to MongoDB, check the following:

1. Make sure the MongoDB container is running:
   ```bash
   docker ps | grep mongodb
   ```

2. Check MongoDB logs:
   ```bash
   docker logs cryptoyield-mongodb
   ```

3. Verify the connection string in `backend/.env.development`:
   - When running the backend directly on your host machine, use: `************************************************************************`
   - When running the backend in Docker, use: `**********************************************************************`

### Data Persistence

MongoDB data is stored in a Docker volume named `mongodb_data`. This ensures your data persists even if you stop or remove the container.

To completely reset the database, remove the volume:
```bash
docker-compose -f docker-compose.mongodb.yml down -v
```

## Running Backend in Docker

If you want to run both the backend and MongoDB in Docker, use the development Docker Compose file:

```bash
docker-compose -f docker-compose.development.yml up -d
```

This will start both services in Docker containers with proper networking between them.
