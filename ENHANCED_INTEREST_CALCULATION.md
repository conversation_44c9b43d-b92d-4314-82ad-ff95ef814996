# Enhanced Daily Interest Calculation System

## Overview

This document describes the enhanced daily interest calculation system that provides comprehensive, atomic, and auditable interest payments for investment packages.

## Features

### ✅ **Atomic Database Transactions**
- All operations wrapped in MongoDB transactions with automatic rollback
- Ensures data consistency across multiple collections
- Retry logic for failed transactions with exponential backoff

### ✅ **Comprehensive Wallet Updates**
- Updates `assets.interestBalance` for the matching cryptocurrency
- Updates `totalInterestEarned` field in wallet model
- Creates or updates asset entries as needed

### ✅ **Transaction Record Creation**
- Creates 'interest' type transactions for each payment
- Includes metadata with investment package reference
- Links to wallet and user for complete audit trail

### ✅ **Payment History Logging**
- New PaymentHistory model for detailed payment tracking
- Records payment type, amount, currency, and metadata
- Supports querying by user, package, date range, and payment type

### ✅ **Enhanced Error Handling**
- Comprehensive error logging with context
- Graceful handling of individual package failures
- Detailed error reporting and monitoring

### ✅ **Batch Processing**
- Processes packages in configurable batches (default: 10)
- Prevents database overload during large-scale calculations
- Progress tracking and reporting

## Architecture

### Core Components

1. **InterestCalculationService** (`backend/src/services/interestCalculationService.ts`)
   - Main service handling all interest calculations
   - Atomic transaction management
   - Batch processing logic

2. **PaymentHistory Model** (`backend/src/models/paymentHistoryModel.ts`)
   - New model for tracking all payment activities
   - Comprehensive indexing for efficient queries
   - Static methods for common operations

3. **Enhanced CronService** (`backend/src/services/cronService.ts`)
   - Updated to use the new interest calculation service
   - Improved error handling and logging
   - System notification integration

4. **Wallet Service Updates** (`backend/src/services/walletService.ts`)
   - New methods for interest balance management
   - Session-aware database operations
   - Helper methods for balance queries

## Database Schema

### PaymentHistory Collection
```typescript
{
  userId: ObjectId,              // Reference to user
  investmentPackageId: ObjectId, // Reference to investment package
  amount: Number,                // Payment amount
  currency: String,              // Currency (BTC, ETH, USDT, etc.)
  paymentDate: Date,             // When payment was made
  paymentType: String,           // 'interest', 'commission', 'bonus', etc.
  status: String,                // 'completed', 'pending', 'failed'
  transactionId: ObjectId,       // Reference to transaction record
  walletId: ObjectId,            // Reference to wallet
  metadata: {                    // Additional payment details
    dailyInterestRate: Number,
    activeDays: Number,
    totalEarned: Number,
    compoundEnabled: Boolean,
    calculationMethod: String,
    previousBalance: Number,
    newBalance: Number
  }
}
```

### Updated Wallet Schema
```typescript
{
  assets: [{
    symbol: String,              // Currency symbol
    balance: Number,             // Main balance
    commissionBalance: Number,   // Commission earnings
    interestBalance: Number,     // ✅ Interest earnings (NEW)
    mode: String                 // 'commission' or 'interest'
  }],
  totalInterestEarned: Number    // ✅ Total interest across all assets (NEW)
}
```

## Usage

### Automated Daily Calculation
The system automatically runs at 3:00 AM Turkey time via cron job:
```typescript
// Runs automatically - no manual intervention needed
// Processes all active investment packages
// Updates wallets, creates transactions, logs payments
```

### Manual Calculation
For testing or administrative purposes:
```typescript
import cronService from './services/cronService';

// Calculate for all active packages
const result = await cronService.calculateInterestManually();

// Calculate for specific package
const result = await cronService.calculateInterestManually('packageId');
```

### Query Payment History
```typescript
import PaymentHistory from './models/paymentHistoryModel';

// Get user's interest payments
const payments = await PaymentHistory.getUserPaymentHistory(userId, {
  paymentType: 'interest',
  currency: 'BTC',
  startDate: new Date('2024-01-01'),
  limit: 50
});

// Get package payment history
const packagePayments = await PaymentHistory.getPackagePaymentHistory(packageId);

// Get total interest earned
const totalInterest = await PaymentHistory.getTotalPaymentsByType(userId, 'interest', 'BTC');
```

## Configuration

### Batch Size
Default batch size is 10 packages per transaction. Adjust in `interestCalculationService.ts`:
```typescript
const batchSize = 10; // Modify as needed
```

### Retry Logic
Default retry configuration:
```typescript
{
  maxRetries: 3,
  retryDelay: 1000 // milliseconds
}
```

## Monitoring and Logging

### Comprehensive Logging
- Package-level calculation details
- Batch processing progress
- Error tracking with context
- Performance metrics

### System Notifications
- Daily calculation completion
- Error notifications
- Performance alerts

### Audit Trail
- All operations logged in AuditTrail collection
- Payment history for compliance
- Transaction records for financial audit

## Testing

Run the test script to verify functionality:
```bash
npm run test:interest-calculation
# or
ts-node backend/src/scripts/testInterestCalculation.ts
```

## Error Handling

### Transaction Rollback
If any step fails during interest calculation:
1. All changes are automatically rolled back
2. Error is logged with full context
3. Processing continues with next package
4. Summary includes failed packages

### Common Error Scenarios
- Package not found or inactive
- Wallet not found for user
- Database connection issues
- Invalid calculation amounts

## Performance Considerations

### Batch Processing
- Processes packages in small batches
- Prevents database lock contention
- Allows for progress monitoring

### Database Optimization
- Proper indexing on PaymentHistory collection
- Efficient queries with session support
- Connection pooling for concurrent operations

### Memory Management
- Processes packages in batches to limit memory usage
- Garbage collection between batches
- Efficient data structures

## Security

### Data Integrity
- Atomic transactions ensure consistency
- Audit trails for all operations
- Immutable payment history records

### Access Control
- Service-level access only
- No direct database manipulation
- Comprehensive logging for security audit

## Future Enhancements

1. **Real-time Notifications**
   - WebSocket notifications for interest payments
   - Email notifications for large payments

2. **Advanced Analytics**
   - Interest calculation statistics
   - Performance metrics dashboard
   - Predictive analytics

3. **Multi-currency Support**
   - Cross-currency interest calculations
   - Dynamic exchange rate integration
   - Currency conversion tracking

4. **Compound Interest**
   - Enhanced compound calculation logic
   - Configurable compounding periods
   - Historical compound tracking
