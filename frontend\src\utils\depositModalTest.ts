/**
 * Deposit Modal Integration Test Utility
 */

import { apiClient } from './apiClient';

export interface DepositTestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

export class DepositModalTester {
  private results: DepositTestResult[] = [];

  async runDepositTests(): Promise<DepositTestResult[]> {
    this.results = [];
    
    console.log('🔍 Starting Deposit Modal Integration Tests...');
    
    await this.testSystemCryptoAddressesAPI();
    await this.testDepositAddressForEachCurrency();
    await this.testAddressTransformation();
    await this.testNetworkHandling();
    
    console.log('✅ Deposit Modal Tests Completed');
    console.table(this.results);
    
    return this.results;
  }

  private async testSystemCryptoAddressesAPI(): Promise<void> {
    try {
      const response = await apiClient.get('/system/crypto-addresses');
      
      if (response && response.data && response.data.cryptoAddresses) {
        const cryptoCount = response.data.cryptoAddresses.length;
        const enabledCount = response.data.cryptoAddresses.filter((crypto: any) => crypto.enabled).length;
        
        this.addResult(
          'System Crypto Addresses API', 
          'PASS', 
          `Successfully fetched ${cryptoCount} cryptocurrencies (${enabledCount} enabled)`,
          response.data.cryptoAddresses
        );
      } else {
        this.addResult('System Crypto Addresses API', 'FAIL', 'Invalid response format', response);
      }
    } catch (error) {
      this.addResult('System Crypto Addresses API', 'FAIL', 'Failed to fetch crypto addresses', error);
    }
  }

  private async testDepositAddressForEachCurrency(): Promise<void> {
    const currencies = ['BTC', 'ETH', 'USDT', 'DOGE', 'TRX'];
    
    for (const currency of currencies) {
      try {
        const response = await apiClient.get('/system/crypto-addresses');
        
        if (response && response.data && response.data.cryptoAddresses) {
          const currencyData = response.data.cryptoAddresses.find(
            (crypto: any) => crypto.currency === currency
          );
          
          if (currencyData && currencyData.addresses && currencyData.addresses.length > 0) {
            const addressCount = currencyData.addresses.length;
            const enabled = currencyData.enabled;
            
            this.addResult(
              `${currency} Address Availability`,
              enabled ? 'PASS' : 'WARNING',
              `${addressCount} addresses available (${enabled ? 'enabled' : 'disabled'})`,
              currencyData
            );
          } else {
            this.addResult(
              `${currency} Address Availability`,
              'FAIL',
              'No addresses configured',
              currencyData
            );
          }
        }
      } catch (error) {
        this.addResult(`${currency} Address Availability`, 'FAIL', 'Failed to check addresses', error);
      }
    }
  }

  private async testAddressTransformation(): Promise<void> {
    try {
      const response = await apiClient.get('/system/crypto-addresses');
      
      if (response && response.data && response.data.cryptoAddresses) {
        const btcData = response.data.cryptoAddresses.find((crypto: any) => crypto.currency === 'BTC');
        
        if (btcData && btcData.addresses && btcData.addresses.length > 0) {
          const currentIndex = btcData.currentIndex || 0;
          const selectedAddress = btcData.addresses[currentIndex];
          
          // Test address transformation logic
          let transformedAddress = '';
          if (typeof selectedAddress === 'string') {
            transformedAddress = selectedAddress;
          } else if (selectedAddress && selectedAddress.address) {
            transformedAddress = selectedAddress.address;
          }
          
          if (transformedAddress) {
            this.addResult(
              'Address Transformation',
              'PASS',
              `Successfully transformed address: ${transformedAddress.substring(0, 10)}...`,
              { originalAddress: selectedAddress, transformedAddress }
            );
          } else {
            this.addResult(
              'Address Transformation',
              'FAIL',
              'Failed to transform address',
              selectedAddress
            );
          }
        } else {
          this.addResult('Address Transformation', 'WARNING', 'No BTC addresses to test transformation');
        }
      }
    } catch (error) {
      this.addResult('Address Transformation', 'FAIL', 'Failed to test address transformation', error);
    }
  }

  private async testNetworkHandling(): Promise<void> {
    try {
      const response = await apiClient.get('/system/crypto-addresses');
      
      if (response && response.data && response.data.cryptoAddresses) {
        const trxData = response.data.cryptoAddresses.find((crypto: any) => crypto.currency === 'TRX');
        
        if (trxData && trxData.addresses && trxData.addresses.length > 0) {
          // Check if TRX addresses have network information
          const hasNetworkInfo = trxData.addresses.some((addr: any) => 
            typeof addr === 'object' && addr.network
          );
          
          if (hasNetworkInfo) {
            this.addResult(
              'Network Handling',
              'PASS',
              'Network-aware addresses detected for TRX',
              trxData.addresses
            );
          } else {
            this.addResult(
              'Network Handling',
              'WARNING',
              'No network information found in TRX addresses',
              trxData.addresses
            );
          }
        } else {
          this.addResult('Network Handling', 'WARNING', 'No TRX addresses to test network handling');
        }
      }
    } catch (error) {
      this.addResult('Network Handling', 'FAIL', 'Failed to test network handling', error);
    }
  }

  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any): void {
    this.results.push({
      test,
      status,
      message,
      details
    });
    
    const emoji = status === 'PASS' ? '✅' : status === 'WARNING' ? '⚠️' : '❌';
    console.log(`${emoji} ${test}: ${message}`);
  }

  // Method to get summary of test results
  getSummary(): { total: number; passed: number; failed: number; warnings: number } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    
    return { total, passed, failed, warnings };
  }
}

// Export singleton instance
export const depositModalTester = new DepositModalTester();

// Global function for easy testing from browser console
(window as any).runDepositTests = () => depositModalTester.runDepositTests();
(window as any).testDepositModal = () => {
  console.log('🧪 Running comprehensive deposit modal tests...');
  return depositModalTester.runDepositTests();
};
