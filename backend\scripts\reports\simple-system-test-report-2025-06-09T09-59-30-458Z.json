{"testSuite": "Simple System Test", "timestamp": "2025-06-09T09:59:30.455Z", "summary": {"totalTests": 11, "passed": 4, "failed": 6, "skipped": 1, "totalDuration": 681, "successRate": "36.36"}, "testConfiguration": {"baseUrl": "http://localhost:5000/api", "frontendUrl": "http://localhost:3003", "testUser": {"email": "<EMAIL>", "password": "TestPassword123!", "firstName": "Simple", "lastName": "Test"}, "testData": {"currencies": ["USDT", "BTC", "ETH"], "investmentAmounts": [100, 500, 1000]}}, "testData": {"testUserId": "6846b08129467e89094f8eb5", "createdPackageIds": [], "authToken": "Not generated"}, "results": [{"testName": "Database Connection", "category": "Infrastructure", "status": "PASS", "duration": 50, "details": "Connected to: mongodb://mongodb:27017/cryptoyield?replicaSet=rs0", "timestamp": "2025-06-09T09:59:29.817Z"}, {"testName": "API Health Check", "category": "API", "status": "PASS", "duration": 30, "details": "Status: ok", "timestamp": "2025-06-09T09:59:29.848Z"}, {"testName": "User Registration", "category": "Authentication", "status": "PASS", "duration": 293, "details": "User registered successfully", "timestamp": "2025-06-09T09:59:30.142Z"}, {"testName": "User Login", "category": "Authentication", "status": "PASS", "duration": 225, "details": "Login successful", "timestamp": "2025-06-09T09:59:30.368Z"}, {"testName": "Wallet Operations", "category": "Wallet", "status": "FAIL", "duration": 40, "details": "Failed to retrieve wallet", "error": "Not authorized, no token", "timestamp": "2025-06-09T09:59:30.409Z"}, {"testName": "Investment Creation", "category": "Investment", "status": "FAIL", "duration": 10, "details": "Failed to create investment packages", "error": "Transaction validation failed: walletId: Path `walletId` is required.", "timestamp": "2025-06-09T09:59:30.420Z"}, {"testName": "Interest Calculation", "category": "Investment", "status": "SKIP", "duration": 0, "details": "No investment packages available", "timestamp": "2025-06-09T09:59:30.421Z"}, {"testName": "User Profile", "category": "API", "status": "FAIL", "duration": 8, "details": "Endpoint not accessible", "error": 401, "timestamp": "2025-06-09T09:59:30.429Z"}, {"testName": "Wallet Info", "category": "API", "status": "FAIL", "duration": 7, "details": "Endpoint not accessible", "error": 401, "timestamp": "2025-06-09T09:59:30.437Z"}, {"testName": "Investment Packages", "category": "API", "status": "FAIL", "duration": 8, "details": "Endpoint not accessible", "error": 401, "timestamp": "2025-06-09T09:59:30.445Z"}, {"testName": "Transaction History", "category": "API", "status": "FAIL", "duration": 10, "details": "Endpoint not accessible", "error": 401, "timestamp": "2025-06-09T09:59:30.455Z"}], "categoryBreakdown": {"Infrastructure": {"total": 1, "passed": 1, "failed": 0, "skipped": 0, "successRate": "100.00", "totalDuration": 50}, "API": {"total": 5, "passed": 1, "failed": 4, "skipped": 0, "successRate": "20.00", "totalDuration": 63}, "Authentication": {"total": 2, "passed": 2, "failed": 0, "skipped": 0, "successRate": "100.00", "totalDuration": 518}, "Wallet": {"total": 1, "passed": 0, "failed": 1, "skipped": 0, "successRate": "0.00", "totalDuration": 40}, "Investment": {"total": 2, "passed": 0, "failed": 1, "skipped": 1, "successRate": "0.00", "totalDuration": 10}}}