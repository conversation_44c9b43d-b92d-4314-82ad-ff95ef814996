import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  ModalO<PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  useToast,
  Box,
  Icon,
  Badge,
  Divider
} from '@chakra-ui/react';
import { FaTrash, FaExclamationTriangle, FaStar } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { walletManagementService, WithdrawalAddress } from '../../services/walletManagementService';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';

interface DeleteWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  address: WithdrawalAddress | null;
}

const DeleteWalletModal: React.FC<DeleteWalletModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess, 
  address 
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  const [loading, setLoading] = useState(false);

  // Handle deletion
  const handleDelete = async () => {
    if (!address) return;

    setLoading(true);
    try {
      await walletManagementService.deleteAddress(address._id);

      toast({
        title: t('Success'),
        description: t('Wallet address deleted successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      onSuccess();
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to delete wallet address'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!address) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Icon as={FaTrash} color="red.500" />
            <Text>{t('Delete Wallet Address')}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Warning Alert */}
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={1} flex={1}>
                <Text fontWeight="semibold">{t('Confirm Deletion')}</Text>
                <Text fontSize="sm">
                  {t('This action cannot be undone. Are you sure you want to delete this wallet address?')}
                </Text>
              </VStack>
            </Alert>

            {/* Address Info */}
            <Box border="1px" borderColor="gray.200" borderRadius="md" p={4}>
              <VStack spacing={3} align="center">
                <Icon
                  as={getCryptoIcon(address.currency)}
                  color={getCryptoColor(address.currency)}
                  boxSize={12}
                />
                <VStack spacing={1}>
                  <HStack spacing={2}>
                    <Text fontSize="lg" fontWeight="bold">
                      {address.currency} - {address.label}
                    </Text>
                    {address.isDefault && (
                      <Badge colorScheme="blue" variant="solid">
                        <Icon as={FaStar} mr={1} />
                        {t('Default')}
                      </Badge>
                    )}
                  </HStack>
                  <Badge
                    colorScheme={address.isVerified ? 'green' : 'orange'}
                    variant="subtle"
                  >
                    {address.isVerified ? t('Verified') : t('Unverified')}
                  </Badge>
                  <Text
                    fontSize="sm"
                    color="gray.600"
                    fontFamily="mono"
                    textAlign="center"
                    wordBreak="break-all"
                  >
                    {address.formattedAddress}
                  </Text>
                  <Badge variant="outline">{address.network}</Badge>
                </VStack>
              </VStack>
            </Box>

            <Divider />

            {/* Address Details */}
            <Box bg="gray.50" p={4} borderRadius="md">
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">{t('Created')}</Text>
                  <Text fontSize="sm">
                    {new Date(address.createdAt).toLocaleDateString()}
                  </Text>
                </HStack>
                {address.lastUsed && (
                  <HStack justify="space-between">
                    <Text fontSize="sm" color="gray.600">{t('Last Used')}</Text>
                    <Text fontSize="sm">
                      {new Date(address.lastUsed).toLocaleDateString()}
                    </Text>
                  </HStack>
                )}
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">{t('Status')}</Text>
                  <HStack spacing={2}>
                    <Badge
                      colorScheme={address.isVerified ? 'green' : 'orange'}
                      variant="subtle"
                    >
                      {address.isVerified ? t('Verified') : t('Unverified')}
                    </Badge>
                    {address.isDefault && (
                      <Badge colorScheme="blue" variant="solid">
                        {t('Default')}
                      </Badge>
                    )}
                  </HStack>
                </HStack>
              </VStack>
            </Box>

            {/* Default Address Warning */}
            {address.isDefault && (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <VStack align="start" spacing={1} flex={1}>
                  <Text fontWeight="semibold">{t('Default Address')}</Text>
                  <Text fontSize="sm">
                    {t('This is your default address for')} {address.currency}. {t('If you delete it, another verified address will be set as default automatically.')}
                  </Text>
                </VStack>
              </Alert>
            )}

            {/* Security Notice */}
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={1} flex={1}>
                <Text fontWeight="semibold">{t('Security Notice')}</Text>
                <Text fontSize="sm">
                  {t('Deleting this address will not affect any pending or completed transactions. You can always add it back later if needed.')}
                </Text>
              </VStack>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              {t('Cancel')}
            </Button>
            <Button
              colorScheme="red"
              onClick={handleDelete}
              isLoading={loading}
              loadingText={t('Deleting...')}
              leftIcon={<FaTrash />}
            >
              {t('Delete Address')}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default DeleteWalletModal;
