#!/bin/bash

# Fix Mongo Express Configuration Script
# This script fixes and tests Mongo Express connectivity

set -e

echo "🔧 Fixing Mongo Express Configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Stop all services
print_status "Stopping all services..."
docker-compose -f docker-compose.mongo.yml down

# Step 2: Remove any problematic containers
print_status "Removing existing containers..."
docker rm -f cryptoyield-mongo-express 2>/dev/null || true
docker rm -f cryptoyield-mongodb 2>/dev/null || true
docker rm -f cryptoyield-redis 2>/dev/null || true

# Step 3: Pull latest images
print_status "Pulling latest images..."
docker-compose -f docker-compose.mongo.yml pull

# Step 4: Start MongoDB first
print_status "Starting MongoDB..."
docker-compose -f docker-compose.mongo.yml up -d mongodb

# Step 5: Wait for MongoDB to be healthy
print_status "Waiting for MongoDB to be healthy..."
timeout=180
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.mongo.yml ps mongodb | grep -q "healthy"; then
        print_success "MongoDB is healthy"
        break
    fi
    
    print_status "Waiting for MongoDB... ($elapsed/$timeout seconds)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

if [ $elapsed -ge $timeout ]; then
    print_error "MongoDB failed to become healthy within $timeout seconds"
    print_status "Checking MongoDB logs..."
    docker logs cryptoyield-mongodb --tail 20
    exit 1
fi

# Step 6: Test MongoDB connection
print_status "Testing MongoDB connection..."
docker exec cryptoyield-mongodb mongosh --quiet --eval "
try {
    db.adminCommand('ping');
    print('✅ MongoDB connection successful');
    
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set is active: ' + status.set);
    }
} catch (e) {
    print('❌ Error: ' + e.message);
    exit(1);
}
"

# Step 7: Start Redis
print_status "Starting Redis..."
docker-compose -f docker-compose.mongo.yml up -d redis

# Step 8: Start Mongo Express
print_status "Starting Mongo Express..."
docker-compose -f docker-compose.mongo.yml up -d mongo-express

# Step 9: Wait for Mongo Express to be ready
print_status "Waiting for Mongo Express to start..."
sleep 30

# Step 10: Check Mongo Express logs
print_status "Checking Mongo Express logs..."
docker logs cryptoyield-mongo-express --tail 20

# Step 11: Test Mongo Express connectivity
print_status "Testing Mongo Express connectivity..."

# Test if the container is running
if docker ps | grep -q "cryptoyield-mongo-express"; then
    print_success "Mongo Express container is running"
else
    print_error "Mongo Express container is not running"
    exit 1
fi

# Test if port 8081 is accessible
sleep 10
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 | grep -q "200\|401"; then
    print_success "Mongo Express is accessible at http://localhost:8081"
    print_status "Login credentials: admin / admin123"
else
    print_warning "Mongo Express may not be ready yet. Checking status..."
    
    # Check container health
    container_status=$(docker inspect cryptoyield-mongo-express --format='{{.State.Status}}')
    print_status "Container status: $container_status"
    
    # Show recent logs
    print_status "Recent Mongo Express logs:"
    docker logs cryptoyield-mongo-express --tail 10
fi

# Step 12: Display service status
print_status "Final service status:"
docker-compose -f docker-compose.mongo.yml ps

# Step 13: Display connection information
echo ""
print_success "🎉 Mongo Express setup completed!"
echo ""
echo "📋 Service Information:"
echo "  🗄️  MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo "  🌐 Mongo Express: http://localhost:8081"
echo ""
echo "🔑 Mongo Express Login:"
echo "  Username: admin"
echo "  Password: admin123"
echo ""
echo "🔗 MongoDB Connection Details:"
echo "  Host: localhost:27017"
echo "  Username: cryptoyield_admin"
echo "  Password: secure_password123"
echo "  Database: cryptoyield"
echo "  Auth Database: admin"
echo "  Replica Set: rs0"
echo ""

# Step 14: Test backend connectivity
print_status "Testing if backend can connect to MongoDB..."
if command -v node >/dev/null 2>&1; then
    cat > test-mongo-connection.js << 'EOF'
const { MongoClient } = require('mongodb');

const uri = '**********************************************************************************************************';

async function testConnection() {
    try {
        const client = new MongoClient(uri);
        await client.connect();
        console.log('✅ Backend can connect to MongoDB successfully');
        
        // Test transaction capability
        const session = client.startSession();
        await session.withTransaction(async () => {
            const db = client.db('cryptoyield');
            await db.collection('test').insertOne({ test: 'transaction', timestamp: new Date() }, { session });
        });
        console.log('✅ Transaction support is working');
        
        await client.close();
    } catch (error) {
        console.error('❌ Backend connection failed:', error.message);
    }
}

testConnection();
EOF

    if [ -f "backend/package.json" ]; then
        cd backend
        node ../test-mongo-connection.js
        cd ..
    else
        node test-mongo-connection.js
    fi
    
    rm -f test-mongo-connection.js
else
    print_warning "Node.js not found. Skipping backend connection test."
fi

echo ""
print_success "✅ Setup complete! You can now:"
echo "  1. Access Mongo Express at: http://localhost:8081"
echo "  2. Run your backend with: cd backend && npm run dev:docker"
echo "  3. View MongoDB data through the web interface"
echo ""
print_status "If Mongo Express is still not accessible, wait 1-2 minutes and try again."
