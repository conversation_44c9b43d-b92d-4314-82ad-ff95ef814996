const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB connected');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Define ReferralCommissionConfig schema
const referralCommissionConfigSchema = new mongoose.Schema({
  level: {
    type: Number,
    required: true,
    min: 1,
    unique: true,
  },
  commissionRate: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 5,
  },
  minInvestmentAmount: {
    type: Number,
    required: true,
    min: 0,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
});

const ReferralCommissionConfig = mongoose.model('ReferralCommissionConfig', referralCommissionConfigSchema);

// Default commission configurations
const defaultConfigs = [
  {
    level: 1,
    commissionRate: 3, // 3%
    minInvestmentAmount: 100,
    isActive: true
  },
  {
    level: 2,
    commissionRate: 5, // 5%
    minInvestmentAmount: 100,
    isActive: true
  },
  {
    level: 3,
    commissionRate: 7, // 7%
    minInvestmentAmount: 100,
    isActive: true
  }
];

// Fix commission system
const fixCommissionSystem = async () => {
  try {
    await connectDB();

    console.log('🔧 Fixing commission system...');

    // 1. Check existing configurations
    const existingConfigs = await ReferralCommissionConfig.find();
    console.log(`📊 Found ${existingConfigs.length} existing commission configurations`);

    if (existingConfigs.length === 0) {
      // 2. Create default configurations
      console.log('📝 Creating default commission configurations...');
      await ReferralCommissionConfig.insertMany(defaultConfigs);
      console.log('✅ Default commission configurations created');
    } else {
      console.log('ℹ️ Commission configurations already exist:');
      existingConfigs.forEach(config => {
        console.log(`   Level ${config.level}: ${config.commissionRate}% (min: $${config.minInvestmentAmount})`);
      });
    }

    // 3. Verify configurations
    const configs = await ReferralCommissionConfig.find().sort({ level: 1 });
    console.log(`\n✅ Commission system ready with ${configs.length} configurations:`);
    configs.forEach(config => {
      console.log(`   Level ${config.level}: ${config.commissionRate}% commission (min investment: $${config.minInvestmentAmount})`);
    });

    console.log('\n🎉 Commission system fixed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Test deposit approval with referral user');
    console.log('2. Check referrer wallet commissionBalance');
    console.log('3. Verify commission transaction records');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing commission system:', error);
    process.exit(1);
  }
};

// Run the fix
fixCommissionSystem();
