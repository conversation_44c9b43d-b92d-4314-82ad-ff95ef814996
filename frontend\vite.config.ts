import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import compression from 'vite-plugin-compression'
import autoprefixer from 'autoprefixer'
import cssnano from 'cssnano'

export default defineConfig(({ mode }) => {
  // Load env variables
  const env = loadEnv(mode, process.cwd(), '')

  // Check if we're analyzing the bundle
  const isAnalyze = mode === 'analyze'

  // Check if we're in production-no-optimize mode
  const isProductionNoOptimize = mode === 'production-no-optimize'

  return {
    plugins: [
      react({
        jsxRuntime: 'automatic',
      }),
      // Chỉ sử dụng compression khi không phải là production-no-optimize
      !isProductionNoOptimize && compression({ algorithm: 'gzip', ext: '.gz' }),
      !isProductionNoOptimize && compression({ algorithm: 'brotliCompress', ext: '.br' }),
      isAnalyze && visualizer({
        open: true,
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true,
      }),
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },

    server: {
      port: 3005,
      host: true,
      open: false,
      strictPort: false,
      hmr: {
        overlay: true,
        timeout: 5000,
        clientPort: 3003, // Port exposed to the host
        host: 'localhost', // Use 'localhost' for Docker
      },
      watch: {
        usePolling: true,
        interval: 1000,
      },
      proxy: {
        '/api': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '/api'),
          secure: false,
          configure: (proxy) => {
            proxy.on('error', (err) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (_proxyReq, req) => {
              console.log('Sending Request to the Target:', req.method, req.url);
            });
            proxy.on('proxyRes', (proxyRes, req) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
            });
          },
        },
        '/uploads': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          secure: false,
        },
        '/ws': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          ws: true, // Important: Allow WebSocket and Socket.IO proxy
          secure: false,
        },
        '/socket.io': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          ws: true, // Important: Allow Socket.IO proxy
          secure: false,
        },
      },
    },

    build: {
      // Luôn tạo sourcemap trong chế độ development hoặc production-no-optimize
      sourcemap: mode !== 'production' || isProductionNoOptimize,

      // Minify trong chế độ production
      minify: mode === 'production' && !isProductionNoOptimize ? 'terser' : false,

      // Cấu hình terser chỉ khi minify được bật
      terserOptions: mode === 'production' && !isProductionNoOptimize ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.debug', 'console.info'],
        },
        format: {
          comments: false,
        }
      } : undefined,

      // Tăng giới hạn cảnh báo kích thước chunk trong chế độ không tối ưu hóa
      chunkSizeWarningLimit: isProductionNoOptimize ? 5000 : 1200,

      // Không inline assets trong chế độ không tối ưu hóa
      assetsInlineLimit: isProductionNoOptimize ? 0 : 4096,

      // Không tách CSS thành các chunks trong chế độ không tối ưu hóa
      cssCodeSplit: !isProductionNoOptimize,

      // Báo cáo kích thước nén
      reportCompressedSize: isProductionNoOptimize,

      // Bao gồm tất cả các loại assets
      assetsInclude: ['**/*.jpg', '**/*.jpeg', '**/*.png', '**/*.gif', '**/*.svg', '**/*.webp', '**/*.avif'],

      // Cấu hình rollup options
      rollupOptions: {
        output: isProductionNoOptimize ? {
          // Tắt manualChunks để không chia nhỏ bundle trong chế độ không tối ưu hóa
          manualChunks: undefined,

          // Không nén các tên file trong chế độ không tối ưu hóa
          entryFileNames: 'assets/[name].js',
          chunkFileNames: 'assets/[name].js',
          assetFileNames: 'assets/[name].[ext]'
        } : {
          // Cấu hình mặc định cho chế độ production
          // manualChunks: (id) => {
          //   if (id.includes('node_modules/react') || id.includes('node_modules/react-dom')) {
          //     return 'react-vendor';
          //   }
          //   if (id.includes('node_modules/@chakra-ui')) {
          //     return 'chakra-ui';
          //   }
          //   if (id.includes('node_modules')) {
          //     return 'vendor';
          //   }
          // }
        }
      }
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@chakra-ui/react',
        'react-icons',
        'i18next',
        'react-i18next',
        'recharts',
        'ethers',
      ],
      esbuildOptions: {
        target: 'es2020',
      },
    },

    css: {
      devSourcemap: true,
      // CSS optimization
      postcss: {
        plugins: [
          mode === 'production' && autoprefixer(),
          mode === 'production' && cssnano({
            preset: ['default', {
              discardComments: { removeAll: true },
              normalizeWhitespace: true,
              minifyFontValues: true,
              colormin: true,
            }]
          })
        ].filter(Boolean)
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`,
        },
      },
    },
  }
})
