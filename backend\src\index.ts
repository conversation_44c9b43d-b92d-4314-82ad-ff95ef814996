import express, { Express, Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import dotenv from 'dotenv';
import morgan from 'morgan';
import helmet from 'helmet';
import path from 'path';
import fs from 'fs';
import cookieParser from 'cookie-parser';
import userRoutes from './routes/userRoutes';
import walletRoutes from './routes/walletRoutes';
import walletManagementRoutes from './routes/walletManagementRoutes';
import enhancedWithdrawalRoutes from './routes/enhancedWithdrawalRoutes';
import transactionRoutes from './routes/transactionRoutes';
import investmentRoutes from './routes/investmentRoutes';
import investmentPackageRoutes from './routes/investmentPackageRoutes';
import systemConfigRoutes from './routes/systemConfigRoutes';
import userSystemConfigRoutes from './routes/userSystemConfigRoutes';
import adminRoutes from './routes/adminRoutes';
import referralRoutes from './routes/referralRoutes';
import referralCommissionRoutes from './routes/referralCommissionRoutes';
import userReferralRoutes from './routes/userReferralRoutes';
import cryptoCurrencyRoutes from './routes/cryptoCurrencyRoutes';
import cryptoPriceRoutes from './routes/cryptoPriceRoutes';
import logsRoutes from './routes/logsRoutes';
import emailVerificationRoutes from './routes/emailVerificationRoutes';
import withdrawalRoutes from './routes/withdrawalRoutes';
import adminWithdrawalRoutes from './routes/adminWithdrawalRoutes';
// import simpleWithdrawRoutes from './routes/simpleWithdrawRoutes';
// import simpleCommissionRoutes from './routes/simpleCommissionRoutes';
// import analyticsRoutes from './routes/analyticsRoutes';
import { initializeMetrics } from './metrics';
import { logger } from './utils/logger';
import { applyMiddleware } from './middleware';
import http from 'http';
import { Server } from 'socket.io';
import compression from 'compression';
import { db } from './config/database';
import { initializeSocketService } from './services/socketService';
import { taskQueueService } from './services/taskQueueService';
import { metricsMiddleware } from './middleware/metricsMiddleware';
import { globalErrorHandler, setupUnhandledRejections } from './utils/errorHandler';

// Load environment variables
dotenv.config();

const app: Express = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5000;

// Initialize Socket.IO service
const socketService = initializeSocketService(server);

// Initialize notification service
import { notificationService } from './services/notificationService';
notificationService.initialize(socketService);

// Initialize cron service for investment calculations
import cronService from './services/cronService';
import timeService from './services/timeService';

// Initialize crypto data collection job
import { initializeCryptoDataJob, stopCryptoDataJob } from './jobs/cryptoDataCollectionJob';

// Export Socket.IO service instance for use in other modules
export { socketService };

// Initialize task queues
const initializeTaskQueues = async () => {
  // Skip task queue initialization for development and testing
  if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development') {
    logger.info(`Task queues initialization skipped for ${process.env.NODE_ENV} mode`);
    return;
  }

  try {
    // Production queue initialization would go here
    logger.info('Task queues initialization for production not implemented');
  } catch (error) {
    logger.error('Failed to initialize task queues:', error);
    // Don't crash the server in development mode
    if (process.env.NODE_ENV !== 'development') {
      throw error;
    }
  }
};
console.log("Environment:", process.env.NODE_ENV);

// JSON parsing middleware with proper error handling
app.use(express.json({
  limit: '10mb',
  verify: (req: any, res: any, buf: Buffer, encoding: string) => {
    try {
      // Store raw body for verification if needed
      req.rawBody = buf;
    } catch (e) {
      logger.error('Error storing raw body:', e);
    }
  }
}));

// URL encoded middleware
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Cookie parser middleware
app.use(cookieParser());

// Apply other middleware
applyMiddleware(app);
app.use(morgan('dev'));
app.use(metricsMiddleware);



// Serve static files from uploads directory
const uploadsPath = path.join(__dirname, '../uploads');
logger.info(`Serving static files from: ${uploadsPath}`);

// Create uploads directory if it doesn't exist
if (!fs.existsSync(uploadsPath)) {
  logger.info(`Creating uploads directory: ${uploadsPath}`);
  fs.mkdirSync(uploadsPath, { recursive: true });
}

// Create receipts subdirectory if it doesn't exist
const receiptsPath = path.join(uploadsPath, 'receipts');
if (!fs.existsSync(receiptsPath)) {
  logger.info(`Creating receipts directory: ${receiptsPath}`);
  fs.mkdirSync(receiptsPath, { recursive: true });
}

// Configure static middleware to serve files from uploads directory
// This makes the uploads directory publicly accessible
logger.info(`Serving static files from: ${uploadsPath}`);
app.use('/uploads', express.static(uploadsPath, {
  // Set cache control headers
  maxAge: process.env.NODE_ENV === 'development' ? 0 : '1d',
  // Allow cross-origin access with Safari-specific headers
  setHeaders: (res, path, stat) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Length, Content-Range, Accept-Ranges');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Vary', 'Origin');
  }
}));

// Initialize monitoring
initializeMetrics(app);

// Routes with /api prefix
app.use('/api/users', userRoutes);
app.use('/api/wallets', walletRoutes);
app.use('/api/wallet-management', walletManagementRoutes);
app.use('/api/enhanced-withdrawals', enhancedWithdrawalRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/investments', investmentRoutes);
app.use('/api/investment-packages', investmentPackageRoutes);
app.use('/api/system', systemConfigRoutes);
app.use('/api/user-system', userSystemConfigRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/referrals', referralRoutes);
app.use('/api/referral-commissions', referralCommissionRoutes);
app.use('/api/user/referrals', userReferralRoutes);
app.use('/api/crypto-currencies', cryptoCurrencyRoutes);
app.use('/api/crypto', cryptoPriceRoutes);
app.use('/api/logs', logsRoutes);
app.use('/api/email-verification', emailVerificationRoutes);
app.use('/api/withdrawals', withdrawalRoutes);
app.use('/api/admin/withdrawals', adminWithdrawalRoutes);
// app.use('/api/wallets', simpleWithdrawRoutes);
// app.use('/api/commissions', simpleCommissionRoutes);
// app.use('/api/analytics', analyticsRoutes);

// Routes without /api prefix (for backward compatibility)
app.use('/users', userRoutes);
app.use('/wallets', walletRoutes);
app.use('/transactions', transactionRoutes);
app.use('/investments', investmentRoutes);
app.use('/system', systemConfigRoutes);
app.use('/admin', adminRoutes);
app.use('/commissions', referralCommissionRoutes);
app.use('/crypto-currencies', cryptoCurrencyRoutes);

// Health check route
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    message: 'Server is running',
    version: process.env.npm_package_version || '1.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: db.isConnectedToDatabase() ? 'connected' : 'disconnected'
  });
});

// Apply error handler middleware (must be after routes)
import { errorHandlerMiddleware } from './middleware';
errorHandlerMiddleware(app);

// MongoDB Connection with retry logic
const connectDB = async (retries = 5) => {
  try {
    if (process.env.NODE_ENV === 'test') {
      // Skip MongoDB connection for testing
      logger.info('MongoDB connection skipped for testing');
      return;
    }

    // Use the database singleton
    await db.connect();
    logger.info('MongoDB Connected successfully');
  } catch (error) {
    logger.error(`MongoDB connection error: ${error}`);
    if (retries > 0) {
      logger.warn(`Retrying connection... (${retries} attempts remaining)`);
      await new Promise(resolve => setTimeout(resolve, 5000));
      return connectDB(retries - 1);
    } else {
      logger.error('Failed to connect to MongoDB after multiple retries');
      if (process.env.NODE_ENV !== 'development') {
        process.exit(1);
      }
      throw error; // Re-throw error in development mode
    }
  }
};

// Setup unhandled rejection and exception handlers
setupUnhandledRejections();

// Initialize application
const startServer = async () => {
  try {
    // Start server first to ensure it's available
    server.listen(PORT, () => {
      logger.info(`⚡️[server]: Server is running at http://localhost:${PORT}`);
      logger.info('🚀 Server started successfully - logout endpoint available at POST /api/users/logout');
    });

    // Try to connect to database (non-blocking)
    try {
      await connectDB();
      logger.info('Connected to database');
    } catch (error) {
      logger.warn('Database connection failed, continuing with mock data only:', error);
    }

    // Try to initialize task queues (non-blocking)
    try {
      await initializeTaskQueues();
      logger.info('Task queues initialized');
    } catch (error) {
      logger.warn('Task queue initialization failed:', error);
    }

    // Initialize cron service for investment calculations (non-blocking)
    try {
      cronService.initialize();
      logger.info('✅ Cron service initialized for investment calculations');
    } catch (error) {
      logger.warn('⚠️ Cron service initialization failed:', error);
    }

    // Initialize crypto data collection job (non-blocking)
    try {
      initializeCryptoDataJob();
      logger.info('✅ Crypto data collection job initialized');
    } catch (error) {
      logger.warn('⚠️ Crypto data collection job initialization failed:', error);
    }

    // Graceful shutdown
    const shutdown = async () => {
      logger.info('Shutting down server...');

      // Close HTTP server
      server.close(() => {
        logger.info('HTTP server closed');
      });

      // Close Socket.IO connections
      socketService.shutdown();
      logger.info('Socket.IO service shut down');

      // Stop cron jobs
      try {
        cronService.stopAll();
        await timeService.cleanup();
        stopCryptoDataJob();
        logger.info('✅ Cron service, time service, and crypto data job shut down');
      } catch (error) {
        logger.error('❌ Error shutting down cron/time services:', error);
      }

      if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development') {
        // Skip task queue and database shutdown for testing and development
        logger.info(`Task queues and database shutdown skipped for ${process.env.NODE_ENV} mode`);
      } else {
        // Shutdown task queues
        try {
          await taskQueueService.shutdown();
          logger.info('Task queues shut down successfully');
        } catch (error) {
          logger.error('Error shutting down task queues:', error);
        }

        // Close database connection
        try {
          await db.closeConnection();
          logger.info('Database connection closed successfully');
        } catch (error) {
          logger.error('Error closing database connection:', error);
        }
      }

      process.exit(0);
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
