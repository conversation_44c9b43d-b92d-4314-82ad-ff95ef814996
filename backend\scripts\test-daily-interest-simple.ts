#!/usr/bin/env npx ts-node

/**
 * Simple Daily Interest Payment Test Script
 * 
 * A simplified version that focuses on testing the core functionality
 * without complex TypeScript configurations.
 * 
 * Usage: npx ts-node scripts/test-daily-interest-simple.ts
 */

// Load environment variables first
require('dotenv').config({ path: require('path').join(__dirname, '../.env.docker') });

import mongoose from 'mongoose';

// Import models (ensure they are registered)
require('../src/models/userModel');
require('../src/models/walletModel');
require('../src/models/investmentPackageModel');
require('../src/models/transactionModel');
require('../src/models/paymentHistoryModel');
require('../src/models/auditTrailModel');

// Import services
const interestCalculationService = require('../src/services/interestCalculationService').default;

// Models
const User = require('../src/models/userModel').default;
const Wallet = require('../src/models/walletModel').default;
const InvestmentPackage = require('../src/models/investmentPackageModel').default;
const Transaction = require('../src/models/transactionModel').default;

/**
 * Connect to database
 */
async function connectDatabase(): Promise<void> {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log('🔌 Connecting to database...');
    console.log(`   URI: ${mongoUri}`);
    
    await mongoose.connect(mongoUri);
    console.log('✅ Database connection established');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

/**
 * Check existing active packages
 */
async function checkActivePackages(): Promise<any[]> {
  try {
    const packages = await InvestmentPackage.find({
      status: 'active',
      activatedAt: { $ne: null }
    });
    
    console.log(`📊 Found ${packages.length} active investment packages`);
    
    if (packages.length > 0) {
      console.log('\n📋 Active packages:');
      packages.forEach((pkg: any, index: number) => {
        console.log(`  ${index + 1}. ${pkg.packageId}: ${pkg.amount} ${pkg.currency}`);
      });
    }
    
    return packages;
  } catch (error) {
    console.error('❌ Error checking packages:', error);
    throw error;
  }
}

/**
 * Create a simple test package if none exist
 */
async function createTestPackage(): Promise<void> {
  try {
    console.log('\n🔧 Creating test data...');
    
    // Create test user
    const testUser = await User.create({
      email: `test-${Date.now()}@example.com`,
      password: 'Test123!@#',
      firstName: 'Test',
      lastName: 'User',
      emailVerified: true,
      kycVerified: true,
      referralCode: Math.random().toString(36).substring(7).toUpperCase()
    });
    
    console.log(`✅ Created test user: ${testUser.email}`);
    
    // Create test wallet
    const testWallet = await Wallet.create({
      userId: testUser._id,
      assets: [{
        symbol: 'USDT',
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'interest'
      }],
      totalCommissionEarned: 0,
      totalInterestEarned: 0
    });
    
    console.log(`✅ Created test wallet: ${testWallet._id}`);
    
    // Create test transaction
    const testTransaction = await Transaction.create({
      userId: testUser._id,
      walletId: testWallet._id,
      type: 'deposit',
      asset: 'USDT',
      amount: 1000,
      status: 'completed',
      description: 'Test deposit for interest calculation'
    });
    
    console.log(`✅ Created test transaction: ${testTransaction._id}`);
    
    // Create test investment package
    const activatedAt = new Date(Date.now() - 24 * 60 * 60 * 1000); // 1 day ago
    
    const testPackage = await InvestmentPackage.create({
      userId: testUser._id,
      transactionId: testTransaction._id,
      packageId: `PKG-${Date.now()}`,
      amount: 1000,
      currency: 'USDT',
      status: 'active',
      createdAt: activatedAt,
      activatedAt: activatedAt,
      dailyInterest: 0,
      totalEarned: 0,
      accumulatedInterest: 0,
      interestRate: 0.01, // 1% daily
      lastCalculatedAt: null,
      lastInterestDistribution: null,
      compoundEnabled: false,
      emergencyWithdrawFee: 0.05,
      packageHash: require('crypto').randomBytes(32).toString('hex'),
      activeDays: 0,
      totalDays: 365,
      roi: 0,
      autoCreated: false,
      minimumWithdrawalUSDT: 50,
      realTimeUSDTValue: 0,
      withdrawableInterest: 0,
      principalLocked: true
    });
    
    console.log(`✅ Created test investment package: ${testPackage.packageId}`);
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
    throw error;
  }
}

/**
 * Test daily interest calculation
 */
async function testInterestCalculation(): Promise<void> {
  try {
    console.log('\n🚀 Starting daily interest calculation test...');
    
    const startTime = Date.now();
    
    // Run the interest calculation
    const summary = await interestCalculationService.processAllActivePackages();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Display results
    console.log('\n📊 DAILY INTEREST CALCULATION RESULTS');
    console.log('=====================================');
    console.log(`📦 Total packages processed: ${summary.totalPackages}`);
    console.log(`✅ Successful calculations: ${summary.successfulCalculations}`);
    console.log(`❌ Failed calculations: ${summary.failedCalculations}`);
    console.log(`💰 Total interest paid: ${summary.totalInterestPaid.toFixed(6)}`);
    console.log(`⏱️  Processing duration: ${duration}ms`);
    console.log(`🕐 Timestamp: ${summary.timestamp.toISOString()}`);
    
    if (summary.totalPackages > 0) {
      const successRate = (summary.successfulCalculations / summary.totalPackages * 100).toFixed(2);
      console.log(`📈 Success rate: ${successRate}%`);
    }
    
    if (summary.errors && summary.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      summary.errors.forEach((error: any, index: number) => {
        console.log(`  ${index + 1}. Package ${error.packageId}: ${error.error}`);
      });
    }
    
    console.log('\n✅ Interest calculation test completed!');
    
  } catch (error) {
    console.error('\n❌ Interest calculation test failed:', error);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  console.log('🧪 DAILY INTEREST PAYMENT TEST (SIMPLE VERSION)');
  console.log('===============================================');
  
  try {
    // Connect to database
    await connectDatabase();
    
    // Check existing packages
    const activePackages = await checkActivePackages();
    
    // Create test data if no active packages exist
    if (activePackages.length === 0) {
      console.log('\n⚠️  No active packages found. Creating test data...');
      await createTestPackage();
    }
    
    // Run interest calculation test
    await testInterestCalculation();
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await mongoose.connection.close();
      console.log('✅ Database connection closed');
    } catch (error) {
      console.error('❌ Error closing database:', error);
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Received SIGINT. Shutting down...');
  try {
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Execute main function
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
