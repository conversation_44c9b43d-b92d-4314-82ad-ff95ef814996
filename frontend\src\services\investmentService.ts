import { apiClient } from '../utils/apiClient';

/**
 * API service for investment-related endpoints
 */
export const investmentService = {
  /**
   * Create a new investment
   * @param data Investment data
   */
  createInvestment: async (data: {
    currency: string;
    amount: number;
    description?: string;
    network?: string;
  }) => {
    try {
      // Reset circuit breaker before making the request
      apiClient.resetCircuitBreaker();
      console.log('Creating investment with data:', data);

      const response = await apiClient.post('/investments', data);
      console.log('Investment creation response:', response);
      return response;
    } catch (error: any) {
      console.error('Investment creation failed:', error);

      // Check if it's a circuit breaker error
      if (error.message && error.message.includes('Circuit breaker is OPEN')) {
        console.log('Circuit breaker is open, resetting...');
        apiClient.resetCircuitBreaker();

        // Retry once after reset
        try {
          const retryResponse = await apiClient.post('/investments', data);
          console.log('Investment creation retry successful:', retryResponse);
          return retryResponse;
        } catch (retryError) {
          console.error('Investment creation retry failed:', retryError);
          throw retryError;
        }
      }

      throw error;
    }
  },

  /**
   * Upload receipt for an investment
   * @param id Investment ID
   * @param formData FormData containing the receipt file
   */
  uploadReceipt: (id: string, formData: FormData) =>
    apiClient.post(`/investments/${id}/receipt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),

  /**
   * Get all investments for the current user
   * @param params Optional query parameters
   */
  getInvestments: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    currency?: string;
    grouped?: boolean;
  }) => apiClient.get('/investments', { params }),

  /**
   * Get grouped investments for the current user
   * This is a convenience method that sets grouped=true
   */
  getGroupedInvestments: (params?: {
    status?: string;
    currency?: string;
  }) => apiClient.get('/investments', { params: { ...params, grouped: true } }),

  /**
   * Get investment by ID
   * @param id Investment ID
   */
  getInvestmentById: (id: string) => apiClient.get(`/investments/${id}`),

  /**
   * Update transaction hash for an investment
   * @param id Investment ID
   * @param txHash Transaction hash
   */
  updateTransactionHash: (id: string, txHash: string) =>
    apiClient.put(`/investments/${id}/txhash`, { txHash }),

  /**
   * Get deposit address for a currency
   * @param currency Currency symbol (e.g., BTC, ETH)
   * @param network Optional network ID
   */
  getDepositAddress: (() => {
    // Cache for deposit addresses
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

    // Function to clear cache for a specific currency
    const clearCacheForCurrency = (currency: string) => {
      const keysToDelete = Object.keys(cache).filter(key => key.startsWith(`${currency.toUpperCase()}:`));
      keysToDelete.forEach(key => delete cache[key]);
      console.log(`Cleared deposit address cache for ${currency}:`, keysToDelete);
    };

    // Set up WebSocket listener for cache invalidation
    const setupCacheInvalidation = async () => {
      try {
        const { SocketService } = await import('../utils/socketService');
        const socketService = SocketService.getInstance();

        // Subscribe to crypto address updates to invalidate cache
        socketService.subscribe('crypto_address_updated', (data: any) => {
          if (data.payload && data.payload.currency) {
            console.log('Invalidating deposit address cache for:', data.payload.currency);
            clearCacheForCurrency(data.payload.currency);
          }
        });
      } catch (error) {
        console.warn('Could not set up cache invalidation for deposit addresses:', error);
      }
    };

    // Initialize cache invalidation (only once)
    let cacheInvalidationSetup = false;

    return (currency: string, network?: string) => {
      const cacheKey = `${currency.toUpperCase()}:${network || 'default'}`;
      const now = Date.now();

      // Set up cache invalidation on first call
      if (!cacheInvalidationSetup) {
        setupCacheInvalidation();
        cacheInvalidationSetup = true;
      }

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached deposit address for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Make the API call to system crypto addresses endpoint
      return apiClient.get('/system/crypto-addresses').then(response => {
        console.log('System crypto addresses response:', response);

        // Find the specific currency data
        if (response.data && response.data.cryptoAddresses) {
          const currencyData = response.data.cryptoAddresses.find(
            (crypto: any) => crypto.currency === currency.toUpperCase()
          );

          if (currencyData && currencyData.addresses && currencyData.addresses.length > 0) {
            // Get the current address based on currentIndex
            const currentIndex = currencyData.currentIndex || 0;
            let selectedAddress = '';

            // Handle both string addresses and address objects with network
            const addressAtIndex = currencyData.addresses[currentIndex];
            if (typeof addressAtIndex === 'string') {
              selectedAddress = addressAtIndex;
            } else if (addressAtIndex && addressAtIndex.address) {
              // If network is specified, try to find matching network
              if (network && addressAtIndex.network === network) {
                selectedAddress = addressAtIndex.address;
              } else if (!network) {
                selectedAddress = addressAtIndex.address;
              } else {
                // Find address with matching network
                const networkMatch = currencyData.addresses.find(
                  (addr: any) => addr.network === network
                );
                selectedAddress = networkMatch ? networkMatch.address : addressAtIndex.address;
              }
            }

            const transformedResponse = {
              data: {
                address: selectedAddress,
                currency: currency.toUpperCase(),
                network: network || 'mainnet',
                enabled: currencyData.enabled
              }
            };

            // Cache the response with proper key
            cache[cacheKey] = {
              data: transformedResponse,
              timestamp: now
            };

            console.log('Transformed deposit address response:', transformedResponse);
            console.log('Cached with key:', cacheKey);
            return transformedResponse;
          }
        }

        // If no address found, throw error
        throw new Error(`No address found for currency ${currency}`);

      }).catch(error => {
        console.error('Error fetching deposit address:', error);

        // Check if it's a circuit breaker error
        if (error.message && error.message.includes('Circuit breaker is OPEN')) {
          console.log('Circuit breaker is open for deposit address, resetting...');
          apiClient.resetCircuitBreaker();

          // Retry once after reset
          return apiClient.get('/system/crypto-addresses').then(response => {
            // Same transformation logic as above
            if (response.data && response.data.cryptoAddresses) {
              const currencyData = response.data.cryptoAddresses.find(
                (crypto: any) => crypto.currency === currency.toUpperCase()
              );

              if (currencyData && currencyData.addresses && currencyData.addresses.length > 0) {
                const currentIndex = currencyData.currentIndex || 0;
                let selectedAddress = '';

                const addressAtIndex = currencyData.addresses[currentIndex];
                if (typeof addressAtIndex === 'string') {
                  selectedAddress = addressAtIndex;
                } else if (addressAtIndex && addressAtIndex.address) {
                  selectedAddress = addressAtIndex.address;
                }

                const transformedResponse = {
                  data: {
                    address: selectedAddress,
                    currency: currency.toUpperCase(),
                    network: network || 'mainnet',
                    enabled: currencyData.enabled
                  }
                };

                cache[cacheKey] = {
                  data: transformedResponse,
                  timestamp: now
                };

                console.log('Retry: Transformed deposit address response:', transformedResponse);
                console.log('Retry: Cached with key:', cacheKey);
                return transformedResponse;
              }
            }

            throw new Error(`No address found for currency ${currency} after retry`);
          });
        }

        throw error;
      });
    };
  })(),

  /**
   * Get available wallet addresses for all currencies or a specific currency with network information
   * @param currency Optional currency symbol (e.g., BTC, ETH)
   */
  getAvailableWallets: (() => {
    // Cache for wallet data
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 60 * 1000; // 1 minute cache

    return (currency?: string) => {
      const cacheKey = currency || 'all';
      const now = Date.now();

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached wallet data for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Otherwise make the API call
      return apiClient.get('/wallets/available', {
        params: currency ? { currency } : undefined
      }).then(response => {
        // Cache the response
        cache[cacheKey] = {
          data: response,
          timestamp: now
        };
        return response;
      });
    };
  })(),
};

export default investmentService;
