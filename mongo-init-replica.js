// MongoDB Replica Set Initialization Script
// This script initializes a single-node replica set to enable transactions
// with keyFile authentication for Docker environment

print('🚀 Starting MongoDB replica set initialization with keyFile authentication...');

// Switch to admin database for authentication
db = db.getSiblingDB('admin');

// Wait for MongoDB to be ready
var attempts = 0;
var maxAttempts = 30;

print('⏳ Waiting for MongoDB to be ready...');
while (attempts < maxAttempts) {
  try {
    // Try to connect and check if we can run commands
    var status = db.adminCommand({ ping: 1 });
    if (status.ok === 1) {
      print('✅ MongoDB is ready, proceeding with replica set initialization...');
      break;
    }
  } catch (e) {
    print('⏳ Waiting for MongoDB to be ready... Attempt ' + (attempts + 1) + '/' + maxAttempts + ' - ' + e.message);
    sleep(3000); // Wait 3 seconds
    attempts++;
  }
}

if (attempts >= maxAttempts) {
  print('❌ ERROR: MongoDB did not become ready within the expected time');
  quit(1);
}

try {
  // Check if replica set is already initialized
  var rsStatus;
  try {
    rsStatus = rs.status();
    print('📊 Replica set already initialized with status: ' + rsStatus.ok);

    if (rsStatus.ok === 1) {
      print('✅ Replica set is already running and healthy');
      print('🎯 Set name: ' + rsStatus.set);
      print('🏆 Primary member: ' + rsStatus.members.find(m => m.stateStr === 'PRIMARY').name);
      print('🔥 Transactions are supported!');
      quit(0);
    }
  } catch (e) {
    print('🔧 Replica set not yet initialized, proceeding with initialization...');
    print('📝 Error details: ' + e.message);
  }

  // Initialize replica set with proper hostname for Docker
  var config = {
    _id: 'rs0',
    members: [
      {
        _id: 0,
        host: 'mongodb:27017',
        priority: 1,
        votes: 1,
        arbiterOnly: false,
        buildIndexes: true,
        hidden: false,
        secondaryDelaySecs: 0,
        tags: {}
      }
    ],
    settings: {
      electionTimeoutMillis: 2000,
      heartbeatTimeoutSecs: 2,
      heartbeatIntervalMillis: 2000,
      replicationOplogSizeMB: 1024,
      getLastErrorModes: {},
      getLastErrorDefaults: {
        w: 1,
        wtimeout: 0
      }
    }
  };

  print('🔧 Initializing replica set with config:');
  printjson(config);

  var result = rs.initiate(config);
  print('📋 Replica set initiation result:');
  printjson(result);

  if (result.ok === 1) {
    print('✅ Replica set initialized successfully!');

    // Wait for the replica set to become primary
    var waitAttempts = 0;
    var maxWaitAttempts = 30;

    print('⏳ Waiting for replica set member to become PRIMARY...');
    while (waitAttempts < maxWaitAttempts) {
      try {
        var status = rs.status();
        if (status.members && status.members[0] && status.members[0].stateStr === 'PRIMARY') {
          print('🏆 Replica set member is now PRIMARY');
          print('🎯 Set name: ' + status.set);
          print('📊 Member state: ' + status.members[0].stateStr);
          print('🌐 Member host: ' + status.members[0].name);
          break;
        }
        print('⏳ Waiting for PRIMARY status... Attempt ' + (waitAttempts + 1) + '/' + maxWaitAttempts + ' (Current state: ' + (status.members && status.members[0] ? status.members[0].stateStr : 'unknown') + ')');
        sleep(3000);
        waitAttempts++;
      } catch (e) {
        print('⚠️ Error checking replica set status: ' + e.message);
        sleep(3000);
        waitAttempts++;
      }
    }

    if (waitAttempts >= maxWaitAttempts) {
      print('⚠️ WARNING: Replica set member did not become PRIMARY within expected time');
      print('🔍 This might be normal for initial setup. Check logs and try again if needed.');
    } else {
      print('🎉 Replica set is ready and transactions are now supported!');

      // Test transaction capability
      try {
        var session = db.getMongo().startSession();
        session.startTransaction();
        print('✅ Transaction test successful!');
        session.abortTransaction();
        session.endSession();
      } catch (e) {
        print('⚠️ Transaction test failed: ' + e.message);
      }
    }

  } else {
    print('❌ ERROR: Failed to initialize replica set');
    printjson(result);
    quit(1);
  }

} catch (e) {
  print('❌ ERROR during replica set initialization: ' + e.message);
  print('🔍 Stack trace: ' + e.stack);
  quit(1);
}

print('🎉 MongoDB replica set initialization completed successfully!');
print('🔥 Transactions are now enabled for your application!');
print('🌐 You can connect using: ********************************************************************************************************');
