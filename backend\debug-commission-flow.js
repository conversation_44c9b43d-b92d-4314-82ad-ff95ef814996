const mongoose = require('mongoose');

// Connect to database
mongoose.connect('mongodb://localhost:27017/cryptoyield')
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

const debugCommissionFlow = async () => {
  try {
    console.log('🔍 DEBUGGING COMMISSION FLOW...\n');

    // 1. Find users with referrers
    const usersWithReferrers = await mongoose.connection.db.collection('users').find({
      referrerId: { $exists: true, $ne: null }
    }).toArray();

    console.log(`👥 Users with referrers: ${usersWithReferrers.length}`);
    
    if (usersWithReferrers.length === 0) {
      console.log('❌ NO USERS WITH REFERRERS FOUND!');
      console.log('   This is why 3% commission is not working.');
      console.log('   Need to create users with referral relationships.\n');
      
      // Show sample users
      const sampleUsers = await mongoose.connection.db.collection('users').find({}).limit(5).toArray();
      console.log('📋 Sample users in database:');
      sampleUsers.forEach((user, i) => {
        console.log(`   ${i+1}. ${user.firstName} ${user.lastName} (${user.email})`);
        console.log(`      ID: ${user._id}`);
        console.log(`      ReferrerID: ${user.referrerId || 'NONE'}`);
      });
      
      process.exit(0);
    }

    // 2. Check each user with referrer
    for (const user of usersWithReferrers) {
      console.log(`\n👤 User: ${user.firstName} ${user.lastName} (${user.email})`);
      console.log(`   User ID: ${user._id}`);
      console.log(`   Referrer ID: ${user.referrerId}`);

      // Check if referrer exists
      const referrer = await mongoose.connection.db.collection('users').findOne({
        _id: user.referrerId
      });

      if (!referrer) {
        console.log(`   ❌ Referrer NOT FOUND! Invalid referrerId`);
        continue;
      }

      console.log(`   ✅ Referrer: ${referrer.firstName} ${referrer.lastName}`);

      // Check user's deposits
      const deposits = await mongoose.connection.db.collection('transactions').find({
        userId: user._id,
        type: 'deposit',
        status: 'approved'
      }).sort({ createdAt: -1 }).toArray();

      console.log(`   📊 User's approved deposits: ${deposits.length}`);
      
      if (deposits.length > 0) {
        deposits.forEach((deposit, i) => {
          console.log(`      Deposit ${i+1}: ${deposit.amount} ${deposit.asset} (${deposit.createdAt})`);
        });

        // Check if commission exists for this user
        const existingCommission = await mongoose.connection.db.collection('referralcommissions').findOne({
          referrerId: user.referrerId,
          referredId: user._id
        });

        if (existingCommission) {
          console.log(`   ✅ Commission EXISTS:`);
          console.log(`      Amount: ${existingCommission.amount} ${existingCommission.currency}`);
          console.log(`      Status: ${existingCommission.status}`);
          console.log(`      Created: ${existingCommission.createdAt}`);

          // Check referrer's wallet
          const referrerWallet = await mongoose.connection.db.collection('wallets').findOne({
            userId: user.referrerId
          });

          if (referrerWallet) {
            console.log(`   💰 Referrer's wallet:`);
            console.log(`      Total commission earned: ${referrerWallet.totalCommissionEarned || 0}`);
            
            const commissionAssets = referrerWallet.assets.filter(a => a.commissionBalance > 0);
            if (commissionAssets.length > 0) {
              console.log(`      Commission assets:`);
              commissionAssets.forEach(asset => {
                console.log(`        ${asset.symbol}: ${asset.commissionBalance} (mode: ${asset.mode})`);
              });
            } else {
              console.log(`      ❌ NO commission balance in wallet assets!`);
            }
          } else {
            console.log(`   ❌ Referrer's wallet NOT FOUND!`);
          }
        } else {
          console.log(`   ❌ NO commission record found - should have been created!`);
        }
      } else {
        console.log(`   ⚠️  No approved deposits - commission will be created when first deposit is approved`);
      }
    }

    // 3. Check all commission transactions
    console.log(`\n💳 COMMISSION TRANSACTIONS:`);
    const commissionTransactions = await mongoose.connection.db.collection('transactions').find({
      type: { $in: ['commission', 'referral_commission'] }
    }).sort({ createdAt: -1 }).toArray();

    console.log(`   Total commission transactions: ${commissionTransactions.length}`);
    
    if (commissionTransactions.length > 0) {
      commissionTransactions.forEach((tx, i) => {
        console.log(`   ${i+1}. Type: ${tx.type}`);
        console.log(`      Amount: ${tx.amount} ${tx.asset}`);
        console.log(`      User: ${tx.userId}`);
        console.log(`      Status: ${tx.status}`);
        console.log(`      Created: ${tx.createdAt}`);
        console.log(`      Description: ${tx.description}`);
      });
    }

    // 4. Check all referral commission records
    console.log(`\n📋 REFERRAL COMMISSION RECORDS:`);
    const referralCommissions = await mongoose.connection.db.collection('referralcommissions').find({}).toArray();
    
    console.log(`   Total referral commissions: ${referralCommissions.length}`);
    
    if (referralCommissions.length > 0) {
      referralCommissions.forEach((comm, i) => {
        console.log(`   ${i+1}. Referrer: ${comm.referrerId}`);
        console.log(`      Referred: ${comm.referredId}`);
        console.log(`      Amount: ${comm.amount} ${comm.currency}`);
        console.log(`      Status: ${comm.status}`);
        console.log(`      Created: ${comm.createdAt}`);
      });
    }

    // 5. Summary
    console.log(`\n📊 SUMMARY:`);
    console.log(`   Users with referrers: ${usersWithReferrers.length}`);
    console.log(`   Commission transactions: ${commissionTransactions.length}`);
    console.log(`   Referral commission records: ${referralCommissions.length}`);
    
    if (usersWithReferrers.length > 0 && referralCommissions.length === 0) {
      console.log(`\n🚨 ISSUE: Users have referrers but no commissions created!`);
      console.log(`   Check server logs during deposit approval for errors.`);
    } else if (referralCommissions.length > 0) {
      console.log(`\n✅ Commission system is working!`);
      console.log(`   Check referrer wallets for commission balances.`);
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

// Run after connection
setTimeout(debugCommissionFlow, 1000);
