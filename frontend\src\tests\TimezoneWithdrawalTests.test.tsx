import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../i18n';
import ThreeStepWithdrawModal from '../components/modals/ThreeStepWithdrawModal';
import { enhancedWithdrawalService } from '../services/enhancedWithdrawalService';

// Mock Date to control time for testing
const mockDate = (dateString: string) => {
  const mockDateObj = new Date(dateString);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDateObj as any);
  global.Date.now = jest.fn(() => mockDateObj.getTime());
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ChakraProvider>
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  </ChakraProvider>
);

describe('Enhanced Withdrawal System - Critical Bug Fixes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('Time Restriction Countdown Logic - Bug Fix Verification', () => {
    it('should correctly calculate UTC+3 time and show accurate countdown', () => {
      // Test case 1: 01:30:45 UTC+3 (should show 01:29:15 remaining until 03:00)
      mockDate('2024-01-15T22:30:45.000Z'); // This is 01:30:45 UTC+3
      
      const restriction = enhancedWithdrawalService.checkTimeBasedRestrictions();
      
      expect(restriction.canWithdraw).toBe(false);
      expect(restriction.timeRemaining).toBe('01:29:15');
      expect(restriction.message).toContain('03:00 UTC+3');
    });

    it('should allow withdrawals after 03:00 UTC+3', () => {
      // Test case 2: 03:00:00 UTC+3 exactly (should allow withdrawal)
      mockDate('2024-01-16T00:00:00.000Z'); // This is 03:00:00 UTC+3
      
      const restriction = enhancedWithdrawalService.checkTimeBasedRestrictions();
      
      expect(restriction.canWithdraw).toBe(true);
      expect(restriction.timeRemaining).toBe('');
      expect(restriction.nextEligibleTime).toBe(null);
    });

    it('should allow withdrawals throughout the day after 03:00 UTC+3', () => {
      // Test case 3: 15:45:30 UTC+3 (should allow withdrawal)
      mockDate('2024-01-16T12:45:30.000Z'); // This is 15:45:30 UTC+3
      
      const restriction = enhancedWithdrawalService.checkTimeBasedRestrictions();
      
      expect(restriction.canWithdraw).toBe(true);
      expect(restriction.timeRemaining).toBe('');
    });

    it('should handle edge case at 02:59:59 UTC+3', () => {
      // Test case 4: 02:59:59 UTC+3 (should show 00:00:01 remaining)
      mockDate('2024-01-15T23:59:59.000Z'); // This is 02:59:59 UTC+3
      
      const restriction = enhancedWithdrawalService.checkTimeBasedRestrictions();
      
      expect(restriction.canWithdraw).toBe(false);
      expect(restriction.timeRemaining).toBe('00:00:01');
    });

    it('should handle midnight crossover correctly', () => {
      // Test case 5: 00:30:00 UTC+3 (should show 02:30:00 remaining)
      mockDate('2024-01-15T21:30:00.000Z'); // This is 00:30:00 UTC+3
      
      const restriction = enhancedWithdrawalService.checkTimeBasedRestrictions();
      
      expect(restriction.canWithdraw).toBe(false);
      expect(restriction.timeRemaining).toBe('02:30:00');
    });

    it('should format time remaining correctly for different durations', () => {
      const testCases = [
        { input: 3661000, expected: '01:01:01' }, // 1 hour, 1 minute, 1 second
        { input: 7200000, expected: '02:00:00' }, // 2 hours exactly
        { input: 59000, expected: '00:00:59' },   // 59 seconds
        { input: 3600000, expected: '01:00:00' }, // 1 hour exactly
        { input: 0, expected: '00:00:00' },       // Edge case: 0 milliseconds
        { input: -1000, expected: '00:00:00' }    // Edge case: negative (should not happen)
      ];

      testCases.forEach(({ input, expected }) => {
        const service = enhancedWithdrawalService as any;
        const result = service.formatTimeRemaining(input);
        expect(result).toBe(expected);
      });
    });
  });

  describe('Cryptocurrency Selection Locking - Bug Fix Verification', () => {
    const defaultProps = {
      isOpen: true,
      onClose: () => {},
      initialCrypto: 'BTC',
      lockCryptocurrency: true,
      enableTimeBasedRestrictions: false, // Disable for locking tests
      showAdvancedValidation: false
    };

    it('should lock cryptocurrency selection when lockCryptocurrency is true', () => {
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal {...defaultProps} />
        </TestWrapper>
      );

      const cryptoSelect = screen.getByRole('combobox');
      expect(cryptoSelect).toBeDisabled();
      expect(screen.getByText('Locked')).toBeInTheDocument();
    });

    it('should show visual indicators for locked state', () => {
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal {...defaultProps} />
        </TestWrapper>
      );

      const cryptoSelect = screen.getByRole('combobox');
      expect(cryptoSelect).toHaveStyle({
        backgroundColor: '#1A1D21',
        borderColor: '#F0B90B',
        color: '#F0B90B'
      });
      
      expect(screen.getByText('Cryptocurrency selection is locked for this withdrawal')).toBeInTheDocument();
    });

    it('should allow cryptocurrency selection when lockCryptocurrency is false', () => {
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal {...defaultProps} lockCryptocurrency={false} />
        </TestWrapper>
      );

      const cryptoSelect = screen.getByRole('combobox');
      expect(cryptoSelect).not.toBeDisabled();
      expect(screen.queryByText('Locked')).not.toBeInTheDocument();
    });

    it('should prevent user interaction when cryptocurrency is locked', () => {
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal {...defaultProps} />
        </TestWrapper>
      );

      const cryptoSelect = screen.getByRole('combobox');
      
      // Try to change the value
      fireEvent.change(cryptoSelect, { target: { value: 'ETH' } });
      
      // Should still be BTC (locked)
      expect(cryptoSelect).toHaveValue('BTC');
    });

    it('should pre-select correct cryptocurrency when locked', () => {
      const testCases = ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'TRX', 'DOGE'];
      
      testCases.forEach(crypto => {
        const { unmount } = render(
          <TestWrapper>
            <ThreeStepWithdrawModal 
              {...defaultProps} 
              initialCrypto={crypto}
              lockCryptocurrency={true}
            />
          </TestWrapper>
        );

        const cryptoSelect = screen.getByRole('combobox');
        expect(cryptoSelect).toHaveValue(crypto);
        expect(cryptoSelect).toBeDisabled();
        
        unmount();
      });
    });
  });

  describe('Real-Time Countdown Updates', () => {
    it('should update countdown timer every second', async () => {
      // Start at 01:30:00 UTC+3
      mockDate('2024-01-15T22:30:00.000Z');
      
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal
            isOpen={true}
            onClose={() => {}}
            enableTimeBasedRestrictions={true}
          />
        </TestWrapper>
      );

      // Should show initial countdown
      await waitFor(() => {
        expect(screen.getByText('01:30:00')).toBeInTheDocument();
      });

      // Advance time by 1 second
      act(() => {
        mockDate('2024-01-15T22:30:01.000Z');
        jest.advanceTimersByTime(1000);
      });

      // Should show updated countdown
      await waitFor(() => {
        expect(screen.getByText('01:29:59')).toBeInTheDocument();
      });
    });

    it('should transition from restricted to allowed at 03:00 UTC+3', async () => {
      // Start at 02:59:58 UTC+3
      mockDate('2024-01-15T23:59:58.000Z');
      
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal
            isOpen={true}
            onClose={() => {}}
            enableTimeBasedRestrictions={true}
          />
        </TestWrapper>
      );

      // Should show restriction warning
      await waitFor(() => {
        expect(screen.getByText('Withdrawal Time Restriction')).toBeInTheDocument();
        expect(screen.getByText('00:00:02')).toBeInTheDocument();
      });

      // Advance time to 03:00:00 UTC+3
      act(() => {
        mockDate('2024-01-16T00:00:00.000Z');
        jest.advanceTimersByTime(2000);
      });

      // Restriction should be removed
      await waitFor(() => {
        expect(screen.queryByText('Withdrawal Time Restriction')).not.toBeInTheDocument();
      });
    });
  });

  describe('Cross-Timezone Verification', () => {
    const timezoneTestCases = [
      {
        name: 'UTC-5 (EST)',
        utcTime: '2024-01-15T19:30:00.000Z', // 14:30 EST = 22:30 UTC = 01:30 UTC+3
        expectedRestricted: true,
        expectedRemaining: '01:30:00'
      },
      {
        name: 'UTC+8 (China)',
        utcTime: '2024-01-15T16:30:00.000Z', // 00:30 China = 19:30 UTC = 22:30 UTC+3 (next day)
        expectedRestricted: false,
        expectedRemaining: ''
      },
      {
        name: 'UTC+0 (London)',
        utcTime: '2024-01-15T22:30:00.000Z', // 22:30 London = 01:30 UTC+3
        expectedRestricted: true,
        expectedRemaining: '01:30:00'
      },
      {
        name: 'UTC-8 (PST)',
        utcTime: '2024-01-15T16:30:00.000Z', // 08:30 PST = 01:30 UTC+3
        expectedRestricted: true,
        expectedRemaining: '01:30:00'
      }
    ];

    timezoneTestCases.forEach(({ name, utcTime, expectedRestricted, expectedRemaining }) => {
      it(`should handle ${name} timezone correctly`, () => {
        mockDate(utcTime);
        
        const restriction = enhancedWithdrawalService.checkTimeBasedRestrictions();
        
        expect(restriction.canWithdraw).toBe(!expectedRestricted);
        if (expectedRestricted) {
          expect(restriction.timeRemaining).toBe(expectedRemaining);
        }
      });
    });
  });

  describe('Integration with ThreeStepWithdrawModal', () => {
    it('should disable suggested amount buttons when time restricted', async () => {
      mockDate('2024-01-15T22:30:00.000Z'); // 01:30 UTC+3 - restricted
      
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal
            isOpen={true}
            onClose={() => {}}
            enableTimeBasedRestrictions={true}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        const buttons = screen.getAllByText(/\d+%/);
        buttons.forEach(button => {
          expect(button).toBeDisabled();
        });
      });
    });

    it('should enable suggested amount buttons when time allowed', async () => {
      mockDate('2024-01-16T00:00:00.000Z'); // 03:00 UTC+3 - allowed
      
      render(
        <TestWrapper>
          <ThreeStepWithdrawModal
            isOpen={true}
            onClose={() => {}}
            enableTimeBasedRestrictions={true}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        const buttons = screen.getAllByText(/\d+%/);
        buttons.forEach(button => {
          expect(button).not.toBeDisabled();
        });
      });
    });
  });
});
