import { Request, Response, NextFunction } from 'express';
import { Types } from 'mongoose';

// Mock user for development/testing
const mockUser = {
  _id: new Types.ObjectId('6847685d24a3eb6b1d646135'), // Match the userId in database
  email: '<EMAIL>',
  username: 'testuser',
  firstName: 'Test',
  lastName: 'User',
  isVerified: true,
  role: 'user',
  createdAt: new Date(),
  updatedAt: new Date()
};

// Mock authentication middleware for development
export const mockAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Only use in development environment
  if (process.env.NODE_ENV !== 'development') {
    return res.status(401).json({
      success: false,
      message: 'Mock authentication is only available in development mode'
    });
  }

  // Add mock user to request
  req.user = mockUser;
  
  console.log('🔧 Mock Auth: Using test user:', mockUser.email);
  
  next();
};

// Alternative mock auth that creates a user if not exists
export const mockAuthWithUserCreation = async (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV !== 'development') {
    return res.status(401).json({
      success: false,
      message: 'Mock authentication is only available in development mode'
    });
  }

  try {
    // Try to import User model dynamically to avoid circular dependencies
    const { default: User } = await import('../models/userModel');
    
    // Check if mock user exists in database
    let user = await User.findById(mockUser._id);
    
    if (!user) {
      // Create mock user if doesn't exist
      user = new User({
        _id: mockUser._id,
        email: mockUser.email,
        username: mockUser.username,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        password: 'hashedpassword123', // This won't be used
        isVerified: true,
        role: 'user'
      });
      
      await user.save();
      console.log('🔧 Mock Auth: Created test user in database');
    }
    
    req.user = user;
    console.log('🔧 Mock Auth: Using database user:', user.email);
    
    next();
  } catch (error) {
    console.error('Mock auth error:', error);
    // Fallback to simple mock user
    req.user = mockUser;
    next();
  }
};

export default mockAuthMiddleware;
