// Mock system configuration service for development/testing

export interface MockSystemConfig {
  supportedCurrencies: Array<{
    symbol: string;
    name: string;
    networks: string[];
    addressFormat: string;
    isActive: boolean;
    minWithdrawal?: number;
    maxWithdrawal?: number;
    withdrawalFee?: number;
  }>;
  withdrawalSettings: {
    dailyLimit: number;
    monthlyLimit: number;
    verificationRequired: boolean;
  };
  commissionRates: {
    [key: string]: number;
  };
  maintenanceMode: boolean;
}

// Mock system configuration data
const mockSystemConfig: MockSystemConfig = {
  supportedCurrencies: [
    {
      symbol: 'BTC',
      name: 'Bitcoin',
      networks: ['mainnet'],
      addressFormat: 'Legacy (1...) or SegWit (bc1...)',
      isActive: true,
      minWithdrawal: 0.001,
      maxWithdrawal: 10,
      withdrawalFee: 0.0005
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      networks: ['mainnet', 'arbitrum', 'optimism'],
      addressFormat: '0x... format (42 characters)',
      isActive: true,
      minWithdrawal: 0.01,
      maxWithdrawal: 100,
      withdrawalFee: 0.005
    },
    {
      symbol: 'USDT',
      name: 'Tether USD',
      networks: ['ethereum', 'tron', 'bsc'],
      addressFormat: '0x... (ERC-20/BEP-20) or T... (TRC-20)',
      isActive: true,
      minWithdrawal: 10,
      maxWithdrawal: 50000,
      withdrawalFee: 1
    },
    {
      symbol: 'BNB',
      name: 'Binance Coin',
      networks: ['bsc'],
      addressFormat: '0x... format (42 characters)',
      isActive: true,
      minWithdrawal: 0.01,
      maxWithdrawal: 100,
      withdrawalFee: 0.001
    },
    {
      symbol: 'ADA',
      name: 'Cardano',
      networks: ['mainnet'],
      addressFormat: 'addr1... format (Cardano address)',
      isActive: true,
      minWithdrawal: 10,
      maxWithdrawal: 10000,
      withdrawalFee: 1
    },
    {
      symbol: 'DOT',
      name: 'Polkadot',
      networks: ['mainnet'],
      addressFormat: '1... format (Polkadot address)',
      isActive: true,
      minWithdrawal: 1,
      maxWithdrawal: 1000,
      withdrawalFee: 0.1
    }
  ],
  withdrawalSettings: {
    dailyLimit: 10000,
    monthlyLimit: 100000,
    verificationRequired: true
  },
  commissionRates: {
    BTC: 0.001,
    ETH: 0.002,
    USDT: 1.0,
    BNB: 0.001,
    ADA: 1.0,
    DOT: 0.1
  },
  maintenanceMode: false
};

export const mockSystemConfigService = {
  // Get system configuration
  async getSystemConfig(): Promise<{ success: boolean; data: MockSystemConfig }> {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
    
    console.log('🔧 Mock System Config Service: Returning mock data');
    
    return {
      success: true,
      data: mockSystemConfig
    };
  },

  // Get public system info
  async getPublicSystemInfo(): Promise<{ success: boolean; data: any }> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return {
      success: true,
      data: {
        systemName: 'CryptoYield Platform',
        version: '1.0.0',
        maintenanceMode: mockSystemConfig.maintenanceMode,
        supportedCurrencies: mockSystemConfig.supportedCurrencies.filter(c => c.isActive).map(c => c.symbol)
      }
    };
  },

  // Get commission rates
  async getCommissionRates(): Promise<{ success: boolean; data: any }> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return {
      success: true,
      data: mockSystemConfig.commissionRates
    };
  },

  // Get transaction settings
  async getTransactionSettings(): Promise<{ success: boolean; data: any }> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return {
      success: true,
      data: mockSystemConfig.withdrawalSettings
    };
  }
};

export default mockSystemConfigService;
