import React from 'react';
import { <PERSON>, Container, Heading, Button, VStack, HStack, Text, Alert, AlertIcon } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import useAuth from '../hooks/useAuth';

const QuickTest = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  const handleSetMockUser = () => {
    const mockUser = {
      _id: "6838bf7656339ea52d689084",
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User",
      country: "TR",
      city: "Istanbul",
      kycVerified: false,
      twoFactorEnabled: false,
      token: "mock-token",
      referralCode: "C7D026FE",
      referralCount: 0,
      referralEarnings: 0,
      marketingConsent: false,
      isAdmin: false
    };

    localStorage.setItem('user', JSON.stringify(mockUser));
    console.log('✅ Mock user set in localStorage');
    
    // Reload page to trigger AuthContext
    window.location.reload();
  };

  const handleClearStorage = () => {
    localStorage.clear();
    console.log('🗑️ localStorage cleared');
    window.location.reload();
  };

  const handleTestAPI = async () => {
    try {
      console.log('🔄 Testing API...');
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      
      const response = await fetch(`${API_URL.replace('/api', '')}/health`);
      const data = await response.json();
      console.log('✅ API Response:', data);
    } catch (error) {
      console.error('❌ API Error:', error);
    }
  };

  const handleGoToProfile = () => {
    navigate('/profile');
  };

  return (
    <Container maxW="container.md" py={8}>
      <VStack spacing={6} align="stretch">
        <Heading size="lg" color="gold.400">Quick Test Page</Heading>
        
        <Alert status="info">
          <AlertIcon />
          Bu sayfa hızlı test için kullanılır. Browser console'u (F12) açık tutun.
        </Alert>

        <Box bg="gray.800" p={6} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <Heading size="md" mb={4} color="gold.400">Auth State</Heading>
          <VStack align="start" spacing={2}>
            <Text><strong>Loading:</strong> {loading ? 'True' : 'False'}</Text>
            <Text><strong>User:</strong> {user ? user.email : 'None'}</Text>
            <Text><strong>User ID:</strong> {user?._id || 'None'}</Text>
            <Text><strong>LocalStorage User:</strong> {localStorage.getItem('user') ? 'Exists' : 'None'}</Text>
          </VStack>
        </Box>

        <HStack spacing={4} wrap="wrap">
          <Button colorScheme="blue" onClick={handleSetMockUser}>
            Set Mock User
          </Button>
          
          <Button colorScheme="purple" onClick={handleTestAPI}>
            Test API
          </Button>
          
          <Button colorScheme="green" onClick={handleGoToProfile}>
            Go to Profile
          </Button>
          
          <Button colorScheme="red" onClick={handleClearStorage}>
            Clear Storage
          </Button>
        </HStack>

        <Box bg="gray.800" p={6} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <Heading size="md" mb={4} color="gold.400">Instructions</Heading>
          <VStack align="start" spacing={2}>
            <Text>1. "Set Mock User" butonuna tıklayın</Text>
            <Text>2. Sayfa yeniden yüklenecek</Text>
            <Text>3. "Go to Profile" butonuna tıklayın</Text>
            <Text>4. Console'da logları kontrol edin</Text>
          </VStack>
        </Box>
      </VStack>
    </Container>
  );
};

export default QuickTest;
