import React from 'react';
import {
  <PERSON>,
  Flex,
  Text,
  Grid,
  VStack,
  Button,
} from '@chakra-ui/react';
import { getCryptoName, getCryptoColor } from '../utils/cryptoIcons';
import { formatUSD } from '../utils/exchangeRates';
import { useCryptoPrice } from '../hooks/useCryptoPrices';

interface WalletAsset {
  symbol: string;
  balance: number;
  interestBalance: number;
  commissionBalance: number;
}

interface WalletCardProps {
  asset: WalletAsset;
  onDeposit?: (currency: string, walletData?: any) => void;
  onWithdraw?: (currency: string, walletData?: any) => void;
}

const WalletCard: React.FC<WalletCardProps> = ({
  asset,
  onDeposit,
  onWithdraw,
}) => {
  // Get USD rate from API using custom hook
  const { price: usdRate, isLoading: isLoadingPrice } = useCryptoPrice(asset.symbol);

  // Calculate total balance
  const totalBalance = asset.balance;

  // Get crypto-specific colors
  const primaryColor = getCryptoColor(asset.symbol);
  const secondaryColor = getCryptoColor(asset.symbol, '#FF6B35'); // Slightly different shade for gradients

  // Format currency function
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ${asset.symbol}`;
  };

  // Calculate USD equivalent
  const totalBalanceUSD = totalBalance * usdRate;

  // Calculate daily interest and percentage
  const dailyInterest = asset.interestBalance;
  const dailyInterestPercentage = totalBalance > 0 ? (dailyInterest / totalBalance) * 100 : 0;
  const totalEarnings = asset.interestBalance + asset.commissionBalance;

  return (
    <Box
      bg="rgba(255, 255, 255, 0.05)"
      backdropFilter="blur(20px)"
      borderRadius="24px"
      borderWidth="1px"
      borderColor="rgba(255, 255, 255, 0.1)"
      overflow="hidden"
      position="relative"
      transition="all 0.3s ease"
      boxShadow="0 20px 40px rgba(0, 0, 0, 0.3)"
      w="100%"
      maxW="380px"
      p="24px"
      fontFamily="'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(255, 69, 58, 0.4), transparent)',
        zIndex: 1,
      }}
    >
      {/* Wallet Header */}
      <Flex
        justify="space-between"
        align="center"
        mb="24px"
        position="relative"
        zIndex={2}
      >
        <Flex align="center" gap="12px">
          {/* Logo Icon */}
          <Flex
            w="40px"
            h="40px"
            bg={`linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`}
            borderRadius="12px"
            align="center"
            justify="center"
            fontWeight="bold"
            color="white"
            fontSize="16px"
          >
            {asset.symbol}
          </Flex>

          <VStack align="start" spacing="2px">
            <Text
              color="#ffffff"
              fontSize="18px"
              fontWeight="600"
              lineHeight="1"
            >
              {asset.symbol === 'TRX' ? 'TRON' : getCryptoName(asset.symbol)} Wallet
            </Text>
            <Text
              color="#94a3b8"
              fontSize="12px"
              fontWeight="400"
            >
              Premium Account
            </Text>
          </VStack>
        </Flex>

        {/* Settings Button */}
        <Button
          w="40px"
          h="40px"
          bg="rgba(255, 255, 255, 0.1)"
          border="none"
          borderRadius="12px"
          color="#94a3b8"
          _hover={{
            bg: `${primaryColor}33`,
            color: primaryColor,
          }}
          transition="all 0.3s ease"
          p={0}
          minW="40px"
        >
          ⚙
        </Button>
      </Flex>

      {/* Balance Section */}
      <Box
        textAlign="center"
        mb="20px"
        position="relative"
        zIndex={2}
      >
        <Text
          color="#94a3b8"
          fontSize="12px"
          fontWeight="500"
          mb="6px"
          textTransform="uppercase"
          letterSpacing="0.5px"
        >
          Main Balance
        </Text>
        <Text
          color="#ffffff"
          fontSize="25px"
          fontWeight="700"
          mb="6px"
          background={`linear-gradient(135deg, ${primaryColor} 0%, #ffffff 100%)`}
          bgClip="text"
          sx={{
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
          lineHeight="1.1"
        >
          {totalBalance.toLocaleString()} {asset.symbol}
        </Text>
        <Text
          color="#94a3b8"
          fontSize="14px"
          fontWeight="500"
        >
          {isLoadingPrice ? (
            "≈ Loading... USD"
          ) : (
            `≈ ${formatUSD(totalBalanceUSD)} USD`
          )}
        </Text>
      </Box>

      {/* Daily Interest Card */}
      <Box
        bg="rgba(255, 255, 255, 0.05)"
        border={`1px solid ${primaryColor}33`}
        borderRadius="16px"
        p="20px"
        mb="20px"
        transition="all 0.3s ease"
        position="relative"
        overflow="hidden"
        _hover={{
          bg: `${primaryColor}1A`,
          transform: "translateY(-2px)",
          borderColor: `${primaryColor}66`,
        }}
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: `linear-gradient(90deg, transparent, ${primaryColor}66, transparent)`,
        }}
      >
        <Text
          color="#94a3b8"
          fontSize="11px"
          fontWeight="500"
          mb="8px"
          textTransform="uppercase"
          letterSpacing="0.5px"
        >
          Daily Interest
        </Text>
        <Text
          color="#ffffff"
          fontSize="15px"
          fontWeight="600"
          mb="6px"
          lineHeight="1.1"
        >
          {formatCurrency(dailyInterest)}
        </Text>
        <Flex align="center" gap="4px">
          <Text fontSize="12px" fontWeight="500" color="#10b981">
            ↗ +1%
          </Text>
        </Flex>
      </Box>

      {/* Commission Section */}
      <Box
        bg="rgba(255, 255, 255, 0.05)"
        border={`1px solid ${primaryColor}33`}
        borderRadius="16px"
        p="20px"
        mb="20px"
        position="relative"
        overflow="hidden"
        boxShadow={`0 0 20px ${primaryColor}33`}
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: `linear-gradient(90deg, transparent, ${primaryColor}66, transparent)`,
        }}
      >
        {/* Commission Header */}
        <Flex justify="space-between" align="center" mb="14px">
          <Text
            color="#ffffff"
            fontSize="14px"
            fontWeight="600"
          >
            Total Commission
          </Text>
          <Box
            bg={`linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`}
            color="white"
            fontSize="10px"
            fontWeight="600"
            px="6px"
            py="3px"
            borderRadius="4px"
            textTransform="uppercase"
            letterSpacing="0.5px"
          >
            Active
          </Box>
        </Flex>

        {/* Commission Breakdown */}
        <Flex justify="space-between" mb="10px">
          <Box flex={1}>
            <Text
              color="#94a3b8"
              fontSize="11px"
              fontWeight="500"
              mb="3px"
            >
              Interest
            </Text>
            <Text
              color="#ffffff"
              fontSize="14px"
              fontWeight="600"
              lineHeight="1.2"
            >
              {formatCurrency(asset.interestBalance)}
            </Text>
          </Box>
          <Box flex={1}>
            <Text
              color="#94a3b8"
              fontSize="11px"
              fontWeight="500"
              mb="3px"
            >
              Referral
            </Text>
            <Text
              color="#ffffff"
              fontSize="14px"
              fontWeight="600"
              lineHeight="1.2"
            >
              {formatCurrency(asset.commissionBalance)}
            </Text>
          </Box>
        </Flex>

        {/* Commission Total */}
        <Box
          borderTop="1px solid rgba(255, 255, 255, 0.1)"
          pt="10px"
        >
          <Flex justify="space-between" align="center">
            <Text
              color="#94a3b8"
              fontSize="12px"
              fontWeight="500"
            >
              Total Earnings
            </Text>
            <Text
              color="#10b981"
              fontSize="16px"
              fontWeight="700"
              lineHeight="1.2"
              animation="pulse 2s infinite"
              sx={{
                '@keyframes pulse': {
                  '0%, 100%': { opacity: 1 },
                  '50%': { opacity: 0.7 },
                },
              }}
            >
              {formatCurrency(totalEarnings)}
            </Text>
          </Flex>
        </Box>
      </Box>

      {/* Action Modules - Compact Design */}
      <Grid
        templateColumns="1fr 1fr"
        gap="12px"
        position="relative"
        zIndex={2}
      >
        {/* Deposit Module */}
        <Box
          bg="rgba(255, 255, 255, 0.05)"
          border="1px solid rgba(16, 185, 129, 0.3)"
          borderRadius="12px"
          p="16px"
          textAlign="center"
          cursor="pointer"
          transition="all 0.3s ease"
          position="relative"
          overflow="hidden"
          onClick={() => onDeposit?.(asset.symbol, asset)}
          _hover={{
            bg: "rgba(16, 185, 129, 0.1)",
            borderColor: "rgba(16, 185, 129, 0.5)",
            transform: "translateY(-1px)",
          }}
        >
          <Flex
            w="32px"
            h="32px"
            bg="linear-gradient(135deg, #10b981 0%, #059669 100%)"
            borderRadius="8px"
            align="center"
            justify="center"
            mx="auto"
            mb="8px"
            fontSize="16px"
            color="white"
            transition="all 0.3s ease"
          >
            ↓
          </Flex>
          <Text
            color="#ffffff"
            fontSize="13px"
            fontWeight="600"
            mb="2px"
            lineHeight="1.2"
          >
            Deposit
          </Text>
          <Text
            color="#94a3b8"
            fontSize="7px"
            fontWeight="500"
            lineHeight="1.3"
          >
            Add {asset.symbol} to wallet
          </Text>
        </Box>

        {/* Withdraw Module */}
        <Box
          bg="rgba(255, 255, 255, 0.05)"
          border="1px solid rgba(239, 68, 68, 0.3)"
          borderRadius="12px"
          p="16px"
          textAlign="center"
          cursor="pointer"
          transition="all 0.3s ease"
          position="relative"
          overflow="hidden"
          onClick={() => onWithdraw?.(asset.symbol, asset)}
          _hover={{
            bg: "rgba(239, 68, 68, 0.1)",
            borderColor: "rgba(239, 68, 68, 0.5)",
            transform: "translateY(-1px)",
          }}
        >
          <Flex
            w="32px"
            h="32px"
            bg="linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"
            borderRadius="8px"
            align="center"
            justify="center"
            mx="auto"
            mb="8px"
            fontSize="16px"
            color="white"
            transition="all 0.3s ease"
          >
            ↑
          </Flex>
          <Text
            color="#ffffff"
            fontSize="13px"
            fontWeight="600"
            mb="2px"
            lineHeight="1.2"
          >
            Withdraw
          </Text>
          <Text
            color="#94a3b8"
            fontSize="7px"
            fontWeight="500"
            lineHeight="1.3"
          >
            Send {asset.symbol} from wallet
          </Text>
        </Box>
      </Grid>
    </Box>
  );
};

export default WalletCard;
