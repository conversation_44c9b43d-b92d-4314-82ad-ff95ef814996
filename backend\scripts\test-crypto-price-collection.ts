#!/usr/bin/env npx ts-node

/**
 * Test script để kiểm tra chức năng thu thập và lưu trữ giá tiền mã hóa
 * 
 * Chạy script: npx ts-node backend/scripts/test-crypto-price-collection.ts
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import { cryptoDataCollectionService } from '../src/services/cryptoDataCollectionService';
import CryptoPriceHistory from '../src/models/cryptoPriceHistoryModel';
import { logger } from '../src/utils/logger';

// Load environment variables
config();

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  details?: any;
  executionTime?: number;
}

class CryptoPriceCollectionTest {
  private results: TestResult[] = [];

  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARN', message: string, details?: any, executionTime?: number) {
    this.results.push({ test, status, message, details, executionTime });
  }

  /**
   * Kết nối database
   */
  async connectDatabase(): Promise<void> {
    try {
      const mongoURI = process.env.MONGO_URI || '*******************************************************************';
      console.log(`🔗 Đang kết nối database: ${mongoURI.replace(/\/\/.*@/, '//***:***@')}`);
      await mongoose.connect(mongoURI);
      this.addResult('Database Connection', 'PASS', 'Kết nối database thành công');
    } catch (error: any) {
      this.addResult('Database Connection', 'FAIL', `Lỗi kết nối database: ${error.message}`);
      throw error;
    }
  }

  /**
   * Test thu thập dữ liệu giá
   */
  async testPriceCollection(): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('🔄 Bắt đầu test thu thập dữ liệu giá...');
      
      const results = await cryptoDataCollectionService.collectAllPriceData();
      const stats = cryptoDataCollectionService.getCollectionStats();
      
      const executionTime = Date.now() - startTime;
      
      if (results.length > 0) {
        this.addResult(
          'Price Collection', 
          'PASS', 
          `Thu thập thành công ${results.length} cryptocurrency`,
          {
            totalSymbols: stats.totalSymbols,
            successful: stats.successfulCollections,
            failed: stats.failedCollections,
            savedToDatabase: stats.savedToDatabase,
            samplePrices: results.slice(0, 3).map(r => ({ symbol: r.symbol, price: r.price }))
          },
          executionTime
        );
      } else {
        this.addResult('Price Collection', 'FAIL', 'Không thu thập được dữ liệu nào');
      }
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      this.addResult('Price Collection', 'FAIL', `Lỗi thu thập dữ liệu: ${error.message}`, null, executionTime);
    }
  }

  /**
   * Test lưu trữ dữ liệu vào database
   */
  async testDatabaseStorage(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Kiểm tra số lượng bản ghi trong database
      const totalRecords = await CryptoPriceHistory.countDocuments();
      
      // Lấy một số bản ghi mới nhất
      const latestRecords = await CryptoPriceHistory.find()
        .sort({ timestamp: -1 })
        .limit(5);
      
      const executionTime = Date.now() - startTime;
      
      if (totalRecords > 0) {
        this.addResult(
          'Database Storage', 
          'PASS', 
          `Database có ${totalRecords} bản ghi giá`,
          {
            totalRecords,
            latestRecords: latestRecords.map(r => ({
              symbol: r.symbol,
              price: r.price,
              timestamp: r.timestamp,
              source: r.source
            }))
          },
          executionTime
        );
      } else {
        this.addResult('Database Storage', 'WARN', 'Database chưa có dữ liệu giá nào');
      }
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      this.addResult('Database Storage', 'FAIL', `Lỗi kiểm tra database: ${error.message}`, null, executionTime);
    }
  }

  /**
   * Test lấy lịch sử giá
   */
  async testPriceHistory(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const symbol = 'BTC';
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000); // 24h trước
      
      const history = await cryptoDataCollectionService.getPriceHistory(symbol, startDate, endDate, 10);
      
      const executionTime = Date.now() - startTime;
      
      if (history.length > 0) {
        this.addResult(
          'Price History', 
          'PASS', 
          `Lấy được ${history.length} bản ghi lịch sử giá cho ${symbol}`,
          {
            symbol,
            recordCount: history.length,
            dateRange: { startDate, endDate },
            sampleData: history.slice(0, 3).map(h => ({
              price: h.price,
              timestamp: h.lastUpdated,
              source: h.source
            }))
          },
          executionTime
        );
      } else {
        this.addResult('Price History', 'WARN', `Không có dữ liệu lịch sử cho ${symbol}`);
      }
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      this.addResult('Price History', 'FAIL', `Lỗi lấy lịch sử giá: ${error.message}`, null, executionTime);
    }
  }

  /**
   * Test lấy giá mới nhất từ database
   */
  async testLatestPrices(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const latestPrices = await cryptoDataCollectionService.getAllLatestPricesFromDB();
      
      const executionTime = Date.now() - startTime;
      
      if (latestPrices.length > 0) {
        this.addResult(
          'Latest Prices', 
          'PASS', 
          `Lấy được giá mới nhất cho ${latestPrices.length} cryptocurrency`,
          {
            count: latestPrices.length,
            symbols: latestPrices.map(p => p.symbol),
            samplePrices: latestPrices.slice(0, 3).map(p => ({
              symbol: p.symbol,
              price: p.price,
              lastUpdated: p.lastUpdated
            }))
          },
          executionTime
        );
      } else {
        this.addResult('Latest Prices', 'WARN', 'Không có dữ liệu giá mới nhất trong database');
      }
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      this.addResult('Latest Prices', 'FAIL', `Lỗi lấy giá mới nhất: ${error.message}`, null, executionTime);
    }
  }

  /**
   * Test dọn dẹp dữ liệu cũ
   */
  async testDataCleanup(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test với 1 ngày để xem có hoạt động không (không xóa dữ liệu thật)
      const deletedCount = await cryptoDataCollectionService.cleanOldPriceData(1);
      
      const executionTime = Date.now() - startTime;
      
      this.addResult(
        'Data Cleanup', 
        'PASS', 
        `Chức năng dọn dẹp hoạt động bình thường`,
        {
          deletedRecords: deletedCount,
          note: 'Test với 1 ngày để kiểm tra chức năng'
        },
        executionTime
      );
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      this.addResult('Data Cleanup', 'FAIL', `Lỗi dọn dẹp dữ liệu: ${error.message}`, null, executionTime);
    }
  }

  /**
   * Chạy tất cả các test
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Bắt đầu test chức năng thu thập giá tiền mã hóa...\n');

    try {
      await this.connectDatabase();
      await this.testPriceCollection();
      await this.testDatabaseStorage();
      await this.testPriceHistory();
      await this.testLatestPrices();
      await this.testDataCleanup();
    } catch (error) {
      console.error('❌ Test bị dừng do lỗi nghiêm trọng:', error);
    } finally {
      await mongoose.disconnect();
    }

    this.printResults();
  }

  /**
   * In kết quả test
   */
  private printResults(): void {
    console.log('\n📊 KẾT QUẢ TEST');
    console.log('='.repeat(50));

    const categories = [...new Set(this.results.map(r => r.test))];
    
    categories.forEach(category => {
      console.log(`\n📁 ${category.toUpperCase()}`);
      console.log('-'.repeat(40));
      
      const categoryResults = this.results.filter(r => r.test === category);
      categoryResults.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        const timeInfo = result.executionTime ? ` (${result.executionTime}ms)` : '';
        console.log(`${statusIcon} ${result.test}: ${result.message}${timeInfo}`);
        
        if (result.details) {
          console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
        }
      });
    });

    // Tổng kết
    const passCount = this.results.filter(r => r.status === 'PASS').length;
    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const warnCount = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📈 TỔNG KẾT');
    console.log('='.repeat(30));
    console.log(`✅ Thành công: ${passCount}`);
    console.log(`❌ Thất bại: ${failCount}`);
    console.log(`⚠️ Cảnh báo: ${warnCount}`);
    console.log(`📊 Tổng cộng: ${this.results.length}`);

    if (failCount === 0) {
      console.log('\n🎉 Tất cả test đều thành công!');
    } else {
      console.log('\n⚠️ Có một số test thất bại, vui lòng kiểm tra lại.');
    }
  }
}

// Chạy test
const test = new CryptoPriceCollectionTest();
test.runAllTests().catch(console.error);
