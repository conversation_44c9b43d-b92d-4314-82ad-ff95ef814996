#!/bin/bash

# MongoDB Transaction Test Script
# This script tests MongoDB replica set and transaction functionality

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== MongoDB Transaction Test ===${NC}"

# Function to run MongoDB command
run_mongo_cmd() {
    docker exec cryptoyield-mongodb mongosh --quiet --eval "$1" 2>/dev/null
}

# Test 1: Check if MongoDB is running
echo -e "${YELLOW}Test 1: Checking MongoDB connection...${NC}"
PING_RESULT=$(run_mongo_cmd "db.adminCommand('ping')")
if echo "$PING_RESULT" | grep -q '"ok" : 1'; then
    echo -e "${GREEN}✓ MongoDB is running${NC}"
else
    echo -e "${RED}✗ MongoDB is not responding${NC}"
    exit 1
fi

# Test 2: Check replica set status
echo -e "${YELLOW}Test 2: Checking replica set status...${NC}"
RS_STATUS=$(run_mongo_cmd "
try {
    var status = rs.status();
    if (status.ok === 1) {
        print('REPLICA_SET_OK');
        print('Set: ' + status.set);
        print('Primary: ' + status.members.find(m => m.stateStr === 'PRIMARY').name);
    } else {
        print('REPLICA_SET_ERROR');
    }
} catch (e) {
    print('REPLICA_SET_NOT_INITIALIZED: ' + e.message);
}
")

if echo "$RS_STATUS" | grep -q "REPLICA_SET_OK"; then
    echo -e "${GREEN}✓ Replica set is running${NC}"
    echo "$RS_STATUS" | grep -E "(Set:|Primary:)"
else
    echo -e "${RED}✗ Replica set is not properly configured${NC}"
    echo "$RS_STATUS"
    exit 1
fi

# Test 3: Test authentication
echo -e "${YELLOW}Test 3: Testing authentication...${NC}"
AUTH_TEST=$(run_mongo_cmd "
try {
    db.getSiblingDB('admin').auth('cryptoyield_admin', 'secure_password123');
    print('AUTH_OK');
} catch (e) {
    print('AUTH_ERROR: ' + e.message);
}
")

if echo "$AUTH_TEST" | grep -q "AUTH_OK"; then
    echo -e "${GREEN}✓ Authentication successful${NC}"
else
    echo -e "${RED}✗ Authentication failed${NC}"
    echo "$AUTH_TEST"
    exit 1
fi

# Test 4: Test transaction capability
echo -e "${YELLOW}Test 4: Testing transaction capability...${NC}"
TRANSACTION_TEST=$(run_mongo_cmd "
try {
    db.getSiblingDB('admin').auth('cryptoyield_admin', 'secure_password123');
    var session = db.getMongo().startSession();
    session.startTransaction();
    
    // Test database operations within transaction
    var testDb = session.getDatabase('cryptoyield');
    var testCollection = testDb.getCollection('test_transactions');
    
    // Insert test document
    testCollection.insertOne({test: 'transaction_test', timestamp: new Date()});
    
    // Commit transaction
    session.commitTransaction();
    session.endSession();
    
    print('TRANSACTION_SUCCESS');
} catch (e) {
    print('TRANSACTION_ERROR: ' + e.message);
}
")

if echo "$TRANSACTION_TEST" | grep -q "TRANSACTION_SUCCESS"; then
    echo -e "${GREEN}✓ Transactions are working perfectly${NC}"
else
    echo -e "${RED}✗ Transaction test failed${NC}"
    echo "$TRANSACTION_TEST"
    exit 1
fi

# Test 5: Test database operations
echo -e "${YELLOW}Test 5: Testing database operations...${NC}"
DB_TEST=$(run_mongo_cmd "
try {
    db.getSiblingDB('admin').auth('cryptoyield_admin', 'secure_password123');
    var cryptoyieldDb = db.getSiblingDB('cryptoyield');
    
    // Test collection creation and operations
    var testCollection = cryptoyieldDb.getCollection('connection_test');
    testCollection.insertOne({
        test: 'connection_test',
        timestamp: new Date(),
        message: 'MongoDB is working correctly'
    });
    
    var count = testCollection.countDocuments({test: 'connection_test'});
    print('DB_TEST_SUCCESS: ' + count + ' documents found');
    
    // Clean up test data
    testCollection.deleteMany({test: 'connection_test'});
    
} catch (e) {
    print('DB_TEST_ERROR: ' + e.message);
}
")

if echo "$DB_TEST" | grep -q "DB_TEST_SUCCESS"; then
    echo -e "${GREEN}✓ Database operations working${NC}"
    echo "$DB_TEST" | grep "DB_TEST_SUCCESS"
else
    echo -e "${RED}✗ Database operations failed${NC}"
    echo "$DB_TEST"
fi

# Test 6: Check Mongo Express connectivity
echo -e "${YELLOW}Test 6: Checking Mongo Express...${NC}"
if curl -s http://localhost:8081 > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Mongo Express is accessible at http://localhost:8081${NC}"
else
    echo -e "${YELLOW}⚠ Mongo Express is not accessible (this is optional)${NC}"
fi

# Summary
echo -e "${BLUE}=== Test Summary ===${NC}"
echo -e "${GREEN}✓ MongoDB is properly configured for transactions${NC}"
echo -e "${GREEN}✓ Replica set is running (rs0)${NC}"
echo -e "${GREEN}✓ Authentication is working${NC}"
echo -e "${GREEN}✓ Transactions are fully supported${NC}"
echo ""
echo -e "${YELLOW}Connection Details:${NC}"
echo -e "${BLUE}Host: localhost:27017${NC}"
echo -e "${BLUE}Database: cryptoyield${NC}"
echo -e "${BLUE}Username: cryptoyield_admin${NC}"
echo -e "${BLUE}Replica Set: rs0${NC}"
echo ""
echo -e "${YELLOW}Connection String for Application:${NC}"
echo "**********************************************************************************************************"
echo ""
echo -e "${GREEN}All tests passed! MongoDB is ready for production use. 🎉${NC}"
