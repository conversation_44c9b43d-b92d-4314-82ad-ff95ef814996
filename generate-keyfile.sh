#!/bin/bash

# Script to generate MongoDB keyFile for replica set authentication

echo "Generating MongoDB keyFile for replica set authentication..."

# Create keyfile directory if it doesn't exist
mkdir -p ./mongodb-keyfile

# Generate a random 1024-byte keyfile
openssl rand -base64 756 > ./mongodb-keyfile/mongodb-keyfile

# Set proper permissions (MongoDB requires 600 or 400)
chmod 600 ./mongodb-keyfile/mongodb-keyfile

echo "✓ KeyFile generated successfully at ./mongodb-keyfile/mongodb-keyfile"
echo "✓ Permissions set to 600"

# Display the keyfile content (first few lines for verification)
echo ""
echo "KeyFile content (first 5 lines):"
head -5 ./mongodb-keyfile/mongodb-keyfile

echo ""
echo "KeyFile is ready for use in Docker Compose!"
