#!/bin/bash

# MongoDB Docker Setup Script for CryptoYield
# This script sets up MongoDB with replica set support for transactions

set -e

echo "🚀 Setting up MongoDB with Docker for CryptoYield..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_success "Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

print_success "docker-compose is available"

# Create data directories
print_status "Creating data directories..."
mkdir -p ./data/mongodb
mkdir -p ./data/mongodb-config
mkdir -p ./data/redis

# Set proper permissions for MongoDB data directory
chmod 755 ./data/mongodb
chmod 755 ./data/mongodb-config
chmod 755 ./data/redis

print_success "Data directories created"

# Check if keyfile exists
if [ ! -f "./mongodb-keyfile/mongodb-keyfile" ]; then
    print_error "MongoDB keyfile not found. Please ensure ./mongodb-keyfile/mongodb-keyfile exists."
    exit 1
fi

print_success "MongoDB keyfile found"

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f docker-compose.mongo.yml down --remove-orphans || true

# Remove any existing volumes (optional - comment out if you want to preserve data)
print_warning "Removing existing volumes to ensure clean setup..."
docker volume rm cryptoyield_mongodb_data 2>/dev/null || true
docker volume rm cryptoyield_mongodb_config 2>/dev/null || true
docker volume rm cryptoyield_redis_data 2>/dev/null || true

# Start MongoDB first
print_status "Starting MongoDB..."
docker-compose -f docker-compose.mongo.yml up -d mongodb

# Wait for MongoDB to be healthy
print_status "Waiting for MongoDB to be healthy..."
timeout=300  # 5 minutes
elapsed=0
interval=10

while [ $elapsed -lt $timeout ]; do
    if docker-compose -f docker-compose.mongo.yml ps mongodb | grep -q "healthy"; then
        print_success "MongoDB is healthy"
        break
    fi
    
    print_status "Waiting for MongoDB... ($elapsed/$timeout seconds)"
    sleep $interval
    elapsed=$((elapsed + interval))
done

if [ $elapsed -ge $timeout ]; then
    print_error "MongoDB failed to become healthy within $timeout seconds"
    print_status "Checking MongoDB logs..."
    docker-compose -f docker-compose.mongo.yml logs mongodb
    exit 1
fi

# Test MongoDB connection and replica set
print_status "Testing MongoDB connection and replica set..."
docker exec cryptoyield-mongodb mongosh --quiet --eval "
try {
    db.adminCommand('ping');
    print('✅ MongoDB connection successful');
    
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set is active: ' + status.set);
        const primary = status.members.find(m => m.stateStr === 'PRIMARY');
        if (primary) {
            print('✅ Primary member found: ' + primary.name);
        } else {
            print('⚠️ No primary member found yet');
        }
    } else {
        print('❌ Replica set not ready');
    }
} catch (e) {
    print('❌ Error: ' + e.message);
    exit(1);
}
"

# Test transaction support
print_status "Testing transaction support..."
docker exec cryptoyield-mongodb mongosh --quiet --eval "
try {
    const session = db.getMongo().startSession();
    session.startTransaction();
    
    // Test transaction with a simple operation
    const testDb = session.getDatabase('test');
    testDb.transactionTest.insertOne({test: 'transaction', timestamp: new Date()}, {session: session});
    
    session.commitTransaction();
    session.endSession();
    
    print('✅ Transaction test successful');
} catch (e) {
    print('❌ Transaction test failed: ' + e.message);
    exit(1);
}
"

# Start Redis
print_status "Starting Redis..."
docker-compose -f docker-compose.mongo.yml up -d redis

# Wait for Redis to be ready
print_status "Waiting for Redis to be ready..."
sleep 10

# Test Redis connection
print_status "Testing Redis connection..."
if docker exec cryptoyield-redis redis-cli ping | grep -q "PONG"; then
    print_success "Redis is ready"
else
    print_error "Redis is not responding"
    exit 1
fi

# Start Mongo Express
print_status "Starting Mongo Express..."
docker-compose -f docker-compose.mongo.yml up -d mongo-express

# Wait for Mongo Express to be ready
print_status "Waiting for Mongo Express to be ready..."
sleep 20

# Check if Mongo Express is accessible
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 | grep -q "200"; then
    print_success "Mongo Express is accessible at http://localhost:8081"
    print_status "Login credentials: admin / admin123"
else
    print_warning "Mongo Express may not be ready yet. Check http://localhost:8081 in a few minutes."
fi

# Display final status
print_success "MongoDB Docker setup completed successfully!"
echo ""
echo "📋 Service Status:"
echo "  🗄️  MongoDB: localhost:27017 (replica set: rs0)"
echo "  🔴 Redis: localhost:6379"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo ""
echo "🔗 Connection Details:"
echo "  📝 MongoDB URI: **********************************************************************************************************"
echo "  🔑 Redis URI: redis://localhost:6379"
echo ""
echo "✅ Your backend application can now connect to MongoDB with transaction support!"
