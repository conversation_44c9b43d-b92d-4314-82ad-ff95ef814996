#!/bin/bash

# Test Simple Mongo Express Configuration
set -e

echo "🧪 Testing Simple Mongo Express Configuration..."

# Stop existing services
echo "📋 Stopping existing services..."
docker-compose -f docker-compose.mongo.yml down 2>/dev/null || true

# Start simple configuration
echo "🚀 Starting simple configuration..."
docker-compose -f docker-compose.mongo-simple.yml up -d

# Wait for services
echo "⏳ Waiting for services to start..."
sleep 60

# Check status
echo "📊 Checking service status..."
docker-compose -f docker-compose.mongo-simple.yml ps

# Test MongoDB
echo "🔍 Testing MongoDB..."
for i in {1..5}; do
    if docker exec cryptoyield-mongodb mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
        echo "✅ MongoDB is accessible"
        break
    else
        echo "⏳ Attempt $i/5: Waiting for MongoDB..."
        sleep 10
    fi
done

# Initialize replica set
echo "🔧 Initializing replica set..."
docker exec cryptoyield-mongodb mongosh --eval "
try {
    const status = rs.status();
    if (status.ok === 1) {
        print('✅ Replica set already active');
    }
} catch (e) {
    if (e.message.includes('no replset config')) {
        rs.initiate({
            _id: 'rs0',
            members: [{ _id: 0, host: 'mongodb:27017' }]
        });
        print('✅ Replica set initialized');
    } else {
        print('⚠️ ' + e.message);
    }
}
" 2>/dev/null || echo "⚠️ Replica set initialization may need more time"

# Wait for replica set to stabilize
echo "⏳ Waiting for replica set to stabilize..."
sleep 30

# Test Mongo Express
echo "🌐 Testing Mongo Express..."
for i in {1..10}; do
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 2>/dev/null || echo "000")
    if [[ "$response" == "200" || "$response" == "401" ]]; then
        echo "✅ Mongo Express is accessible at http://localhost:8081"
        echo "🔑 Login: admin / admin123"
        break
    else
        echo "⏳ Attempt $i/10: Waiting for Mongo Express... (HTTP $response)"
        sleep 15
    fi
done

# Final status
echo ""
echo "🎉 Test completed!"
echo ""
echo "📋 Service Information:"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🔗 MongoDB Connection String:"
echo "  **********************************************************************************************************"
echo ""

# Show final status
echo "📊 Final container status:"
docker-compose -f docker-compose.mongo-simple.yml ps

# Test transaction capability
echo ""
echo "🧪 Testing transaction capability..."
docker exec cryptoyield-mongodb mongosh --eval "
try {
    const session = db.getMongo().startSession();
    session.startTransaction();
    
    const testDb = session.getDatabase('cryptoyield_test');
    testDb.transactionTest.insertOne({
        test: 'simple_transaction',
        timestamp: new Date()
    }, {session: session});
    
    session.commitTransaction();
    session.endSession();
    
    print('✅ Transaction test successful');
} catch (e) {
    print('❌ Transaction test failed: ' + e.message);
}
" 2>/dev/null || echo "⚠️ Transaction test may need more time"

echo ""
echo "✅ Setup is ready! You can now run your backend with: cd backend && npm run dev:docker"
