#!/usr/bin/env node

/**
 * Manual Daily Interest Calculation Command
 * 
 * Chạy daily interest calculation bằng tay khi có lỗi cronjob
 * 
 * Usage:
 *   node scripts/manual-daily-interest.js
 *   npm run manual-interest
 */

// Load environment variables
require('dotenv').config({ path: require('path').join(__dirname, '../.env.docker') });

const mongoose = require('mongoose');

// Import models
require('../src/models/userModel');
require('../src/models/walletModel');
require('../src/models/investmentPackageModel');
require('../src/models/transactionModel');
require('../src/models/paymentHistoryModel');
require('../src/models/auditTrailModel');

// Import service
const interestCalculationService = require('../src/services/interestCalculationService').default;

// Models
const InvestmentPackage = require('../src/models/investmentPackageModel').default;

/**
 * Connect to database
 */
async function connectDatabase() {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log('🔌 Connecting to database...');
    console.log(`   URI: ${mongoUri.replace(/\/\/.*@/, '//***:***@')}`); // Hide credentials
    
    await mongoose.connect(mongoUri);
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

/**
 * Check system status before running
 */
async function checkSystemStatus() {
  try {
    console.log('\n🔍 Checking system status...');
    
    // Check active packages
    const activePackages = await InvestmentPackage.countDocuments({
      status: 'active',
      activatedAt: { $ne: null }
    });
    
    // Check packages that need interest calculation
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const packagesNeedingCalculation = await InvestmentPackage.countDocuments({
      status: 'active',
      activatedAt: { $ne: null },
      $or: [
        { lastCalculatedAt: null },
        { lastCalculatedAt: { $lt: yesterday } }
      ]
    });
    
    console.log(`📊 System Status:`);
    console.log(`   Active packages: ${activePackages}`);
    console.log(`   Packages needing calculation: ${packagesNeedingCalculation}`);
    
    if (activePackages === 0) {
      console.log('⚠️  No active packages found. Nothing to calculate.');
      return false;
    }
    
    if (packagesNeedingCalculation === 0) {
      console.log('ℹ️  All packages are up to date. No calculation needed.');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error checking system status:', error);
    throw error;
  }
}

/**
 * Run manual daily interest calculation
 */
async function runManualInterestCalculation() {
  try {
    console.log('\n🚀 Starting manual daily interest calculation...');
    console.log(`⏰ Timestamp: ${new Date().toISOString()}`);
    
    const startTime = Date.now();
    
    // Run the interest calculation
    const summary = await interestCalculationService.processAllActivePackages();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Display detailed results
    console.log('\n📊 MANUAL INTEREST CALCULATION RESULTS');
    console.log('======================================');
    console.log(`📦 Total packages processed: ${summary.totalPackages}`);
    console.log(`✅ Successful calculations: ${summary.successfulCalculations}`);
    console.log(`❌ Failed calculations: ${summary.failedCalculations}`);
    console.log(`💰 Total interest paid: ${summary.totalInterestPaid.toFixed(6)} USDT`);
    console.log(`⏱️  Processing duration: ${duration}ms (${(duration/1000).toFixed(2)}s)`);
    console.log(`🕐 Completed at: ${summary.timestamp.toISOString()}`);
    
    // Calculate success rate
    if (summary.totalPackages > 0) {
      const successRate = (summary.successfulCalculations / summary.totalPackages * 100).toFixed(2);
      console.log(`📈 Success rate: ${successRate}%`);
      
      if (summary.successfulCalculations > 0) {
        const avgInterestPerPackage = (summary.totalInterestPaid / summary.successfulCalculations).toFixed(6);
        console.log(`💵 Average interest per package: ${avgInterestPerPackage} USDT`);
      }
    }
    
    // Show errors if any
    if (summary.errors && summary.errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:');
      summary.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. Package ${error.packageId}:`);
        console.log(`     Error: ${error.error}`);
        if (error.details) {
          console.log(`     Details: ${error.details}`);
        }
      });
    }
    
    // Show warnings if any
    if (summary.warnings && summary.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      summary.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
    }
    
    // Final status
    if (summary.failedCalculations === 0) {
      console.log('\n🎉 Manual interest calculation completed successfully!');
      console.log('✅ All packages processed without errors.');
    } else {
      console.log('\n⚠️  Manual interest calculation completed with errors.');
      console.log(`❌ ${summary.failedCalculations} packages failed to process.`);
      console.log('   Check the error details above and fix the issues.');
    }
    
    return summary;
    
  } catch (error) {
    console.error('\n💥 Manual interest calculation failed:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('💰 MANUAL DAILY INTEREST CALCULATION');
  console.log('====================================');
  console.log('🔧 Running manual interest calculation for all active packages...');
  
  try {
    // Connect to database
    await connectDatabase();
    
    // Check system status
    const shouldProceed = await checkSystemStatus();
    
    if (!shouldProceed) {
      console.log('\n✅ No action needed. Exiting...');
      return;
    }
    
    // Ask for confirmation (in production, you might want to skip this)
    console.log('\n⚠️  This will calculate and distribute interest for all eligible packages.');
    console.log('   Make sure this is what you want to do.');
    
    // Run the calculation
    const summary = await runManualInterestCalculation();
    
    // Final summary
    console.log('\n📋 FINAL SUMMARY:');
    console.log(`   Packages processed: ${summary.totalPackages}`);
    console.log(`   Interest distributed: ${summary.totalInterestPaid.toFixed(6)} USDT`);
    console.log(`   Success rate: ${summary.totalPackages > 0 ? (summary.successfulCalculations / summary.totalPackages * 100).toFixed(2) : 0}%`);
    
    if (summary.failedCalculations > 0) {
      console.log('\n🚨 ATTENTION: Some packages failed to process!');
      console.log('   Please check the error logs and fix the issues.');
      console.log('   You may need to run this command again after fixing the problems.');
      process.exit(1);
    } else {
      console.log('\n🎉 All done! Interest calculation completed successfully.');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('\n💥 Fatal error during manual interest calculation:', error);
    console.error('   Please check the error details and try again.');
    process.exit(1);
  } finally {
    // Close database connection
    try {
      if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
        console.log('✅ Database connection closed');
      }
    } catch (error) {
      console.error('❌ Error closing database connection:', error);
    }
  }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Received interrupt signal. Shutting down gracefully...');
  try {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('✅ Database connection closed');
    }
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('\n\n⚠️  Received termination signal. Shutting down gracefully...');
  try {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('✅ Database connection closed');
    }
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Execute main function
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { main, runManualInterestCalculation };
