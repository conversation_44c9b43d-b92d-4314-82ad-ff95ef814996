@echo off
setlocal enabledelayedexpansion

REM Fix Mongo Express Configuration Script (Windows)
REM This script fixes and tests Mongo Express connectivity

echo 🔧 Fixing Mongo Express Configuration...

REM Step 1: Stop all services
echo 📋 Stopping all services...
docker-compose -f docker-compose.mongo.yml down

REM Step 2: Remove any problematic containers
echo 🗑️ Removing existing containers...
docker rm -f cryptoyield-mongo-express 2>nul
docker rm -f cryptoyield-mongodb 2>nul
docker rm -f cryptoyield-redis 2>nul

REM Step 3: Pull latest images
echo 📥 Pulling latest images...
docker-compose -f docker-compose.mongo.yml pull

REM Step 4: Start MongoDB first
echo 🗄️ Starting MongoDB...
docker-compose -f docker-compose.mongo.yml up -d mongodb

REM Step 5: Wait for MongoDB to be healthy
echo ⏳ Waiting for MongoDB to be healthy...
set timeout=180
set elapsed=0
set interval=10

:wait_mongodb
if !elapsed! GEQ !timeout! (
    echo ❌ ERROR: MongoDB failed to become healthy within !timeout! seconds
    echo 📋 Checking MongoDB logs...
    docker logs cryptoyield-mongodb --tail 20
    exit /b 1
)

docker-compose -f docker-compose.mongo.yml ps mongodb | findstr "healthy" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ MongoDB is healthy
    goto mongodb_ready
)

echo ⏳ Waiting for MongoDB... (!elapsed!/!timeout! seconds)
timeout /t !interval! /nobreak >nul
set /a elapsed=!elapsed!+!interval!
goto wait_mongodb

:mongodb_ready

REM Step 6: Test MongoDB connection
echo 🔍 Testing MongoDB connection...
docker exec cryptoyield-mongodb mongosh --quiet --eval "try { db.adminCommand('ping'); print('✅ MongoDB connection successful'); const status = rs.status(); if (status.ok === 1) { print('✅ Replica set is active: ' + status.set); } } catch (e) { print('❌ Error: ' + e.message); exit(1); }"

REM Step 7: Start Redis
echo 🔴 Starting Redis...
docker-compose -f docker-compose.mongo.yml up -d redis

REM Step 8: Start Mongo Express
echo 🌐 Starting Mongo Express...
docker-compose -f docker-compose.mongo.yml up -d mongo-express

REM Step 9: Wait for Mongo Express to be ready
echo ⏳ Waiting for Mongo Express to start...
timeout /t 30 /nobreak >nul

REM Step 10: Check Mongo Express logs
echo 📋 Checking Mongo Express logs...
docker logs cryptoyield-mongo-express --tail 20

REM Step 11: Test Mongo Express connectivity
echo 🔍 Testing Mongo Express connectivity...

REM Test if the container is running
docker ps | findstr "cryptoyield-mongo-express" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Mongo Express container is running
) else (
    echo ❌ ERROR: Mongo Express container is not running
    exit /b 1
)

REM Test if port 8081 is accessible
timeout /t 10 /nobreak >nul
curl -s -o nul -w "%%{http_code}" http://localhost:8081 | findstr "200 401" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Mongo Express is accessible at http://localhost:8081
    echo 🔑 Login credentials: admin / admin123
) else (
    echo ⚠️ Mongo Express may not be ready yet. Checking status...
    
    REM Show recent logs
    echo 📋 Recent Mongo Express logs:
    docker logs cryptoyield-mongo-express --tail 10
)

REM Step 12: Display service status
echo 📊 Final service status:
docker-compose -f docker-compose.mongo.yml ps

REM Step 13: Display connection information
echo.
echo ✅ Mongo Express setup completed!
echo.
echo 📋 Service Information:
echo   🗄️  MongoDB: localhost:27017
echo   🔴 Redis: localhost:6379
echo   🌐 Mongo Express: http://localhost:8081
echo.
echo 🔑 Mongo Express Login:
echo   Username: admin
echo   Password: admin123
echo.
echo 🔗 MongoDB Connection Details:
echo   Host: localhost:27017
echo   Username: cryptoyield_admin
echo   Password: secure_password123
echo   Database: cryptoyield
echo   Auth Database: admin
echo   Replica Set: rs0
echo.

REM Step 14: Create test connection script
echo 🧪 Creating MongoDB connection test...
(
echo const { MongoClient } = require('mongodb'^);
echo.
echo const uri = '*******************************************************************************************^&replicaSet=rs0';
echo.
echo async function testConnection(^) {
echo     try {
echo         const client = new MongoClient(uri^);
echo         await client.connect(^);
echo         console.log('✅ Backend can connect to MongoDB successfully'^);
echo         
echo         // Test transaction capability
echo         const session = client.startSession(^);
echo         await session.withTransaction(async (^) =^> {
echo             const db = client.db('cryptoyield'^);
echo             await db.collection('test'^).insertOne({ test: 'transaction', timestamp: new Date(^) }, { session }^);
echo         }^);
echo         console.log('✅ Transaction support is working'^);
echo         
echo         await client.close(^);
echo     } catch (error^) {
echo         console.error('❌ Backend connection failed:', error.message^);
echo     }
echo }
echo.
echo testConnection(^);
) > test-mongo-connection.js

REM Test backend connectivity if Node.js is available
where node >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 🔍 Testing backend connectivity...
    if exist "backend\package.json" (
        cd backend
        node ..\test-mongo-connection.js
        cd ..
    ) else (
        node test-mongo-connection.js
    )
    del test-mongo-connection.js
) else (
    echo ⚠️ Node.js not found. Skipping backend connection test.
    del test-mongo-connection.js
)

echo.
echo ✅ Setup complete! You can now:
echo   1. Access Mongo Express at: http://localhost:8081
echo   2. Run your backend with: cd backend ^&^& npm run dev:docker
echo   3. View MongoDB data through the web interface
echo.
echo 📝 If Mongo Express is still not accessible, wait 1-2 minutes and try again.

pause
