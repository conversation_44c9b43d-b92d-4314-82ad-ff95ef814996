# Dependencies
node_modules/
/.pnp
.pnp.js

# Build outputs
/frontend/dist/
/frontend/build/
/backend/dist/
/backend/build/

# Environment variables
.env
.env.local
.env.dev
.env.docker
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/dist
/build
/frontend/public/images/optimized
/backend/uploads

# Docker data volumes
/data/mongodb/
/data/mongodb-config/
/data/redis/

# Test files and temporary scripts
test-*.js
test-*.html
test-*.md
*-test.js
*-test.bat
*-test.sh

# Temporary automation files
CryptoBasriko*
*Otomasyon*
*.spec

# Old setup scripts
run-*.sh
run-*.bat
init-*.sh
init-*.bat
setup-*.old
verify-*.sh

# Coverage reports
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
.cache/

# Documentation and README files (development)
*-FEATURE.md
*-DOCUMENTATION.md
*-SETUP.md
*-SOLUTION.md
*-IMPLEMENTATION*.md
*-SYSTEM*.md
MANUAL-*.md

# Development and test scripts
dev-*.sh
dev-*.bat
setup-*.sh
setup-*.bat
fix-*.sh
fix-*.bat
test-*.sh
test-*.bat
quick-*.sh
quick-*.bat
verify-*.sh
verify-*.bat
restart-*.sh
start-*.sh
final-*.sh

# Monitoring and infrastructure
/alertmanager/
/grafana/
/prometheus/
prometheus.yml
/mongodb-keyfile/

# Build and compilation artifacts
*.tsbuildinfo
jest.config.*
build.js
dev.js

# Backup and old files
*.backup
*.bak
*.old
Profile.*.tsx
*.tsx.backup

# Test directories and files
/__tests__/
/tests/
/src/__tests__/
/src/tests/
/test/
*Test.tsx
*Test.ts
*test.js
*test.ts
test-*.tsx
test-*.ts

# Mock services
mock*.ts
mock*.js

# Scripts and utilities (development)
/scripts/test-*
/scripts/check-*
/scripts/debug-*
/scripts/phase*
/scripts/reports/

# Example and demo files
*.example
/examples/
/demo/
converter.html

# Stack dumps and crash reports
*.stackdump

# AI Guidelines and Memory (ALWAYS TRACK)
!.ai-guidelines.md
!.ai-memory.json
