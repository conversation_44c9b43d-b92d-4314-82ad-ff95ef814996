#!/bin/bash

# Quick Fix for Mongo Express - Simplified approach
set -e

echo "🔧 Quick Fix for Mongo Express..."

# Stop all services
echo "📋 Stopping all services..."
docker-compose -f docker-compose.mongo.yml down

# Remove problematic containers and volumes
echo "🗑️ Cleaning up..."
docker rm -f cryptoyield-mongo-express cryptoyield-mongodb cryptoyield-redis 2>/dev/null || true
docker volume prune -f

# Start MongoDB first with simplified approach
echo "🗄️ Starting MongoDB..."
docker-compose -f docker-compose.mongo.yml up -d mongodb

# Wait for MongoDB to start (not necessarily healthy)
echo "⏳ Waiting for MongoDB to start..."
sleep 30

# Check if MongoDB is running
if docker ps | grep -q "cryptoyield-mongodb"; then
    echo "✅ MongoDB container is running"
    
    # Try to initialize replica set manually
    echo "🔧 Initializing replica set..."
    docker exec cryptoyield-mongodb mongosh --eval "
    try {
        rs.initiate({
            _id: 'rs0',
            members: [{ _id: 0, host: 'mongodb:27017' }]
        });
        print('✅ Replica set initialized');
    } catch (e) {
        if (e.message.includes('already initialized')) {
            print('✅ Replica set already initialized');
        } else {
            print('⚠️ Replica set init: ' + e.message);
        }
    }
    " 2>/dev/null || echo "⚠️ Replica set initialization may need more time"
    
    # Wait a bit more for replica set to stabilize
    sleep 20
    
    # Start Redis
    echo "🔴 Starting Redis..."
    docker-compose -f docker-compose.mongo.yml up -d redis
    
    # Start Mongo Express
    echo "🌐 Starting Mongo Express..."
    docker-compose -f docker-compose.mongo.yml up -d mongo-express
    
    # Wait for Mongo Express
    echo "⏳ Waiting for Mongo Express..."
    sleep 30
    
    # Test connectivity
    echo "🔍 Testing connectivity..."
    
    # Check if containers are running
    echo "📊 Container status:"
    docker-compose -f docker-compose.mongo.yml ps
    
    # Test Mongo Express
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 | grep -q "200\|401"; then
        echo "✅ Mongo Express is accessible at http://localhost:8081"
        echo "🔑 Login: admin / admin123"
    else
        echo "⚠️ Mongo Express may still be starting. Check logs:"
        docker logs cryptoyield-mongo-express --tail 10
    fi
    
    # Test MongoDB
    if docker exec cryptoyield-mongodb mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
        echo "✅ MongoDB is accessible"
    else
        echo "⚠️ MongoDB may still be starting"
    fi
    
else
    echo "❌ MongoDB failed to start. Check logs:"
    docker logs cryptoyield-mongodb --tail 20
    exit 1
fi

echo ""
echo "🎉 Setup completed!"
echo "📋 Access Information:"
echo "  🌐 Mongo Express: http://localhost:8081 (admin/admin123)"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "💡 If Mongo Express shows connection errors, wait 2-3 minutes for MongoDB replica set to fully initialize."
