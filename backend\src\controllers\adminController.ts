import { Request, Response } from 'express';
import mongoose from 'mongoose';
import User from '../models/userModel';
import Transaction from '../models/transactionModel';
import Investment from '../models/investmentModel';
import InvestmentPackage from '../models/investmentPackageModel';
import Wallet from '../models/walletModel';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import jwt from 'jsonwebtoken';

// Generate JWT token for admin
const generateToken = (id: string) => {
  const secret = process.env.JWT_SECRET || 'fallback_secret';
  const expiresIn = process.env.JWT_ADMIN_EXPIRES_IN || '1d';

  return jwt.sign({ id }, secret, {
    expiresIn,
    algorithm: 'HS512'
  } as jwt.SignOptions);
};

// ===== ADMIN AUTHENTICATION =====

// @desc    Check admin status
// @route   GET /api/admin/check
// @access  Admin
export const checkAdminStatus = catchAsync(async (req: Request, res: Response) => {
  // Log the request for debugging
  console.log('Admin check request received:', {
    url: req.originalUrl,
    headers: req.headers,
    cookies: req.cookies,
    user: req.user ? {
      id: req.user._id,
      email: req.user.email,
      isAdmin: req.user.isAdmin
    } : 'No user'
  });

  // If middleware passes, user is admin
  // Ensure the admin cookie is set
  res.cookie('adminToken', 'true', {
    httpOnly: false, // Allow JavaScript access for UI state
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    path: '/' // Available across the entire site
  });

  // Send the response
  res.status(200).json({
    status: 'success',
    isAdmin: true,
    user: {
      _id: req.user._id,
      email: req.user.email,
      firstName: req.user.firstName,
      lastName: req.user.lastName
    },
    message: 'Admin status confirmed'
  });
});

// ===== USER MANAGEMENT =====

// @desc    Get all users (admin only)
// @route   GET /api/admin/users
// @access  Admin
export const getUsers = catchAsync(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;

  // Build query
  const query: any = {};

  // Add search functionality
  if (search) {
    query.$or = [
      { email: { $regex: search, $options: 'i' } },
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
      { walletAddress: { $regex: search, $options: 'i' } }
    ];
  }

  // Execute query with pagination
  const [users, total] = await Promise.all([
    User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit),
    User.countDocuments(query)
  ]);

  res.json({
    users,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

// @desc    Get user by ID (admin only)
// @route   GET /api/admin/users/:id
// @access  Admin
export const getUserById = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.params.id).select('-password');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json(user);
});

// @desc    Update user (admin only)
// @route   PUT /api/admin/users/:id
// @access  Admin
export const updateUser = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Update user fields
  if (req.body.firstName) user.firstName = req.body.firstName;
  if (req.body.lastName) user.lastName = req.body.lastName;
  if (req.body.email) user.email = req.body.email.toLowerCase();
  if (req.body.isAdmin !== undefined) user.isAdmin = Boolean(req.body.isAdmin);
  if (req.body.kycVerified !== undefined) user.kycVerified = Boolean(req.body.kycVerified);

  const updatedUser = await user.save();

  logger.info(`Admin ${req.user._id} updated user ${user._id}`);

  res.json({
    _id: updatedUser._id,
    firstName: updatedUser.firstName,
    lastName: updatedUser.lastName,
    email: updatedUser.email,
    isAdmin: updatedUser.isAdmin,
    kycVerified: updatedUser.kycVerified,
    message: 'User updated successfully'
  });
});

// @desc    Delete user (admin only)
// @route   DELETE /api/admin/users/:id
// @access  Admin
export const deleteUser = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Prevent deleting yourself
  if (user._id.toString() === req.user._id.toString()) {
    throw new AppError('You cannot delete your own account', 400);
  }

  await user.deleteOne();

  logger.info(`Admin ${req.user._id} deleted user ${user._id}`);

  res.json({ message: 'User deleted successfully' });
});

// @desc    Toggle admin status (admin only)
// @route   PUT /api/admin/users/:id/toggle-admin
// @access  Admin
export const toggleAdminStatus = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Prevent changing your own admin status
  if (user._id.toString() === req.user._id.toString()) {
    throw new AppError('You cannot change your own admin status', 400);
  }

  user.isAdmin = !user.isAdmin;
  await user.save();

  logger.info(`Admin ${req.user._id} toggled admin status for user ${user._id} to ${user.isAdmin}`);

  res.json({
    _id: user._id,
    isAdmin: user.isAdmin,
    message: `User is ${user.isAdmin ? 'now an admin' : 'no longer an admin'}`
  });
});

// @desc    Login as user (admin only)
// @route   POST /api/admin/users/:id/login-as
// @access  Admin
export const loginAsUser = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Prevent logging in as yourself
  if (user._id.toString() === req.user._id.toString()) {
    throw new AppError('You are already logged in as this user', 400);
  }

  // Prevent logging in as another admin (security measure)
  if (user.isAdmin) {
    throw new AppError('Cannot impersonate another admin user', 403);
  }

  // Get client IP and user agent for audit trail
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] as string || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';

  // Create audit trail entry
  try {
    const AuditTrail = (await import('../models/auditTrailModel')).default;
    await AuditTrail.createAuditLog({
      userId: req.user._id,
      action: 'ADMIN_LOGIN_AS_USER',
      ipAddress: clientIP,
      userAgent: userAgent,
      details: {
        adminId: req.user._id,
        adminEmail: req.user.email,
        targetUserId: user._id,
        targetUserEmail: user.email,
        targetUserName: `${user.firstName} ${user.lastName}`,
        timestamp: new Date(),
        sessionType: 'impersonation'
      },
      status: 'success'
    });
  } catch (auditError) {
    logger.error('Failed to create audit trail for admin login as user:', auditError);
    // Continue with login process even if audit fails
  }

  // Log the action with detailed information
  logger.info(`Admin impersonation started`, {
    adminId: req.user._id,
    adminEmail: req.user.email,
    targetUserId: user._id,
    targetUserEmail: user.email,
    targetUserName: `${user.firstName} ${user.lastName}`,
    clientIP,
    userAgent,
    timestamp: new Date().toISOString()
  });

  // Generate token for the user
  const token = generateToken(user._id?.toString() || '');

  // Store original admin session info in a separate cookie for return functionality
  const adminSessionData = {
    adminId: req.user._id,
    adminEmail: req.user.email,
    adminFirstName: req.user.firstName,
    adminLastName: req.user.lastName,
    impersonationStartTime: new Date().toISOString(),
    originalToken: req.cookies.token // Store original admin token
  };

  // Set impersonation session cookie
  res.cookie('impersonationSession', JSON.stringify(adminSessionData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours (shorter than normal session)
    path: '/'
  });

  // Set user token in HTTP-only cookie
  res.cookie('token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours for impersonation sessions
    path: '/'
  });

  // Set impersonation indicator cookie (accessible by JavaScript for UI)
  res.cookie('isImpersonating', 'true', {
    httpOnly: false, // Allow JavaScript access for UI state
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 24 * 60 * 60 * 1000,
    path: '/'
  });

  // Clear admin cookie
  res.clearCookie('adminToken', {
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
  });

  res.json({
    status: 'success',
    data: {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isAdmin: user.isAdmin,
      isImpersonating: true,
      impersonatedBy: {
        adminId: req.user._id,
        adminEmail: req.user.email,
        adminName: `${req.user.firstName} ${req.user.lastName}`
      }
    },
    message: `You are now logged in as ${user.firstName} ${user.lastName}`
  });
});

// @desc    Return to admin session from impersonation
// @route   POST /api/admin/return-to-admin
// @access  Impersonating Admin
export const returnToAdmin = catchAsync(async (req: Request, res: Response) => {
  // Check if there's an impersonation session
  const impersonationSession = req.cookies.impersonationSession;

  if (!impersonationSession) {
    throw new AppError('No impersonation session found', 400);
  }

  let adminSessionData;
  try {
    adminSessionData = JSON.parse(impersonationSession);
  } catch (error) {
    throw new AppError('Invalid impersonation session data', 400);
  }

  // Verify admin user still exists
  const admin = await User.findById(adminSessionData.adminId);
  if (!admin || !admin.isAdmin) {
    throw new AppError('Original admin user not found or no longer admin', 403);
  }

  // Get client IP and user agent for audit trail
  const clientIP = req.ip || req.headers['x-forwarded-for'] as string || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';

  // Create audit trail entry for returning to admin
  try {
    const AuditTrail = (await import('../models/auditTrailModel')).default;
    await AuditTrail.createAuditLog({
      userId: adminSessionData.adminId,
      action: 'ADMIN_RETURN_TO_ADMIN',
      ipAddress: clientIP,
      userAgent: userAgent,
      details: {
        adminId: adminSessionData.adminId,
        adminEmail: adminSessionData.adminEmail,
        impersonationDuration: new Date().getTime() - new Date(adminSessionData.impersonationStartTime).getTime(),
        returnTimestamp: new Date(),
        sessionType: 'return_from_impersonation'
      },
      status: 'success'
    });
  } catch (auditError) {
    logger.error('Failed to create audit trail for return to admin:', auditError);
  }

  // Log the return action
  logger.info(`Admin returned from impersonation`, {
    adminId: adminSessionData.adminId,
    adminEmail: adminSessionData.adminEmail,
    impersonationDuration: new Date().getTime() - new Date(adminSessionData.impersonationStartTime).getTime(),
    clientIP,
    userAgent,
    timestamp: new Date().toISOString()
  });

  // Generate new admin token
  const adminToken = generateToken(adminSessionData.adminId);

  // Set admin token
  res.cookie('token', adminToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    path: '/'
  });

  // Set admin cookie
  res.cookie('adminToken', 'true', {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 30 * 24 * 60 * 60 * 1000,
    path: '/'
  });

  // Clear impersonation cookies
  res.clearCookie('impersonationSession', {
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
  });

  res.clearCookie('isImpersonating', {
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
  });

  res.json({
    status: 'success',
    data: {
      _id: admin._id,
      email: admin.email,
      firstName: admin.firstName,
      lastName: admin.lastName,
      isAdmin: admin.isAdmin,
      isImpersonating: false
    },
    message: `Welcome back, ${admin.firstName} ${admin.lastName}`
  });
});

// ===== TRANSACTION MANAGEMENT =====

// @desc    Get transaction by ID (admin only)
// @route   GET /api/admin/transactions/:id
// @access  Admin
export const getTransactionById = catchAsync(async (req: Request, res: Response): Promise<any> => {
  const { id } = req.params;

  logger.info(`Admin ${req.user._id} requesting transaction details for ID: ${id}`);

  // Find the transaction with populated user data
  const transaction = await Transaction.findById(id)
    .populate({
      path: 'userId',
      select: 'firstName lastName email phoneNumber country city',
      model: 'User'
    });

  if (!transaction) {
    return res.status(404).json({
      status: 'error',
      message: 'Transaction not found'
    });
  }

  // Get investment data if transaction has investmentId
  let investmentData = null;
  if (transaction.investmentId) {
    try {
      investmentData = await Investment.findById(transaction.investmentId);
    } catch (error) {
      logger.warn(`Could not fetch investment data for transaction ${id}:`, error);
    }
  }

  // Format user data
  const userData = transaction.userId as any;
  const isUserPopulated = userData && typeof userData !== 'string' && userData !== null && userData._id;

  // Format response data
  const transactionDetail = {
    _id: transaction._id,
    type: transaction.type,
    amount: transaction.amount,
    asset: transaction.asset,
    status: transaction.status,
    txHash: transaction.txHash,
    walletAddress: transaction.walletAddress,
    blockchainNetwork: transaction.blockchainNetwork,
    description: transaction.description,
    createdAt: transaction.createdAt,
    updatedAt: transaction.updatedAt,

    // User information
    user: isUserPopulated ? {
      _id: userData._id,
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      phoneNumber: userData.phoneNumber,
      country: userData.country,
      city: userData.city,
      fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim()
    } : null,

    // Investment information (if available)
    investment: investmentData ? {
      _id: investmentData._id,
      amount: investmentData.amount,
      currency: investmentData.currency,
      status: investmentData.status,
      network: investmentData.network,
      cryptoAddress: investmentData.cryptoAddress,
      receiptUrl: investmentData.receiptUrl,
      adminNotes: investmentData.adminNotes,
      createdAt: investmentData.createdAt,
      approvedAt: investmentData.approvedAt,
      rejectedAt: investmentData.rejectedAt,
      rejectionReason: investmentData.rejectionReason
    } : null,

    // Transaction metadata
    metadata: transaction.metadata,

    // Amount correction information
    originalAmount: transaction.originalAmount,
    adminVerifiedAmount: transaction.adminVerifiedAmount,
    amountCorrectionReason: transaction.amountCorrectionReason,
    amountModifiedBy: transaction.amountModifiedBy,
    amountModifiedAt: transaction.amountModifiedAt,

    // Investment relationship
    investmentId: transaction.investmentId,
    hasInvestment: !!investmentData
  };

  logger.info(`Transaction details retrieved for ID: ${id}, type: ${transaction.type}, status: ${transaction.status}`);

  return res.status(200).json({
    status: 'success',
    data: {
      transaction: transactionDetail
    }
  });
});

// ===== DEPOSIT MANAGEMENT =====

// @desc    Update deposit amount (admin verification)
// @route   PUT /api/admin/deposits/:id/amount
// @access  Admin
export const updateDepositAmount = catchAsync(async (req: Request, res: Response): Promise<any> => {
  const { id } = req.params;
  const { adminVerifiedAmount, amountCorrectionReason } = req.body;

  // Validate request body
  if (!adminVerifiedAmount || typeof adminVerifiedAmount !== 'number' || adminVerifiedAmount <= 0) {
    return res.status(400).json({
      status: 'error',
      message: 'Valid admin verified amount is required'
    });
  }

  // Find the deposit
  const deposit = await Transaction.findById(id);
  if (!deposit) {
    return res.status(404).json({
      status: 'error',
      message: 'Deposit not found'
    });
  }

  // Ensure it's a deposit transaction
  if (deposit.type !== 'deposit') {
    return res.status(400).json({
      status: 'error',
      message: 'Transaction is not a deposit'
    });
  }

  // Validate that amount modifications are only allowed for pending or processing deposits
  if (!['pending', 'processing'].includes(deposit.status)) {
    return res.status(400).json({
      status: 'error',
      message: `Cannot modify amount for deposits with '${deposit.status}' status. Amount editing is only allowed for deposits with 'pending' or 'processing' status.`,
      data: {
        currentStatus: deposit.status,
        allowedStatuses: ['pending', 'processing'],
        depositId: deposit._id
      }
    });
  }

  // Store original amount if not already stored
  if (!deposit.originalAmount) {
    deposit.originalAmount = deposit.amount;
  }

  // Update with admin verified amount
  deposit.adminVerifiedAmount = adminVerifiedAmount;
  deposit.amount = adminVerifiedAmount; // Use verified amount as the effective amount
  deposit.amountModifiedBy = req.user?.id;
  deposit.amountModifiedAt = new Date();

  if (amountCorrectionReason) {
    deposit.amountCorrectionReason = amountCorrectionReason;
  }

  // Update related investment if it exists
  if (deposit.investmentId) {
    const investment = await Investment.findById(deposit.investmentId);
    if (investment) {
      investment.amount = adminVerifiedAmount;
      investment.adminVerifiedAmount = adminVerifiedAmount;
      investment.amountModifiedBy = req.user?.id;
      investment.amountModifiedAt = new Date();
      investment.amountCorrectionReason = amountCorrectionReason;
      investment.originalAmount = deposit.originalAmount;
      await investment.save();
      logger.info(`Investment amount updated to match deposit: ${investment._id} - ${adminVerifiedAmount}`);
    }
  }

  // Save the updated deposit
  await deposit.save();

  logger.info(`Deposit amount updated by admin ${req.user?.id}: ${deposit.originalAmount} → ${adminVerifiedAmount} for deposit ${deposit._id}`);

  // Automatically create investment package if deposit is approved and doesn't have one
  let investmentPackage = null;

  logger.info(`Checking automatic investment package creation conditions:`, {
    depositId: deposit._id,
    status: deposit.status,
    userId: deposit.userId,
    investmentId: deposit.investmentId,
    adminVerifiedAmount: adminVerifiedAmount,
    asset: deposit.asset
  });

  if (deposit.status === 'approved' && !deposit.investmentId && adminVerifiedAmount >= 0.000001) {
    if (!deposit.userId) {
      logger.warn(`Cannot create investment package: deposit ${deposit._id} has no userId`);
    } else {
      try {
        // Import the investment package model and create package directly
        const InvestmentPackage = require('../models/investmentPackageModel').default;
        const crypto = require('crypto');

        // Generate unique package ID and hash
        const packageId = `PKG-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        const packageHash = crypto.createHash('sha256').update(packageId + deposit._id + Date.now()).digest('hex');

        logger.info(`Creating investment package for deposit ${deposit._id} with amount ${adminVerifiedAmount}`);

        // Create investment package using admin-verified amount
        investmentPackage = new InvestmentPackage({
          userId: deposit.userId,
          transactionId: deposit._id, // Link to the deposit transaction
          packageId: packageId,
          amount: adminVerifiedAmount, // Use admin-verified amount
          currency: deposit.asset.toUpperCase(),
          status: 'pending',
          packageHash: packageHash,
          autoCreated: true,
          depositAmount: adminVerifiedAmount,
          depositCurrency: deposit.asset.toUpperCase(),
          originalUSDTValue: adminVerifiedAmount, // Assume 1:1 for now
          compoundEnabled: false
        });

        // Activate the package (sets activation time to next 03:00)
        await investmentPackage.activate();
        await investmentPackage.save();

        logger.info(`Investment package auto-created from admin-approved deposit: ${investmentPackage._id}`);
      } catch (packageError) {
        logger.error('Error creating investment package from admin-approved deposit:', packageError);
        // Continue without failing the deposit update
      }
    }
  } else {
    logger.info(`Investment package creation skipped:`, {
      statusApproved: deposit.status === 'approved',
      noExistingInvestment: !deposit.investmentId,
      amountSufficient: adminVerifiedAmount >= 0.000001
    });
  }

  // Emit WebSocket event for real-time updates
  try {
    const { notificationService } = require('../services/notificationService');
    await notificationService.notifyDepositAmountUpdate({
      id: deposit._id.toString(),
      userId: deposit.userId.toString(),
      originalAmount: deposit.originalAmount,
      adminVerifiedAmount: adminVerifiedAmount,
      amountCorrectionReason: amountCorrectionReason || '',
      modifiedBy: req.user?.id,
      modifiedAt: new Date(),
      currency: deposit.asset,
      investmentPackageCreated: !!investmentPackage,
      investmentPackageId: investmentPackage?._id?.toString(),
      investmentPackageAmount: investmentPackage?.amount,
      investmentPackageCurrency: investmentPackage?.currency
    });
  } catch (error) {
    logger.error('Error emitting deposit amount update notification:', error);
  }

  res.status(200).json({
    status: 'success',
    message: 'Deposit amount updated successfully',
    data: {
      deposit: {
        id: deposit._id,
        originalAmount: deposit.originalAmount,
        adminVerifiedAmount: deposit.adminVerifiedAmount,
        currentAmount: deposit.amount,
        amountCorrectionReason: deposit.amountCorrectionReason,
        modifiedBy: deposit.amountModifiedBy,
        modifiedAt: deposit.amountModifiedAt
      }
    }
  });
});

// @desc    Update deposit status
// @route   PUT /api/admin/deposits/:id/status
// @access  Admin
export const updateDepositStatus = catchAsync(async (req: Request, res: Response): Promise<any> => {
  const { id } = req.params;

  // Log the request body for debugging
  logger.debug('Update deposit status request body:', JSON.stringify(req.body));

  // Validate request body
  if (!req.body || typeof req.body !== 'object') {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid request body format'
    });
  }

  const { status, adminNotes, txHash, walletAddress, adminCorrectedAmount, amountCorrectionReason } = req.body;

  // Validate required fields
  if (!status) {
    return res.status(400).json({
      status: 'error',
      message: 'Status is required'
    });
  }

  // Validate corrected amount if provided
  if (adminCorrectedAmount !== undefined) {
    if (typeof adminCorrectedAmount !== 'number' || adminCorrectedAmount < 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Admin corrected amount must be a positive number'
      });
    }
  }

  // Validate status
  if (!['pending', 'processing', 'approved', 'rejected'].includes(status)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid status value'
    });
  }

  logger.info(`Updating deposit status: ${id} to ${status}`);

  // Find the deposit transaction with populated user data and investment data
  const deposit = await Transaction.findOne({ _id: id, type: 'deposit' })
    .populate({
      path: 'userId',
      select: 'firstName lastName email phoneNumber country city',
      model: 'User'
    })
    .populate({
      path: 'investmentId',
      select: 'amount currency receiptUrl cryptoAddress network adminNotes',
      model: 'Investment'
    });

  if (!deposit) {
    throw new AppError('Deposit not found', 404);
  }

  // Store original status before updating it
  const originalStatus = deposit.status;

  // Log user data for debugging
  const user = deposit.userId as any;
  if (user && typeof user !== 'string' && user !== null && user._id) {
    logger.debug(`Found user for deposit: ${user.firstName} ${user.lastName} (${user.email})`);
  } else {
    logger.warn(`User data not properly populated for deposit: ${id}`);
  }

  // Log investment data for debugging
  const investment = deposit.investmentId as any;
  if (investment && typeof investment !== 'string' && investment !== null && investment._id) {
    logger.debug(`Found investment data for deposit: ${investment._id}`);
  } else if (deposit.investmentId) {
    logger.warn(`Investment data not properly populated for deposit: ${id}, investmentId: ${deposit.investmentId}`);
  }

  // Update the deposit
  deposit.status = status;

  // Handle amount correction
  if (adminCorrectedAmount !== undefined && adminCorrectedAmount !== deposit.amount) {
    // Store original amount if not already stored
    if (!deposit.originalAmount) {
      deposit.originalAmount = deposit.amount;
    }

    // Update with corrected amount
    deposit.adminVerifiedAmount = adminCorrectedAmount;
    deposit.amount = adminCorrectedAmount; // Use corrected amount as the effective amount
    deposit.amountModifiedBy = req.user?.id;
    deposit.amountModifiedAt = new Date();

    if (amountCorrectionReason) {
      deposit.amountCorrectionReason = amountCorrectionReason;
    }

    logger.info(`Amount corrected for deposit ${deposit._id}: ${deposit.originalAmount} → ${adminCorrectedAmount}`);
  }

  // Update metadata
  if (!deposit.metadata) deposit.metadata = {};

  if (adminNotes) {
    deposit.metadata.adminNotes = adminNotes;
  }

  if (txHash) {
    deposit.txHash = txHash;
  }

  if (walletAddress) {
    deposit.walletAddress = walletAddress;
  }

  // Update the corresponding investment if it exists
  if (deposit.investmentId) {
    try {
      // Get the investment ID
      const investmentId = typeof deposit.investmentId === 'object' && deposit.investmentId !== null
        ? (deposit.investmentId as any)._id
        : deposit.investmentId;

      if (investmentId) {
        logger.info(`Updating investment ${investmentId} status to ${status}`);

        // Find the investment
        const investment = await Investment.findById(investmentId);

        if (investment) {
          // Update investment status to match deposit status
          investment.status = status;

          // Update investment amount if corrected
          if (adminCorrectedAmount !== undefined && adminCorrectedAmount !== investment.amount) {
            investment.amount = adminCorrectedAmount;
            logger.info(`Investment ${investmentId} amount updated to ${adminCorrectedAmount}`);
          }

          // Update additional fields based on status
          if (status === 'approved') {
            investment.approvedAt = new Date();
            if (adminNotes) {
              investment.adminNotes = adminNotes;
            }
            if (txHash) {
              investment.txHash = txHash;
            }

            // Note: Referral commission will be processed inside the MongoDB session below
          } else if (status === 'rejected') {
            investment.rejectedAt = new Date();
            if (adminNotes) {
              investment.rejectionReason = adminNotes;
              investment.adminNotes = adminNotes;
            }
          }

          // Save the updated investment
          await investment.save();
          logger.info(`Investment ${investmentId} status updated to ${status}`);
        } else {
          logger.error(`Investment not found for deposit ${deposit._id}, investmentId: ${investmentId}`);
        }
      }
    } catch (error) {
      logger.error(`Error updating investment for deposit ${deposit._id}:`, error);
    }
  }

  // If approved, update user's balance, wallet assets, and CREATE INVESTMENT PACKAGE
  if (status === 'approved' && originalStatus !== 'approved') {
    // Start MongoDB session for atomic operations
    const session = await mongoose.startSession();
    let sessionEnded = false;

    // Helper function to safely end session
    const safeEndSession = async (shouldAbort = false) => {
      if (!sessionEnded) {
        try {
          if (shouldAbort && session.inTransaction()) {
            await session.abortTransaction();
          } else if (session.inTransaction()) {
            await session.commitTransaction();
          }
        } catch (sessionError) {
          logger.error('Error ending MongoDB session:', sessionError);
        } finally {
          session.endSession();
          sessionEnded = true;
        }
      }
    };

    try {
      session.startTransaction();

      // Find the user
      const userId = typeof deposit.userId === 'object' && deposit.userId !== null
        ? (deposit.userId as any)._id
        : deposit.userId;

      if (!userId) {
        await safeEndSession(true);
        logger.error(`Cannot update balance: Invalid userId for deposit ${deposit._id}`);
        throw new Error(`Invalid userId for deposit ${deposit._id}`);
      }

      const user = await User.findById(userId).session(session);

      if (user) {
          // Update user's balance based on the deposit asset
          // Initialize balances if it doesn't exist
          if (!user.balances) {
            user.balances = {} as Record<string, number>;
          }

          // Get asset and amount, preferring from investment if available
          const investment = deposit.investmentId as any;
          const asset = investment && typeof investment !== 'string' && investment !== null
            ? investment.currency?.toUpperCase()
            : deposit.asset?.toUpperCase();

          const amount = investment && typeof investment !== 'string' && investment !== null
            ? investment.amount
            : deposit.amount;

          const network = investment && typeof investment !== 'string' && investment !== null
            ? investment.network
            : deposit.blockchainNetwork;

          if (!asset) {
            logger.error(`Cannot update balance: Invalid asset for deposit ${deposit._id}`);
            return;
          }

          // Use type assertion to handle the balances property
          const balances = user.balances as Record<string, number>;
          balances[asset] = (balances[asset] || 0) + amount;

          // Save the updated user
          await user.save();

          logger.info(`User ${user._id} balance updated: +${amount} ${asset}`);

          // Update user's wallet assets with real-time synchronization
          try {
            // Find or create wallet for the user
            let wallet = await Wallet.findOne({ userId: user._id });

            if (!wallet) {
              // Create a new wallet
              wallet = await Wallet.create({
                userId: user._id,
                assets: [],
                totalCommissionEarned: 0,
                totalInterestEarned: 0
              });
              logger.info(`Created new wallet for user ${user._id} during deposit approval`);
            }

            // Find or create asset in wallet
            let assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
            if (assetIndex === -1) {
              // Add new asset to wallet
              wallet.assets.push({
                symbol: asset,
                balance: 0,
                commissionBalance: 0,
                interestBalance: 0,
                mode: 'commission',
                network: network || undefined
              });
              assetIndex = wallet.assets.length - 1;
              logger.info(`Added new asset ${asset} to wallet for user ${user._id}`);
            }

            // Update asset balance
            wallet.assets[assetIndex].balance += amount;

            // Calculate commission (1%)
            const commissionRate = 0.01; // 1%
            const commissionAmount = amount * commissionRate;

            // Add commission if in commission mode
            if (wallet.assets[assetIndex].mode === 'commission') {
              wallet.assets[assetIndex].commissionBalance += commissionAmount;
              wallet.totalCommissionEarned += commissionAmount;
            }

            // Save wallet with retry logic to handle version conflicts
            let saveAttempts = 0;
            const maxSaveAttempts = 3;

            while (saveAttempts < maxSaveAttempts) {
              try {
                await wallet.save({ session });
                break; // Success, exit retry loop
              } catch (saveError: any) {
                saveAttempts++;

                if (saveError.name === 'VersionError' && saveAttempts < maxSaveAttempts) {
                  logger.warn(`Wallet version conflict (attempt ${saveAttempts}/${maxSaveAttempts}), retrying...`, {
                    walletId: wallet._id,
                    userId: user._id,
                    attempt: saveAttempts
                  });

                  // Reload wallet and reapply changes
                  const freshWallet = await Wallet.findById(wallet._id).session(session);
                  if (!freshWallet) {
                    throw new Error(`Wallet not found during retry: ${wallet._id}`);
                  }

                  // Find or create asset again
                  let freshAssetIndex = freshWallet.assets.findIndex(a => a.symbol.toUpperCase() === asset.toUpperCase());
                  if (freshAssetIndex === -1) {
                    freshWallet.assets.push({
                      symbol: asset.toUpperCase(),
                      balance: 0,
                      interestBalance: 0,
                      commissionBalance: 0,
                      mode: 'interest' // Default to interest mode for new assets
                    });
                    freshAssetIndex = freshWallet.assets.length - 1;
                  }

                  // Reapply balance changes
                  freshWallet.assets[freshAssetIndex].balance += amount;

                  // Reapply commission changes if needed
                  if (freshWallet.assets[freshAssetIndex].mode === 'commission') {
                    freshWallet.assets[freshAssetIndex].commissionBalance += commissionAmount;
                    freshWallet.totalCommissionEarned += commissionAmount;
                  }

                  // Update wallet reference for next attempt
                  wallet = freshWallet;
                  assetIndex = freshAssetIndex;

                  // Small delay before retry
                  await new Promise(resolve => setTimeout(resolve, 100 * saveAttempts));
                } else {
                  // Not a version error or max attempts reached
                  throw saveError;
                }
              }
            }

            if (saveAttempts >= maxSaveAttempts) {
              throw new Error(`Failed to save wallet after ${maxSaveAttempts} attempts due to version conflicts`);
            }
            logger.info(`Updated wallet asset ${asset} balance for user ${user._id}: +${amount}`);

            // CRITICAL: CREATE INVESTMENT PACKAGE for 30-day lock validation
            try {
              const InvestmentPackage = require('../models/investmentPackageModel').default;

              // Check if investment package already exists for this deposit
              const existingPackage = await InvestmentPackage.findOne({
                userId: user._id,
                depositTransactionId: deposit._id
              }).session(session);

              if (!existingPackage) {
                // Generate unique identifiers for the investment package
                const crypto = require('crypto');
                const timestamp = Date.now().toString();
                const random = Math.random().toString(36).substring(2, 8);
                const packageId = `PKG-${timestamp}-${random}`.toUpperCase();

                // Generate package hash
                const hashData = `${user._id}-${amount}-${asset}-${new Date().toISOString()}`;
                const packageHash = crypto.createHash('sha256').update(hashData).digest('hex');

                // Create new investment package with all required fields
                const investmentPackage = new InvestmentPackage({
                  // Required fields
                  userId: user._id,
                  transactionId: deposit._id, // CRITICAL: Link to deposit transaction
                  packageId: packageId, // Unique package identifier
                  packageHash: packageHash, // Unique hash for the package

                  // Core investment fields
                  amount: amount,
                  currency: asset.toUpperCase(), // Use 'currency' field as per model
                  status: 'active', // Set as active since deposit is approved

                  // Interest and earnings fields
                  interestRate: 0.01, // Fixed 1% daily interest
                  totalEarned: 0,
                  accumulatedInterest: 0,
                  dailyInterest: 0,

                  // Time-related fields
                  createdAt: new Date(), // CRITICAL: This timestamp is used for 30-day lock validation
                  activatedAt: new Date(), // Activate immediately since deposit is approved
                  lastCalculatedAt: new Date(),

                  // 30-day principal lock fields
                  principalLockUntil: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)), // 30 days from now
                  principalLocked: true,

                  // Deposit-related fields
                  depositTransactionId: deposit._id,
                  depositCurrency: asset.toUpperCase(),
                  depositAmount: amount,
                  autoCreated: true, // Mark as auto-created from deposit approval

                  // Default values
                  totalDays: 365, // Default 1 year investment period
                  activeDays: 0,
                  roi: 0,
                  compoundEnabled: false,
                  emergencyWithdrawFee: 0.05, // 5% emergency withdrawal fee
                  minimumWithdrawalUSDT: 50,
                  withdrawableInterest: 0
                });

                await investmentPackage.save({ session });

                logger.info(`Created investment package for deposit approval`, {
                  userId: user._id,
                  depositId: deposit._id,
                  packageId: investmentPackage.packageId,
                  packageHash: investmentPackage.packageHash,
                  transactionId: investmentPackage.transactionId,
                  amount: amount,
                  currency: asset,
                  principalLockUntil: investmentPackage.principalLockUntil,
                  createdAt: investmentPackage.createdAt,
                  note: 'This package enables 30-day investment lock validation'
                });
              } else {
                logger.info(`Investment package already exists for deposit ${deposit._id}, package ID: ${existingPackage.packageId}`);
              }
            } catch (packageError) {
              logger.error(`Error creating investment package for deposit ${deposit._id}:`, packageError);
              // Rollback the entire transaction if investment package creation fails
              await safeEndSession(true);
              throw new Error(`Failed to create investment package: ${packageError.message}`);
            }

            // CRITICAL: Send real-time WebSocket notification for balance update
            try {
              const { initializeSocketService } = require('../services/socketService');
              const socketService = initializeSocketService(null);

              // Notify user about balance update
              socketService.broadcastToUser(user._id.toString(), {
                type: 'wallet_balance_updated',
                payload: {
                  currency: asset,
                  balance: wallet.assets[assetIndex].balance,
                  previousBalance: wallet.assets[assetIndex].balance - amount,
                  changeAmount: amount,
                  changeType: 'credit',
                  timestamp: new Date().toISOString(),
                  source: 'deposit_approval'
                }
              });

              // Also send general wallet update
              socketService.broadcastToUser(user._id.toString(), {
                type: 'wallet_update',
                payload: {
                  currency: asset,
                  balance: wallet.assets[assetIndex].balance,
                  previousBalance: wallet.assets[assetIndex].balance - amount,
                  changeAmount: amount,
                  changeType: 'credit',
                  timestamp: new Date().toISOString()
                }
              });

              logger.info(`Sent real-time balance update notification to user ${user._id} for ${amount} ${asset}`);
            } catch (wsError) {
              logger.error(`Error sending WebSocket balance update for user ${user._id}:`, wsError);
            }

            // Create a commission transaction if commission was earned
            if (wallet.assets[assetIndex].mode === 'commission' && commissionAmount > 0) {
              try {
                const { createTransaction } = require('../utils/transactionUtils');
                await createTransaction({
                  userId: user._id,
                  walletId: wallet._id,
                  type: 'commission',
                  asset: asset,
                  amount: commissionAmount,
                  status: 'completed',
                  description: `Commission from deposit ${deposit._id}`,
                  metadata: {
                    depositId: deposit._id.toString(),
                    commissionRate: commissionRate
                  }
                });
                logger.info(`Created commission transaction for user ${user._id}: +${commissionAmount} ${asset}`);
              } catch (commissionError) {
                logger.error(`Error creating commission transaction for user ${user._id}:`, commissionError);
              }
            }

            // PROCESS REFERRAL COMMISSION (3%) - Inside MongoDB session
            try {
              const { applyInvestmentCommissions } = require('../services/simpleCommissionService');

              const commissionResult = await applyInvestmentCommissions(
                user._id,
                amount,
                asset,
                session, // Pass session for atomic operations
                deposit._id // Pass deposit ID as investmentId
              );

              if (commissionResult.referralCommission) {
                logger.info(`Referral commission created: ${commissionResult.referralCommission.commissionAmount} ${asset} for user ${user._id}`);
              } else {
                logger.info(`No referral commission created for user ${user._id} (no referrer or already paid)`);
              }
            } catch (referralCommissionError) {
              logger.error(`Error creating referral commission for user ${user._id}:`, referralCommissionError);
              // Don't abort transaction for referral commission errors
            }
          } catch (walletError) {
            logger.error(`Error updating wallet for user ${user._id}:`, walletError);
            await safeEndSession(true);
            throw walletError;
          }
        } else {
          await safeEndSession(true);
          logger.error(`User not found for deposit ${deposit._id}, userId: ${userId}`);
          throw new Error(`User not found for deposit ${deposit._id}`);
        }

      // Commit the transaction if everything succeeded
      await safeEndSession(false);
      logger.info(`Successfully completed deposit approval with investment package creation for deposit ${deposit._id}`);

    } catch (error) {
      // Rollback transaction on any error
      await safeEndSession(true);
      logger.error(`Error during deposit approval process for deposit ${deposit._id}:`, error);
      throw error;
    }
  }

  // Save the updated deposit
  await deposit.save();

  // If status is approved, also update any related transactions
  if (status === 'approved') {
    try {
      // Import the updateTransaction utility
      const { updateTransaction } = require('../utils/transactionUtils');

      // Find any transactions related to this deposit that are still pending
      const relatedTransactions = await Transaction.find({
        investmentId: deposit.investmentId,
        status: 'pending',
        _id: { $ne: deposit._id } // Exclude the current deposit
      });

      if (relatedTransactions.length > 0) {
        logger.info(`Found ${relatedTransactions.length} related transactions to update for deposit ${deposit._id}`);

        // Update each related transaction
        for (const transaction of relatedTransactions) {
          await updateTransaction(transaction._id, {
            status: 'approved',
            txHash: deposit.txHash || transaction.txHash,
            adminNotes: adminNotes || transaction.metadata?.adminNotes,
            completedAt: new Date()
          });

          logger.info(`Updated related transaction ${transaction._id} to approved status`);
        }
      }
    } catch (error) {
      logger.error(`Error updating related transactions for deposit ${deposit._id}:`, error);
      // Continue with the response even if updating related transactions fails
    }
  }

  // Format user and investment data for both WebSocket and response
  const userData = deposit.userId as any;
  const investmentData = deposit.investmentId as any;
  const isUserPopulated = userData && typeof userData !== 'string' && userData !== null && userData._id;
  let isInvestmentPopulated = investmentData && typeof investmentData !== 'string' && investmentData !== null && investmentData._id;

  // Get updated investment data if it exists
  let updatedInvestmentData = investmentData;
  if (deposit.investmentId && (!isInvestmentPopulated || status === 'approved' || status === 'rejected')) {
    try {
      const investmentId = typeof deposit.investmentId === 'object' && deposit.investmentId !== null
        ? (deposit.investmentId as any)._id
        : deposit.investmentId;

      if (investmentId) {
        const updatedInvestment = await Investment.findById(investmentId);
        if (updatedInvestment) {
          updatedInvestmentData = updatedInvestment;
          isInvestmentPopulated = true;
        }
      }
    } catch (error) {
      logger.error(`Error fetching updated investment data: ${error}`);
    }
  }

  // Broadcast deposit update via Socket.IO
  try {
    const { initializeSocketService } = require('../services/socketService');
    const socketService = initializeSocketService(null);

    // Format deposit data for WebSocket
    const depositData = {
      _id: deposit._id,
      userId: isUserPopulated ? userData._id : deposit.userId,
      userName: isUserPopulated ? `${userData.firstName || ''} ${userData.lastName || ''}`.trim() : 'Unknown User',
      userEmail: isUserPopulated ? userData.email : '<EMAIL>',
      phoneNumber: isUserPopulated ? userData.phoneNumber : '',
      country: isUserPopulated ? userData.country : '',
      city: isUserPopulated ? userData.city : '',
      amount: isInvestmentPopulated ? updatedInvestmentData.amount : deposit.amount,
      asset: deposit.asset,
      currency: isInvestmentPopulated ? updatedInvestmentData.currency : deposit.asset,
      status: deposit.status,
      txHash: deposit.txHash,
      walletAddress: deposit.walletAddress || (isInvestmentPopulated ? updatedInvestmentData.cryptoAddress : ''),
      metadata: deposit.metadata,
      createdAt: deposit.createdAt,
      updatedAt: new Date(),
      investmentId: deposit.investmentId,
      receiptUrl: isInvestmentPopulated ? updatedInvestmentData.receiptUrl : deposit.metadata?.receiptUrl || '',
      adminNotes: isInvestmentPopulated ? updatedInvestmentData.adminNotes : deposit.metadata?.adminNotes || '',
      network: isInvestmentPopulated ? updatedInvestmentData.network : deposit.blockchainNetwork || ''
    };

    // Broadcast the update to all admin clients
    await socketService.broadcastDepositUpdate(depositData);
    logger.info(`Broadcast deposit update for ${deposit._id} with status ${status} to all admin clients`);

    // Also notify the user about their deposit status update
    if (isUserPopulated && userData._id) {
      socketService.broadcastToUser(userData._id.toString(), {
        type: 'deposit_status_updated',
        payload: {
          transaction: {
            id: deposit._id,
            status: deposit.status,
            amount: deposit.amount,
            currency: deposit.asset,
            updatedAt: new Date()
          },
          message: `Your deposit status has been updated to ${status}`,
          timestamp: new Date().toISOString()
        }
      });
      logger.info(`Notified user ${userData._id} about deposit status update to ${status}`);
    }
  } catch (error) {
    logger.error('Failed to broadcast deposit update via WebSocket:', error);
    // Continue with the response even if WebSocket broadcast fails
  }



  return res.status(200).json({
    success: true,
    message: `Deposit ${status}`,
    deposit: {
      id: deposit._id,
      status: deposit.status,
      txHash: deposit.txHash,
      adminNotes: isInvestmentPopulated ? updatedInvestmentData.adminNotes : deposit.metadata?.adminNotes,
      user: isUserPopulated ? `${userData.firstName || ''} ${userData.lastName || ''}`.trim() : 'Unknown User',
      userId: isUserPopulated ? userData._id : deposit.userId,
      email: isUserPopulated ? userData.email : '<EMAIL>',
      phoneNumber: isUserPopulated ? userData.phoneNumber : '',
      country: isUserPopulated ? userData.country : '',
      city: isUserPopulated ? userData.city : '',
      amount: isInvestmentPopulated ? updatedInvestmentData.amount : deposit.amount,
      currency: isInvestmentPopulated ? updatedInvestmentData.currency : deposit.asset,
      date: deposit.createdAt,
      network: isInvestmentPopulated ? updatedInvestmentData.network : deposit.blockchainNetwork || '',
      wallet: isInvestmentPopulated ? updatedInvestmentData.cryptoAddress : deposit.walletAddress || '',
      receiptUrl: isInvestmentPopulated ? updatedInvestmentData.receiptUrl : deposit.metadata?.receiptUrl || '',
      source: isInvestmentPopulated ? 'investment' : 'transaction',
      // Include investment status information
      investmentStatus: isInvestmentPopulated ? updatedInvestmentData.status : null,
      approvedAt: isInvestmentPopulated ? updatedInvestmentData.approvedAt : null,
      rejectedAt: isInvestmentPopulated ? updatedInvestmentData.rejectedAt : null,
      rejectionReason: isInvestmentPopulated ? updatedInvestmentData.rejectionReason : null
    }
  });
});







export const getAdminDeposits = catchAsync(async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const currency = req.query.currency as string;
    const search = req.query.search as string;

    logger.info('Fetching admin deposits from database');

    // Find users matching the search term if search is provided
    let userIds: any[] = [];
    if (search) {
      const users = await User.find({
        $or: [
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      userIds = users.map(user => user._id);
    }

    // Build transaction query
    const transactionQuery: any = { type: 'deposit' };
    if (status) transactionQuery.status = status;
    if (currency) transactionQuery.asset = currency.toUpperCase();
    if (search && userIds.length > 0) {
      transactionQuery.userId = { $in: userIds };
    } else if (search) {
      // If no users match, check if search is a transaction ID or wallet address
      transactionQuery.$or = [
        { _id: search.length === 24 ? search : null },
        { walletAddress: { $regex: search, $options: 'i' } },
        { txHash: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute queries with pagination
    const [transactions, transactionsTotal] = await Promise.all([
      Transaction.find(transactionQuery)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .populate({
          path: 'userId',
          select: 'firstName lastName email phoneNumber country city',
          model: 'User'
        })
        .populate({
          path: 'investmentId',
          select: 'amount currency receiptUrl cryptoAddress network adminNotes',
          model: 'Investment'
        }),
      Transaction.countDocuments(transactionQuery)
    ]);

    // Format transactions
    const formattedTransactions = [];

    for (const tx of transactions) {
      try {
        // Handle populated userId field
        const user = tx.userId as any; // Cast to any to access populated fields
        const investment = tx.investmentId as any; // Cast to any to access populated fields

        // Check if user data is properly populated
        const isUserPopulated = user && typeof user !== 'string' && user !== null && user._id;

        // If investment is not populated, try to find it
        let investmentData = investment;
        if (!investment && tx.investmentId) {
          try {
            investmentData = await Investment.findById(tx.investmentId);
          } catch (error) {
            logger.warn(`Failed to find investment data for transaction: ${tx._id}, investmentId: ${tx.investmentId}`);
            // Set investmentData to null to avoid undefined errors
            investmentData = null;
          }
        }

        formattedTransactions.push({
          id: tx._id,
          user: isUserPopulated ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : 'Unknown User',
          userId: isUserPopulated ? user._id : tx.userId,
          email: isUserPopulated ? user.email : '<EMAIL>',
          phoneNumber: isUserPopulated ? user.phoneNumber : '',
          country: isUserPopulated ? user.country : '',
          city: isUserPopulated ? user.city : '',
          wallet: tx.walletAddress || '',
          amount: investmentData ? investmentData.amount : tx.amount,
          currency: investmentData ? investmentData.currency : tx.asset,
          date: tx.createdAt,
          status: tx.status,
          receiptUrl: investmentData ? investmentData.receiptUrl : tx.metadata?.receiptUrl || '',
          txHash: tx.txHash || '',
          network: investmentData ? investmentData.network : tx.blockchainNetwork || '',
          adminNotes: investmentData ? investmentData.adminNotes : tx.metadata?.adminNotes || '',
          source: investmentData ? 'investment' : 'transaction',
          cryptoAddress: investmentData ? investmentData.cryptoAddress : ''
        });
      } catch (error) {
        logger.error(`Error processing transaction ${tx._id}:`, error);
        // Add a minimal object with the transaction ID in case of error
        formattedTransactions.push({
          id: tx._id,
          user: 'Error processing user',
          userId: tx.userId,
          email: '<EMAIL>',
          wallet: tx.walletAddress || '',
          amount: tx.amount,
          currency: tx.asset,
          date: tx.createdAt,
          status: tx.status,
          source: 'transaction'
        });
      }
    }

    res.status(200).json({
      success: true,
      deposits: formattedTransactions,
      pagination: {
        page,
        limit,
        total: transactionsTotal,
        pages: Math.ceil(transactionsTotal / limit)
      }
    });

  } catch (error: any) {
    logger.error('Admin deposits fetch error:', error);
    res.status(500).json({
      message: 'Failed to fetch deposits',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get all transactions for admin
// @route   GET /api/admin/transactions
// @access  Admin
export const getAdminTransactions = catchAsync(async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const type = req.query.type as string;
    const search = req.query.search as string;
    const currency = req.query.currency as string;

    logger.info('Fetching admin transactions from database');

    // Find users matching the search term if search is provided
    let userIds: any[] = [];
    if (search) {
      const users = await User.find({
        $or: [
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      userIds = users.map(user => user._id);
    }

    // Build transaction query
    const transactionQuery: any = {};
    if (status) transactionQuery.status = status;
    if (type) transactionQuery.type = type;
    if (currency) transactionQuery.asset = currency.toUpperCase();

    // Add search functionality
    if (search && userIds.length > 0) {
      transactionQuery.userId = { $in: userIds };
    } else if (search) {
      // If no users match, check if search is a transaction ID or wallet address
      transactionQuery.$or = [
        { _id: search.length === 24 ? search : null },
        { walletAddress: { $regex: search, $options: 'i' } },
        { txHash: { $regex: search, $options: 'i' } }
      ];
    }

    // Log query for debugging
    logger.debug('Transaction query:', JSON.stringify(transactionQuery));

    // Execute queries with pagination
    const [transactions, transactionsTotal] = await Promise.all([
      Transaction.find(transactionQuery)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .populate({
          path: 'userId',
          select: 'firstName lastName email phoneNumber country city',
          model: 'User'
        })
        .populate({
          path: 'investmentId',
          select: 'amount currency receiptUrl cryptoAddress network adminNotes',
          model: 'Investment'
        }),
      Transaction.countDocuments(transactionQuery)
    ]);

    // Log number of transactions found
    logger.debug(`Found ${transactions.length} transactions out of ${transactionsTotal} total`);

    // Format transactions
    const formattedTransactions = [];

    for (const tx of transactions) {
      try {
        // Handle populated userId field
        const user = tx.userId as any; // Cast to any to access populated fields
        const investment = tx.investmentId as any; // Cast to any to access populated fields

        // Check if user data is properly populated
        const isUserPopulated = user && typeof user !== 'string' && user !== null && user._id;
        const isInvestmentPopulated = investment && typeof investment !== 'string' && investment !== null && investment._id;

        // Log user data for debugging
        if (isUserPopulated) {
          logger.debug(`User data for transaction ${tx._id}:`, {
            id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email
          });
        } else {
          logger.warn(`User data not populated for transaction ${tx._id}`);
        }

        formattedTransactions.push({
          id: tx._id,
          user: isUserPopulated ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : 'Unknown User',
          userId: isUserPopulated ? user._id : tx.userId,
          email: isUserPopulated ? user.email : '<EMAIL>',
          phoneNumber: isUserPopulated ? user.phoneNumber : '',
          country: isUserPopulated ? user.country : '',
          city: isUserPopulated ? user.city : '',
          wallet: tx.walletAddress || '',
          amount: tx.amount,
          currency: tx.asset,
          date: tx.createdAt,
          status: tx.status,
          txHash: tx.txHash || '',
          network: tx.blockchainNetwork || '',
          adminNotes: tx.metadata?.adminNotes || '',
          type: tx.type,
          investmentId: tx.investmentId,
          receiptUrl: isInvestmentPopulated ? investment.receiptUrl : tx.metadata?.receiptUrl || '',
          source: isInvestmentPopulated ? 'investment' : 'transaction'
        });
      } catch (error) {
        logger.error(`Error processing transaction ${tx._id}:`, error);
        // Add a minimal object with the transaction ID in case of error
        formattedTransactions.push({
          id: tx._id,
          user: 'Error processing user',
          userId: tx.userId,
          email: '<EMAIL>',
          wallet: tx.walletAddress || '',
          amount: tx.amount,
          currency: tx.asset,
          date: tx.createdAt,
          status: tx.status,
          type: tx.type,
          source: 'transaction'
        });
      }
    }

    res.status(200).json({
      success: true,
      transactions: formattedTransactions,
      pagination: {
        page,
        limit,
        total: transactionsTotal,
        pages: Math.ceil(transactionsTotal / limit)
      }
    });

  } catch (error: any) {
    logger.error('Admin transactions fetch error:', error);
    res.status(500).json({
      message: 'Failed to fetch transactions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
