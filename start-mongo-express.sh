#!/bin/bash

# Start Mongo Express - Final Working Version
set -e

echo "🚀 Starting MongoDB and Mongo Express..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Stop and clean up
print_status "Stopping existing services..."
docker-compose -f docker-compose.mongo.yml down

# Start all services at once
print_status "Starting all services..."
docker-compose -f docker-compose.mongo.yml up -d

# Wait for containers to start
print_status "Waiting for containers to start..."
sleep 45

# Check container status
print_status "Checking container status..."
docker-compose -f docker-compose.mongo.yml ps

# Initialize replica set if needed
print_status "Initializing MongoDB replica set..."
for i in {1..5}; do
    if docker exec cryptoyield-mongodb mongosh --eval "
    try {
        const status = rs.status();
        if (status.ok === 1) {
            print('✅ Replica set already active');
        }
    } catch (e) {
        if (e.message.includes('no replset config')) {
            rs.initiate({
                _id: 'rs0',
                members: [{ _id: 0, host: 'mongodb:27017' }]
            });
            print('✅ Replica set initialized');
        } else {
            print('⚠️ ' + e.message);
        }
    }
    " 2>/dev/null; then
        print_success "MongoDB replica set is ready"
        break
    else
        print_warning "Attempt $i/5: Waiting for MongoDB..."
        sleep 10
    fi
done

# Wait a bit more for everything to stabilize
print_status "Waiting for services to stabilize..."
sleep 30

# Test connectivity
print_status "Testing connectivity..."

# Test MongoDB
if docker exec cryptoyield-mongodb mongosh --eval "db.adminCommand('ping')" 2>/dev/null | grep -q "ok"; then
    print_success "✅ MongoDB is accessible"
else
    print_warning "⚠️ MongoDB may still be starting"
fi

# Test Redis
if docker exec cryptoyield-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
    print_success "✅ Redis is accessible"
else
    print_warning "⚠️ Redis may still be starting"
fi

# Test Mongo Express
print_status "Testing Mongo Express..."
for i in {1..6}; do
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 | grep -q "200\|401"; then
        print_success "✅ Mongo Express is accessible at http://localhost:8081"
        break
    else
        print_warning "Attempt $i/6: Waiting for Mongo Express..."
        sleep 10
    fi
done

# Final status
echo ""
echo "🎉 Setup completed!"
echo ""
echo "📋 Service Information:"
echo "  🌐 Mongo Express: http://localhost:8081"
echo "  🔑 Login: admin / admin123"
echo "  🗄️ MongoDB: localhost:27017"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🔗 MongoDB Connection String:"
echo "  **********************************************************************************************************"
echo ""

# Show final container status
print_status "Final container status:"
docker-compose -f docker-compose.mongo.yml ps

# Show Mongo Express logs if there are issues
if ! curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 | grep -q "200\|401"; then
    echo ""
    print_warning "Mongo Express may need more time. Recent logs:"
    docker logs cryptoyield-mongo-express --tail 10
    echo ""
    echo "💡 If you see connection errors, wait 2-3 minutes and try accessing http://localhost:8081 again."
fi

echo ""
print_success "🎯 You can now run your backend with: cd backend && npm run dev:docker"
