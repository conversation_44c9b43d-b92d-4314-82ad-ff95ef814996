import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  HStack,
  Icon
} from '@chakra-ui/react';
import { FaUserShield, FaSignOutAlt } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { adminApiService } from '../services/adminApi';
import useAuth from '../hooks/useAuth';

interface ImpersonationBannerProps {
  isVisible?: boolean;
}

const ImpersonationBanner: React.FC<ImpersonationBannerProps> = ({ isVisible = true }) => {
  const [isImpersonating, setIsImpersonating] = useState(false);
  const [adminInfo, setAdminInfo] = useState<any>(null);
  const [isReturning, setIsReturning] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();

  useEffect(() => {
    checkImpersonationStatus();
  }, []);

  const checkImpersonationStatus = () => {
    // Check if impersonation cookie exists
    const isImpersonatingCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('isImpersonating='));
    
    if (isImpersonatingCookie && isImpersonatingCookie.split('=')[1] === 'true') {
      setIsImpersonating(true);
      
      // Try to get admin info from session storage or other source
      const storedAdminInfo = localStorage.getItem('impersonationAdminInfo');
      if (storedAdminInfo) {
        try {
          setAdminInfo(JSON.parse(storedAdminInfo));
        } catch (error) {
          console.error('Error parsing admin info:', error);
        }
      }
    } else {
      setIsImpersonating(false);
      setAdminInfo(null);
    }
  };

  const handleReturnToAdmin = async () => {
    setIsReturning(true);
    
    try {
      const response = await adminApiService.returnToAdmin();
      
      if (response.data && response.data.data) {
        const adminData = response.data.data;
        
        // Update auth context with admin data
        await login(adminData.email, '', adminData);
        
        // Clear impersonation state
        setIsImpersonating(false);
        setAdminInfo(null);
        localStorage.removeItem('impersonationAdminInfo');
        
        toast({
          title: 'Success',
          description: response.data.message || 'Successfully returned to admin panel',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Redirect to admin dashboard
        navigate('/admin');
      }
    } catch (error: any) {
      console.error('Return to admin error:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to return to admin panel',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsReturning(false);
    }
  };

  if (!isVisible || !isImpersonating) {
    return null;
  }

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      zIndex={9999}
      bg="orange.500"
      color="white"
      py={2}
      px={4}
      boxShadow="md"
    >
      <Flex
        maxW="1200px"
        mx="auto"
        align="center"
        justify="space-between"
        flexDir={{ base: "column", md: "row" }}
        gap={{ base: 2, md: 0 }}
      >
        <HStack spacing={3}>
          <Icon as={FaUserShield} boxSize={5} />
          <Text fontWeight="bold" fontSize="sm">
            Admin Impersonation Mode
          </Text>
          {adminInfo && (
            <Text fontSize="sm" opacity={0.9}>
              Logged in as user by admin: {adminInfo.adminName || adminInfo.adminEmail}
            </Text>
          )}
        </HStack>

        <Button
          size="sm"
          variant="outline"
          colorScheme="whiteAlpha"
          leftIcon={<FaSignOutAlt />}
          onClick={handleReturnToAdmin}
          isLoading={isReturning}
          loadingText="Returning..."
          _hover={{
            bg: "rgba(255,255,255,0.2)",
            borderColor: "white"
          }}
        >
          Return to Admin
        </Button>
      </Flex>
    </Box>
  );
};

export default ImpersonationBanner;
