import mongoose from 'mongoose';
import User from '../models/userModel';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import { clearWalletCache, clearReferralCache } from '../utils/cacheUtils';
import { logger } from '../utils/logger';

export interface ReferralCommissionResult {
  success: boolean;
  message: string;
  commissionAmount?: number;
  referrerId?: string;
  transactionId?: string;
}

export class FirstDepositCommissionService {
  private static readonly COMMISSION_RATE = 0.03; // 3%

  /**
   * Process referral commission for first-time deposit approval
   */
  static async processFirstDepositCommission(
    userId: mongoose.Types.ObjectId,
    depositAmount: number,
    cryptocurrency: string,
    depositTransactionId: mongoose.Types.ObjectId
  ): Promise<ReferralCommissionResult> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      // 1. Find the user who made the deposit
      const user = await User.findById(userId).session(session);
      if (!user) {
        await session.abortTransaction();
        return {
          success: false,
          message: 'User not found'
        };
      }

      // 2. Check if this is the user's first approved deposit
      if (user.hasFirstDepositApproved) {
        await session.abortTransaction();
        return {
          success: false,
          message: 'User has already had their first deposit approved - no commission applicable'
        };
      }

      // 3. Validate deposit amount
      if (depositAmount <= 0) {
        await session.abortTransaction();
        return {
          success: false,
          message: 'Invalid deposit amount'
        };
      }

      // 4. Find the referrer
      if (!user.referrerId) {
        // Mark user as having first deposit approved (even without referrer)
        user.hasFirstDepositApproved = true;
        user.firstDepositApprovedAt = new Date();
        await user.save({ session });
        
        await session.commitTransaction();
        return {
          success: false,
          message: 'User has no referrer - no commission to process'
        };
      }

      const referrer = await User.findById(user.referrerId).session(session);
      if (!referrer) {
        // Mark user as having first deposit approved
        user.hasFirstDepositApproved = true;
        user.firstDepositApprovedAt = new Date();
        await user.save({ session });
        
        await session.abortTransaction();
        return {
          success: false,
          message: 'Referrer not found'
        };
      }

      // 5. Calculate commission (3% of deposit amount)
      const commissionAmount = depositAmount * this.COMMISSION_RATE;

      logger.info(`Processing referral commission`, {
        userId: userId.toString(),
        referrerId: referrer._id.toString(),
        depositAmount,
        commissionAmount,
        cryptocurrency,
        depositTransactionId: depositTransactionId.toString()
      });

      // 6. Find or create referrer's wallet
      let referrerWallet = await Wallet.findOne({ userId: referrer._id }).session(session);
      if (!referrerWallet) {
        referrerWallet = new Wallet({
          userId: referrer._id,
          assets: [],
          totalCommissionEarned: 0,
          totalInterestEarned: 0
        });
      }

      // 7. Find or create the cryptocurrency asset in referrer's wallet
      let assetIndex = referrerWallet.assets.findIndex(asset => asset.symbol === cryptocurrency);
      if (assetIndex === -1) {
        // Create new asset entry
        referrerWallet.assets.push({
          symbol: cryptocurrency,
          balance: 0,
          commissionBalance: commissionAmount,
          interestBalance: 0,
          mode: 'commission',
          network: 'mainnet'
        });
      } else {
        // Update existing asset
        referrerWallet.assets[assetIndex].commissionBalance += commissionAmount;
      }

      // 8. Update referrer's total commission earned
      referrerWallet.totalCommissionEarned += commissionAmount;

      // 9. Save referrer's wallet
      await referrerWallet.save({ session });

      // 10. Update referrer's user record
      referrer.referralEarnings = (referrer.referralEarnings || 0) + commissionAmount;
      referrer.totalCommission = (referrer.totalCommission || 0) + commissionAmount;
      await referrer.save({ session });

      // 11. Create commission transaction record
      const commissionTransaction = new Transaction({
        userId: referrer._id,
        walletId: referrerWallet._id,
        type: 'commission',
        asset: cryptocurrency,
        amount: commissionAmount,
        status: 'completed',
        description: `Referral commission for first deposit by ${user.firstName} ${user.lastName}`,
        metadata: {
          referralCommission: true,
          referredUserId: userId.toString(),
          referredUserName: `${user.firstName} ${user.lastName}`,
          originalDepositAmount: depositAmount,
          commissionRate: this.COMMISSION_RATE,
          depositTransactionId: depositTransactionId.toString(),
          isFirstDepositCommission: true
        }
      });

      await commissionTransaction.save({ session });

      // 12. Mark user as having first deposit approved
      user.hasFirstDepositApproved = true;
      user.firstDepositApprovedAt = new Date();
      await user.save({ session });

      // 13. Commit the transaction
      await session.commitTransaction();

      // 14. Clear cache for both user and referrer after successful commission processing
      try {
        // Clear cache for the user who made the deposit (first deposit status changed)
        clearReferralCache(userId.toString(), 'first deposit commission processed');

        // Clear cache for the referrer (wallet and referral stats updated)
        clearWalletCache(referrer._id.toString(), 'referral commission received');
        clearReferralCache(referrer._id.toString(), 'referral commission received');

        logger.info(`Cleared cache for user ${userId} and referrer ${referrer._id} after commission processing`);
      } catch (cacheError) {
        // Don't fail the commission processing if cache clearing fails
        logger.error('Error clearing cache after commission processing:', cacheError);
      }

      logger.info(`Referral commission processed successfully`, {
        userId: userId.toString(),
        referrerId: referrer._id.toString(),
        commissionAmount,
        cryptocurrency,
        commissionTransactionId: commissionTransaction._id.toString()
      });

      return {
        success: true,
        message: `Referral commission of ${commissionAmount} ${cryptocurrency} awarded to ${referrer.firstName} ${referrer.lastName}`,
        commissionAmount,
        referrerId: referrer._id.toString(),
        transactionId: commissionTransaction._id.toString()
      };

    } catch (error) {
      await session.abortTransaction();
      logger.error('Error processing referral commission:', error);
      
      return {
        success: false,
        message: `Error processing referral commission: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      session.endSession();
    }
  }

  /**
   * Check if a user is eligible for referral commission on their first deposit
   */
  static async checkFirstDepositEligibility(userId: mongoose.Types.ObjectId): Promise<{
    eligible: boolean;
    reason: string;
    referrerId?: string;
  }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          eligible: false,
          reason: 'User not found'
        };
      }

      if (user.hasFirstDepositApproved) {
        return {
          eligible: false,
          reason: 'User has already had their first deposit approved'
        };
      }

      if (!user.referrerId) {
        return {
          eligible: false,
          reason: 'User has no referrer'
        };
      }

      const referrer = await User.findById(user.referrerId);
      if (!referrer) {
        return {
          eligible: false,
          reason: 'Referrer not found'
        };
      }

      return {
        eligible: true,
        reason: 'User is eligible for first deposit referral commission',
        referrerId: referrer._id.toString()
      };

    } catch (error) {
      logger.error('Error checking first deposit eligibility:', error);
      return {
        eligible: false,
        reason: `Error checking eligibility: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get referral commission statistics for a user
   */
  static async getReferralStats(userId: mongoose.Types.ObjectId): Promise<{
    totalCommissionEarned: number;
    totalReferrals: number;
    firstDepositCommissions: number;
    commissionTransactions: any[];
  }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get commission transactions
      const commissionTransactions = await Transaction.find({
        userId: userId,
        type: 'commission',
        'metadata.referralCommission': true
      }).sort({ createdAt: -1 });

      // Count first deposit commissions
      const firstDepositCommissions = commissionTransactions.filter(
        tx => tx.metadata?.isFirstDepositCommission === true
      ).length;

      // Count total referrals (users referred by this user)
      const totalReferrals = await User.countDocuments({ referrerId: userId });

      return {
        totalCommissionEarned: user.totalCommission || 0,
        totalReferrals,
        firstDepositCommissions,
        commissionTransactions: commissionTransactions.map(tx => ({
          id: tx._id,
          amount: tx.amount,
          asset: tx.asset,
          createdAt: tx.createdAt,
          description: tx.description,
          metadata: tx.metadata
        }))
      };

    } catch (error) {
      logger.error('Error getting referral stats:', error);
      throw error;
    }
  }
}

export default FirstDepositCommissionService;
