import axios from 'axios';

// API URL from environment variables - Use consistent configuration
const API_URL = import.meta.env.VITE_API_URL || '/api';
console.log('🔧 Admin API URL:', API_URL);

// Create a function to get the admin API instance with cookie-based auth
export const getAdminApi = () => {
  // Use the correct URL for the admin API
  const baseUrl = `${API_URL}/admin`;

  console.log('Admin API baseURL:', baseUrl);

  // With cookie-based auth, we don't need to manually set the token
  // Just ensure withCredentials is set to true
  const api = axios.create({
    baseURL: baseUrl,
    withCredentials: true, // Important for cookies
    headers: {
      'Content-Type': 'application/json'
    }
  });

  // Add request interceptor for debugging and validation
  api.interceptors.request.use(
    (config) => {
      console.log('Admin API Request:', config.method?.toUpperCase(), config.url);

      // Log request headers
      console.log('Admin API Headers:', config.headers);

      // Validate request data if it's a POST or PUT request
      if (config.data && (config.method === 'post' || config.method === 'put')) {
        try {
          // If data is already a string, try to parse it to validate JSON
          if (typeof config.data === 'string') {
            JSON.parse(config.data);
          } else {
            // If data is an object, stringify it to ensure it's valid JSON
            // and then use the stringified version to ensure proper JSON format
            const validJsonString = JSON.stringify(config.data);
            config.data = validJsonString;
          }

          console.log('Admin API Request Data:', config.data);
        } catch (error) {
          console.error('Invalid JSON in request data:', error);
          // Return a rejected promise to prevent the request from being sent
          return Promise.reject(new Error('Invalid JSON in request data'));
        }
      }

      return config;
    },
    (error) => {
      console.error('Admin API Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor for debugging
  api.interceptors.response.use(
    (response) => {
      console.log('Admin API Response Status:', response.status);
      return response;
    },
    (error) => {
      console.error('Admin API Response Error:', error.response?.status, error.response?.data);

      // Create a more user-friendly error message
      let errorMessage = 'An error occurred while communicating with the server';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage = error.response.data?.message ||
                      `Server error: ${error.response.status}`;
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'No response received from server';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message || 'Request setup error';
      }

      // Add the error message to the error object
      error.userMessage = errorMessage;

      return Promise.reject(error);
    }
  );

  return api;
};

// Admin API service
export const adminApiService = {
  // Deposits
  getDeposits: (params?: any) =>
    getAdminApi().get('/deposits', { params }),

  updateDepositStatus: (id: string, data: { status: string; adminNotes?: string; txHash?: string; walletAddress?: string }) => {
    console.log('Updating deposit status:', id, JSON.stringify(data));
    return getAdminApi().put(`/deposits/${id}/status`, data);
  },

  updateDepositAmount: (id: string, data: { adminVerifiedAmount: number; amountCorrectionReason?: string }) => {
    console.log('Updating deposit amount:', id, JSON.stringify(data));
    return getAdminApi().put(`/deposits/${id}/amount`, data);
  },

  // Users
  getUsers: (params?: any) =>
    getAdminApi().get('/users', { params }),

  getUserById: (id: string) =>
    getAdminApi().get(`/users/${id}`),

  updateUser: (id: string, userData: any) =>
    getAdminApi().put(`/users/${id}`, userData),

  toggleAdminStatus: (id: string) =>
    getAdminApi().put(`/users/${id}/toggle-admin`),

  deleteUser: (id: string) =>
    getAdminApi().delete(`/users/${id}`),

  loginAsUser: (id: string) =>
    getAdminApi().post(`/users/${id}/login-as`),

  returnToAdmin: () =>
    getAdminApi().post('/return-to-admin'),

  // Transactions
  getTransactions: (params?: any) =>
    getAdminApi().get('/transactions', { params }),

  getTransactionById: (id: string) =>
    getAdminApi().get(`/transactions/${id}`),

  // Referrals
  getReferralStats: () =>
    getAdminApi().get('/referrals'),

  getReferralCommissions: (params?: any) =>
    getAdminApi().get('/commissions/admin', { params }),

  getReferralDetails: (userId: string) =>
    getAdminApi().get(`/referrals/${userId}`),

  // Note: updateTransactionStatus function removed as per requirements

  // Withdrawals
  getWithdrawals: (params?: any) =>
    getAdminApi().get('/withdrawals', { params }),

  // System Configuration
  getSystemConfig: () =>
    getAdminApi().get('/system/config'),

  updateSystemConfig: (data: any) =>
    getAdminApi().put('/system/config', data),

  // Crypto Addresses
  getCryptoAddresses: () =>
    getAdminApi().get('/system/crypto-addresses'),

  updateCryptoAddresses: (currency: string, addresses: string[]) =>
    getAdminApi().put(`/system/crypto-addresses/${currency}`, { addresses }),

  updateWithdrawalStatus: (id: string, data: { status: string; adminNotes?: string; txHash?: string; walletAddress?: string }) =>
    getAdminApi().put(`/withdrawals/${id}/status`, data),
};
