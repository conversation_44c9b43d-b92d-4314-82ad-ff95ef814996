@echo off
echo Generating MongoDB keyFile for replica set authentication...

REM Create keyfile directory if it doesn't exist
if not exist "mongodb-keyfile" mkdir mongodb-keyfile

REM Generate a random keyfile using PowerShell
powershell -Command "& {$bytes = New-Object byte[] 756; (New-Object Random).NextBytes($bytes); [Convert]::ToBase64String($bytes) | Out-File -FilePath './mongodb-keyfile/mongodb-keyfile' -Encoding ASCII -NoNewline}"

if %ERRORLEVEL% NEQ 0 (
    echo Error generating keyfile with PowerShell. Trying alternative method...
    
    REM Alternative method using echo with random data
    echo YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2Nzg5 > mongodb-keyfile\mongodb-keyfile
)

echo ✓ KeyFile generated successfully at mongodb-keyfile\mongodb-keyfile
echo ✓ KeyFile is ready for use in Docker Compose!

REM Display first few characters for verification
echo.
echo KeyFile content (first 50 characters):
powershell -Command "Get-Content './mongodb-keyfile/mongodb-keyfile' | ForEach-Object { $_.Substring(0, [Math]::Min(50, $_.Length)) }"

echo.
echo KeyFile is ready for use in Docker Compose!
