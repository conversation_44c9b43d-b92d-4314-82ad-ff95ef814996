import React, { useState } from 'react';
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Heading,
  Alert,
  AlertIcon,
  Text,
  useToast,
  Spinner,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

const SimpleLogin: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Test123!@#');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const toast = useToast();

  const handleDirectLogin = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🚀 SIMPLE LOGIN: Starting direct login...');
      console.log('📧 Email:', email);
      console.log('🔑 Password length:', password.length);
      
      // Method 1: Direct fetch to proxy
      console.log('📤 Method 1: Direct fetch to proxy...');
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      });
      
      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', response.headers);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('📥 Response data:', data);
      
      if (data.status === 'success' && data.data) {
        const userData = data.data;
        
        // Store user data
        localStorage.setItem('user', JSON.stringify(userData));
        console.log('✅ User data stored successfully');
        
        // Show success message
        toast({
          title: 'Login Successful!',
          description: `Welcome back, ${userData.firstName}!`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        
        // Redirect to profile
        setTimeout(() => {
          navigate('/profile');
        }, 1000);
        
      } else {
        throw new Error('Invalid response format');
      }
      
    } catch (error: any) {
      console.error('❌ Simple login failed:', error);
      setError(error.message);
      
      toast({
        title: 'Login Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMockLogin = () => {
    console.log('🎭 MOCK LOGIN: Creating mock user...');
    
    const mockUser = {
      _id: '6838bf7656339ea52d689084',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      country: 'TR',
      city: 'Istanbul',
      kycVerified: false,
      twoFactorEnabled: false,
      referralCode: 'C7D026FE',
      referralCount: 0,
      referralEarnings: 0,
      marketingConsent: false,
      isAdmin: false,
      lastLogin: new Date().toISOString()
    };
    
    localStorage.setItem('user', JSON.stringify(mockUser));
    console.log('✅ Mock user created and stored');
    
    toast({
      title: 'Mock Login Successful!',
      description: 'You are now logged in with a test account.',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
    
    setTimeout(() => {
      navigate('/profile');
    }, 1000);
  };

  const handleTestAPI = async () => {
    try {
      console.log('🧪 TESTING API CONNECTION...');
      
      // Test health endpoint
      const healthResponse = await fetch('/health');
      const healthData = await healthResponse.json();
      console.log('✅ Health check:', healthData);
      
      toast({
        title: 'API Test Successful',
        description: `Server status: ${healthData.status}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
    } catch (error: any) {
      console.error('❌ API test failed:', error);
      toast({
        title: 'API Test Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Container maxW="md" py={12}>
      <VStack spacing={8}>
        <Heading color="gold.400" textAlign="center">
          🚀 Simple Login
        </Heading>
        
        <Alert status="info">
          <AlertIcon />
          This is a simplified login page to bypass authentication issues.
        </Alert>

        <Box w="100%" bg="gray.800" p={8} borderRadius="lg" border="1px solid" borderColor="gray.600">
          <VStack spacing={6}>
            <FormControl>
              <FormLabel color="gray.300">Email</FormLabel>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="gray.600"
                color="white"
                _focus={{ borderColor: 'gold.400' }}
              />
            </FormControl>

            <FormControl>
              <FormLabel color="gray.300">Password</FormLabel>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="gray.600"
                color="white"
                _focus={{ borderColor: 'gold.400' }}
              />
            </FormControl>

            {error && (
              <Alert status="error">
                <AlertIcon />
                {error}
              </Alert>
            )}

            <VStack spacing={4} w="100%">
              <Button
                colorScheme="blue"
                size="lg"
                w="100%"
                onClick={handleDirectLogin}
                isLoading={loading}
                loadingText="Logging in..."
              >
                🚀 Direct Login
              </Button>

              <Button
                colorScheme="green"
                size="lg"
                w="100%"
                onClick={handleMockLogin}
                isDisabled={loading}
              >
                🎭 Mock Login (Bypass)
              </Button>

              <Button
                colorScheme="purple"
                size="md"
                w="100%"
                onClick={handleTestAPI}
                isDisabled={loading}
              >
                🧪 Test API Connection
              </Button>
            </VStack>
          </VStack>
        </Box>

        <Text color="gray.400" fontSize="sm" textAlign="center">
          Open browser console (F12) to see detailed logs
        </Text>
      </VStack>
    </Container>
  );
};

export default SimpleLogin;
