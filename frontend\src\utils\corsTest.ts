/**
 * CORS testing utilities for Safari compatibility
 */

import { isSafari, isIOSSafari } from './safariUtils';

interface CorsTestResult {
  success: boolean;
  error?: string;
  headers?: Record<string, string>;
  status?: number;
  timing?: number;
}

/**
 * Test CORS preflight request
 */
export const testCorsPreflight = async (url: string): Promise<CorsTestResult> => {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      method: 'OPTIONS',
      headers: {
        'Origin': window.location.origin,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
      },
      credentials: 'include'
    });
    
    const timing = Date.now() - startTime;
    const headers: Record<string, string> = {};
    
    // Extract CORS headers
    response.headers.forEach((value, key) => {
      if (key.toLowerCase().startsWith('access-control-')) {
        headers[key] = value;
      }
    });
    
    return {
      success: response.ok,
      status: response.status,
      headers,
      timing
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      timing: Date.now() - startTime
    };
  }
};

/**
 * Test actual CORS request
 */
export const testCorsRequest = async (url: string, method: string = 'GET'): Promise<CorsTestResult> => {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Origin': window.location.origin
      },
      credentials: 'include'
    });
    
    const timing = Date.now() - startTime;
    const headers: Record<string, string> = {};
    
    // Extract response headers
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });
    
    return {
      success: response.ok,
      status: response.status,
      headers,
      timing
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      timing: Date.now() - startTime
    };
  }
};

/**
 * Comprehensive CORS test suite
 */
export const runCorsTestSuite = async (baseUrl: string): Promise<{
  browser: string;
  results: Record<string, CorsTestResult>;
  summary: {
    total: number;
    passed: number;
    failed: number;
  };
}> => {
  const browser = isSafari() ? 'Safari' : isIOSSafari() ? 'iOS Safari' : 'Other';
  const results: Record<string, CorsTestResult> = {};
  
  console.log(`🧪 Running CORS test suite for ${browser}`);
  
  // Test 1: Health check endpoint
  console.log('Testing health check endpoint...');
  results.healthCheck = await testCorsRequest(`${baseUrl}/health`);
  
  // Test 2: Preflight for API endpoint
  console.log('Testing API preflight...');
  results.apiPreflight = await testCorsPreflight(`${baseUrl}/api/users/profile`);
  
  // Test 3: Actual API request
  console.log('Testing API request...');
  results.apiRequest = await testCorsRequest(`${baseUrl}/api/users/profile`);
  
  // Test 4: Static file request
  console.log('Testing static file request...');
  results.staticFile = await testCorsRequest(`${baseUrl}/uploads/test.jpg`);
  
  // Test 5: WebSocket connection (if applicable)
  if (typeof WebSocket !== 'undefined') {
    console.log('Testing WebSocket connection...');
    results.websocket = await testWebSocketConnection(`${baseUrl.replace('http', 'ws')}/ws`);
  }
  
  // Calculate summary
  const total = Object.keys(results).length;
  const passed = Object.values(results).filter(r => r.success).length;
  const failed = total - passed;
  
  return {
    browser,
    results,
    summary: { total, passed, failed }
  };
};

/**
 * Test WebSocket connection for CORS
 */
const testWebSocketConnection = async (url: string): Promise<CorsTestResult> => {
  const startTime = Date.now();
  
  return new Promise((resolve) => {
    try {
      const ws = new WebSocket(url);
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve({
          success: false,
          error: 'WebSocket connection timeout',
          timing: Date.now() - startTime
        });
      }, 5000);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        ws.close();
        resolve({
          success: true,
          timing: Date.now() - startTime
        });
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          error: 'WebSocket connection failed',
          timing: Date.now() - startTime
        });
      };
    } catch (error: any) {
      resolve({
        success: false,
        error: error.message,
        timing: Date.now() - startTime
      });
    }
  });
};

/**
 * Log CORS test results in a readable format
 */
export const logCorsTestResults = (testResults: Awaited<ReturnType<typeof runCorsTestSuite>>) => {
  console.group(`🧪 CORS Test Results for ${testResults.browser}`);
  
  console.log(`📊 Summary: ${testResults.summary.passed}/${testResults.summary.total} tests passed`);
  
  Object.entries(testResults.results).forEach(([testName, result]) => {
    const status = result.success ? '✅' : '❌';
    const timing = result.timing ? `(${result.timing}ms)` : '';
    
    console.log(`${status} ${testName} ${timing}`);
    
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    if (result.headers && Object.keys(result.headers).length > 0) {
      console.log('   Headers:', result.headers);
    }
  });
  
  console.groupEnd();
  
  // Show recommendations for Safari
  if (testResults.browser.includes('Safari') && testResults.summary.failed > 0) {
    console.group('🦎 Safari CORS Recommendations');
    console.log('• Ensure server sends Access-Control-Allow-Origin header');
    console.log('• Check that Access-Control-Allow-Credentials is set to true');
    console.log('• Verify preflight requests are handled correctly');
    console.log('• Consider adding Vary: Origin header');
    console.log('• Check for mixed content issues (HTTP/HTTPS)');
    console.groupEnd();
  }
};

/**
 * Run CORS test and log results (for development use)
 */
export const debugCors = async () => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('CORS debugging is only available in development mode');
    return;
  }
  
  const apiUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5000';
  const results = await runCorsTestSuite(apiUrl);
  logCorsTestResults(results);
  
  return results;
};

// Make debugCors available globally in development
if (process.env.NODE_ENV === 'development') {
  (window as any).debugCors = debugCors;
  console.log('🧪 CORS debugging available: call debugCors() in console');
}
