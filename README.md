# CryptoYield - Cryptocurrency Investment Platform

A modern, full-stack cryptocurrency investment platform built with React, Node.js, and MongoDB.

## Features

- **User Management**: Registration, authentication, profile management
- **Investment Packages**: Create and manage cryptocurrency investment packages
- **Wallet Management**: Multi-currency wallet with deposit/withdrawal functionality
- **Admin Panel**: Comprehensive admin dashboard for user and transaction management
- **Real-time Updates**: WebSocket integration for live data updates
- **Multi-language Support**: Internationalization with multiple language options
- **Responsive Design**: Mobile-first design with Chakra UI

## Tech Stack

### Frontend
- React 18 with TypeScript
- Vite for build tooling
- Chakra UI for components
- React Router for navigation
- i18next for internationalization
- Socket.IO for real-time communication

### Backend
- Node.js with Express
- TypeScript
- MongoDB with Mongoose
- JWT authentication
- Socket.IO for WebSocket
- Winston for logging
- Bull for job queues

### Infrastructure
- Docker & Docker Compose
- MongoDB replica set
- Redis for caching and queues

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)

### Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd cryptoyield
```

2. Start the development environment:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

3. Access the application:
- Frontend: http://localhost:3004
- Backend API: http://localhost:5000
- MongoDB: localhost:27017

### Environment Configuration

The application uses environment-specific configuration files:
- `.env` - Base configuration
- `.env.development` - Development overrides
- `.env.production` - Production configuration

## Project Structure

```
cryptoyield/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── context/        # React contexts
│   │   ├── hooks/          # Custom hooks
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── backend/                 # Node.js backend application
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── models/         # MongoDB models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── middleware/     # Express middleware
│   │   └── utils/          # Utility functions
│   └── scripts/            # Database scripts
├── contracts/              # Smart contracts
└── docker-compose.*.yml    # Docker configurations
```

## API Documentation

The backend provides RESTful APIs for:
- User authentication and management
- Investment package operations
- Wallet and transaction management
- Admin operations
- Real-time WebSocket events

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is proprietary software. All rights reserved.
