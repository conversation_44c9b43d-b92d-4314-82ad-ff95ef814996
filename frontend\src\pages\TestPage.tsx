import React, { useState } from 'react';
import {
  Box,
  Button,
  VStack,
  Text,
  Input,
  FormControl,
  FormLabel,
  Alert,
  AlertIcon,
  Heading,
  useToast,
} from '@chakra-ui/react';

const TestPage: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const toast = useToast();

  const testBackendConnection = async () => {
    try {
      setLoading(true);
      setResult(null);

      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      console.log('Testing backend connection to:', API_URL);

      // Test health endpoint
      const healthResponse = await fetch(`http://localhost:5000/health`);
      const healthData = await healthResponse.json();
      console.log('Health check:', healthData);

      // Test maintenance status
      const maintenanceResponse = await fetch(`${API_URL}/system/maintenance-status`);
      const maintenanceData = await maintenanceResponse.json();
      console.log('Maintenance status:', maintenanceData);

      setResult({
        health: healthData,
        maintenance: maintenanceData,
        apiUrl: API_URL,
      });

      toast({
        title: 'Backend Connection Test',
        description: 'Successfully connected to backend!',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Backend connection test failed:', error);
      setResult({ error: error.message });
      toast({
        title: 'Backend Connection Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    try {
      setLoading(true);
      setResult(null);

      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      console.log('🔧 Testing login with API_URL:', API_URL);

      // Test with both fetch and axios
      console.log('🚀 Testing with fetch...');
      try {
        const fetchResponse = await fetch(`${API_URL}/users/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ email, password }),
        });

        const fetchData = await fetchResponse.json();
        console.log('✅ Fetch response:', fetchData);

        setResult({
          fetchTest: {
            success: true,
            status: fetchResponse.status,
            data: fetchData
          }
        });

        toast({
          title: 'Fetch Login Test',
          description: 'Fetch login successful!',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } catch (fetchError) {
        console.error('❌ Fetch error:', fetchError);

        // Now test with axios
        console.log('🚀 Testing with axios...');
        const axios = (await import('axios')).default;

        try {
          const axiosResponse = await axios.post(`${API_URL}/users/login`, {
            email,
            password,
          }, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
            }
          });

          console.log('✅ Axios response:', axiosResponse.data);

          setResult({
            fetchTest: {
              success: false,
              error: fetchError.message
            },
            axiosTest: {
              success: true,
              status: axiosResponse.status,
              data: axiosResponse.data
            }
          });

          toast({
            title: 'Axios Login Test',
            description: 'Axios login successful!',
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
        } catch (axiosError) {
          console.error('❌ Axios error:', axiosError);

          setResult({
            fetchTest: {
              success: false,
              error: fetchError.message
            },
            axiosTest: {
              success: false,
              error: axiosError.message,
              details: {
                code: axiosError.code,
                response: axiosError.response?.data,
                status: axiosError.response?.status
              }
            }
          });

          toast({
            title: 'Both Tests Failed',
            description: `Fetch: ${fetchError.message}, Axios: ${axiosError.message}`,
            status: 'error',
            duration: 10000,
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.error('❌ General test error:', error);
      setResult({ error: error.message });
      toast({
        title: 'Test Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box minH="100vh" bg="#0B0E11" p={8}>
      <VStack spacing={6} maxW="600px" mx="auto">
        <Heading color="#FCD535" size="lg">
          Backend Connection Test
        </Heading>

        <Alert status="info">
          <AlertIcon />
          This page bypasses the maintenance mode check to test backend connectivity directly.
        </Alert>

        <Box w="full" bg="#1E2026" p={6} borderRadius="lg">
          <VStack spacing={4}>
            <Button
              onClick={testBackendConnection}
              isLoading={loading}
              colorScheme="yellow"
              size="lg"
              w="full"
            >
              Test Backend Connection
            </Button>

            <FormControl>
              <FormLabel color="#EAECEF">Email</FormLabel>
              <Input
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                bg="#0B0E11"
                color="#EAECEF"
                borderColor="#3C4043"
              />
            </FormControl>

            <FormControl>
              <FormLabel color="#EAECEF">Password</FormLabel>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                bg="#0B0E11"
                color="#EAECEF"
                borderColor="#3C4043"
              />
            </FormControl>

            <Button
              onClick={testLogin}
              isLoading={loading}
              colorScheme="green"
              size="lg"
              w="full"
            >
              Test Login
            </Button>
          </VStack>
        </Box>

        {result && (
          <Box w="full" bg="#1E2026" p={6} borderRadius="lg">
            <Text color="#EAECEF" fontWeight="bold" mb={4}>
              Test Result:
            </Text>
            <Box
              as="pre"
              color="#EAECEF"
              fontSize="sm"
              bg="#0B0E11"
              p={4}
              borderRadius="md"
              overflow="auto"
              maxH="400px"
            >
              {JSON.stringify(result, null, 2)}
            </Box>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default TestPage;
