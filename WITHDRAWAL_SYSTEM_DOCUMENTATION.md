# CryptoYield Withdrawal System Documentation

## Overview

The CryptoYield Withdrawal System is a comprehensive solution for managing cryptocurrency withdrawals with advanced security features, multi-type balance support, and administrative controls.

## Features

### User Features
- **3-Step Withdrawal Process**: Asset Selection → Wallet Validation → Confirmation
- **Multiple Withdrawal Types**: Main Balance, Interest Earnings, Commission Earnings
- **Multi-Cryptocurrency Support**: BTC, ETH, USDT, BNB, SOL, DOGE, TRX
- **Network Selection**: Support for multiple blockchain networks per cryptocurrency
- **Real-time Validation**: Address format, balance checks, minimum amount validation
- **30-Day Lock Protection**: Main balance withdrawals require 30-day investment lock
- **USD Conversion**: Real-time USD equivalent calculation with $50 minimum
- **Withdrawal History**: Complete transaction history with detailed information

### Admin Features
- **Comprehensive Management**: View, filter, and manage all withdrawal requests
- **Status Management**: Approve, reject, complete, or mark as failed
- **Fund Restoration**: Automatic fund restoration on withdrawal rejection
- **Detailed Analytics**: Withdrawal statistics and reporting
- **Bulk Operations**: Bulk approve/reject multiple withdrawals
- **Export Functionality**: Export withdrawal data to CSV
- **Real-time Updates**: Live status updates and notifications

## Architecture

### Backend Components

#### Models
- **Withdrawal Model** (`withdrawalModel.ts`): Core withdrawal data structure
- **Wallet Model**: User wallet with multi-asset support
- **Investment Package Model**: For 30-day lock validation
- **Transaction Model**: Corresponding transaction records

#### Services
- **Withdrawal Service** (`withdrawalService.ts`): Core business logic
- **Crypto API Service** (`cryptoApiService.ts`): USD conversion and price data
- **Admin Withdrawal Service**: Administrative operations

#### Controllers
- **Withdrawal Controller** (`withdrawalController.ts`): User endpoints
- **Admin Withdrawal Controller** (`adminWithdrawalController.ts`): Admin endpoints

#### Routes
- **User Routes** (`/api/withdrawals`): User withdrawal operations
- **Admin Routes** (`/api/admin/withdrawals`): Administrative operations

### Frontend Components

#### User Interface
- **WithdrawModal**: 3-step withdrawal process modal
- **WithdrawalHistory**: User withdrawal history component
- **Wallet Integration**: Seamless integration with wallet page

#### Admin Interface
- **WithdrawalManagement**: Complete admin withdrawal management
- **Real-time Updates**: Live status updates and notifications
- **Advanced Filtering**: Multi-criteria filtering and search

#### Services
- **Withdrawal Service** (`withdrawalService.ts`): User operations
- **Admin Withdrawal Service** (`adminWithdrawalService.ts`): Admin operations

## API Endpoints

### User Endpoints

#### POST /api/withdrawals/validate
Validate withdrawal request before submission.

**Request Body:**
```json
{
  "cryptocurrency": "BTC",
  "withdrawalType": "interest",
  "amount": 0.001,
  "walletAddress": "**********************************",
  "network": "Bitcoin"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "errors": [],
    "warnings": [],
    "availableBalance": 0.005,
    "usdValue": 50.25,
    "lockInfo": {
      "isLocked": false,
      "daysRemaining": 0,
      "unlockDate": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### POST /api/withdrawals/submit
Submit withdrawal request.

**Request Body:**
```json
{
  "cryptocurrency": "BTC",
  "withdrawalType": "interest",
  "amount": 0.001,
  "walletAddress": "**********************************",
  "network": "Bitcoin"
}
```

#### GET /api/withdrawals/history
Get user withdrawal history with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

#### GET /api/withdrawals/balance/:crypto?
Get withdrawable balances for all or specific cryptocurrency.

### Admin Endpoints

#### GET /api/admin/withdrawals
Get all withdrawal requests with filtering and pagination.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `status`: Filter by status
- `cryptocurrency`: Filter by cryptocurrency
- `withdrawalType`: Filter by withdrawal type
- `search`: Search by user, email, or address

#### PUT /api/admin/withdrawals/:id/status
Update withdrawal status.

**Request Body:**
```json
{
  "status": "approved",
  "adminNotes": "Approved after verification",
  "txHash": "0x..."
}
```

#### PUT /api/admin/withdrawals/:id/amount
Update withdrawal amount (pending withdrawals only).

**Request Body:**
```json
{
  "amount": 0.0015,
  "adminNotes": "Amount adjusted due to fees"
}
```

## Business Logic

### Withdrawal Types

#### Main Balance (balance)
- Source: User's main wallet balance from approved deposits
- Lock Period: 30 days from first investment
- Minimum: $50 USD equivalent
- Validation: Investment package creation date check

#### Interest Earnings (interest)
- Source: Interest earned from investments
- Lock Period: None
- Minimum: $50 USD equivalent
- Validation: Available interest balance check

#### Commission Earnings (commission)
- Source: Referral commissions earned
- Lock Period: None
- Minimum: $50 USD equivalent
- Validation: Available commission balance check

### Validation Rules

1. **Balance Validation**: Ensure sufficient funds in selected balance type
2. **30-Day Lock**: Main balance requires 30 days since first investment
3. **Minimum Amount**: $50 USD equivalent using real-time conversion
4. **Address Format**: Cryptocurrency-specific address validation
5. **Network Compatibility**: Ensure network supports selected cryptocurrency

### Status Flow

```
pending → approved → completed
       ↘ rejected
       ↘ failed
```

- **Pending**: Initial status after submission
- **Approved**: Admin approved, ready for processing
- **Rejected**: Admin rejected, funds restored
- **Completed**: Successfully processed
- **Failed**: Processing failed, funds restored

## Security Features

### Authentication & Authorization
- JWT token authentication for all endpoints
- Role-based access control (user vs admin)
- User isolation (users can only access their own data)

### Validation & Sanitization
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting on withdrawal endpoints

### Fund Protection
- Atomic transactions using MongoDB sessions
- Automatic fund restoration on rejection/failure
- Balance deduction only after successful validation
- Audit trail for all administrative actions

### Address Validation
- Cryptocurrency-specific address format validation
- Network compatibility checks
- Checksum validation where applicable

## Error Handling

### User Errors
- Insufficient balance
- Invalid wallet address
- Minimum amount not met
- 30-day lock violation
- Network incompatibility

### System Errors
- Database connection issues
- External API failures
- Network timeouts
- Validation service errors

### Admin Errors
- Invalid status transitions
- Unauthorized access
- Missing required fields
- Concurrent modification conflicts

## Performance Optimization

### Caching
- Price data caching (5-minute TTL)
- Exchange rate caching
- User balance caching

### Database Optimization
- Proper indexing on frequently queried fields
- Pagination for large datasets
- Optimized aggregation queries

### API Optimization
- Rate limiting to prevent abuse
- Request/response compression
- Efficient data serialization

## Monitoring & Logging

### Audit Logging
- All withdrawal operations logged
- Admin actions tracked with user ID
- Status change history maintained
- Fund restoration events logged

### Metrics
- Withdrawal success/failure rates
- Processing times
- Popular cryptocurrencies
- Admin response times

### Alerts
- Failed withdrawal notifications
- Suspicious activity detection
- System error alerts
- High-value transaction alerts

## Testing

### Unit Tests
- Service layer testing
- Validation logic testing
- Business rule verification
- Error handling testing

### Integration Tests
- API endpoint testing
- Database integration testing
- External service integration
- End-to-end workflow testing

### Security Tests
- Authentication testing
- Authorization testing
- Input validation testing
- SQL injection prevention

## Deployment

### Environment Variables
```env
MONGODB_URI=mongodb://localhost:27017/cryptoyield
JWT_SECRET=your-jwt-secret
BINANCE_API_KEY=your-binance-api-key
COINGECKO_API_KEY=your-coingecko-api-key
```

### Database Setup
1. MongoDB with replica set for transaction support
2. Proper indexing for performance
3. Regular backups and monitoring

### Production Considerations
- Load balancing for high availability
- Database clustering for scalability
- Monitoring and alerting setup
- Regular security audits

## Future Enhancements

### Planned Features
- Multi-signature wallet support
- Advanced fee calculation
- Scheduled withdrawals
- Withdrawal limits and quotas
- Enhanced reporting and analytics

### Scalability Improvements
- Microservices architecture
- Event-driven processing
- Caching layer optimization
- Database sharding

## Support & Maintenance

### Regular Tasks
- Monitor withdrawal processing times
- Review and update minimum amounts
- Update supported cryptocurrencies
- Security patch management

### Troubleshooting
- Check logs for error patterns
- Verify external API connectivity
- Monitor database performance
- Review user feedback and issues

For technical support or questions, please contact the development team.
