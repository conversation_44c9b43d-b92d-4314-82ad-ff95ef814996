import React from 'react';
import {
  Box,
  Container,
  Heading,
  VStack,
  Button,
  Text,
  Flex,
  Icon,
  useToast
} from '@chakra-ui/react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { FaFileContract, FaUserShield, FaHome, FaCheckCircle } from 'react-icons/fa';

const RouteTestPage = () => {
  const navigate = useNavigate();
  const toast = useToast();

  const testRoutes = [
    { path: '/privacy', name: 'Privacy Policy', icon: FaUserShield },
    { path: '/terms', name: 'Terms of Service', icon: FaFileContract },
    { path: '/', name: 'Home', icon: FaHome }
  ];

  const testRoute = (path: string, name: string) => {
    try {
      navigate(path);
      toast({
        title: 'Navigation Test',
        description: `Successfully navigated to ${name}`,
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Navigation Error',
        description: `Failed to navigate to ${name}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <Box
      minH="100vh"
      bgGradient="linear(135deg, #0B0E11 0%, #1A1D29 25%, #0B0E11 50%, #1A1D29 75%, #0B0E11 100%)"
      py={12}
    >
      <Container maxW="4xl" px={{ base: 4, md: 8 }}>
        <Box
          bg="rgba(30, 32, 38, 0.8)"
          backdropFilter="blur(20px)"
          borderRadius="2xl"
          border="1px solid rgba(252, 213, 53, 0.2)"
          p={{ base: 6, md: 8 }}
          position="relative"
          overflow="hidden"
        >
          {/* Background gradient overlay */}
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bgGradient="radial(circle at 30% 20%, rgba(252, 213, 53, 0.1) 0%, transparent 50%)"
            pointerEvents="none"
          />

          <VStack spacing={8} align="start" position="relative" zIndex={1}>
            <Flex align="center" w="full" mb={2}>
              <Box
                p={3}
                borderRadius="lg"
                bg="rgba(252, 213, 53, 0.15)"
                display="flex"
                alignItems="center"
                justifyContent="center"
                mr={4}
              >
                <Icon as={FaCheckCircle} color="#FCD535" boxSize={6} />
              </Box>
              <Heading
                fontSize={{ base: '2xl', md: '3xl' }}
                fontWeight="900"
                bgGradient="linear(135deg, #FCD535 0%, #F8D12F 50%, #FCD535 100%)"
                bgClip="text"
                letterSpacing="tight"
              >
                Route Test Page
              </Heading>
            </Flex>

            <Text color="#EAECEF" fontSize="lg">
              Test navigation to Privacy Policy and Terms of Service pages
            </Text>

            <VStack spacing={4} w="full">
              {testRoutes.map((route) => (
                <Flex key={route.path} w="full" gap={4}>
                  <Button
                    as={RouterLink}
                    to={route.path}
                    leftIcon={<Icon as={route.icon} />}
                    colorScheme="yellow"
                    bg="#FCD535"
                    color="#0B0E11"
                    _hover={{
                      bg: '#F8D12F',
                      transform: 'translateY(-2px)',
                    }}
                    _active={{
                      transform: 'translateY(0)',
                    }}
                    transition="all 0.2s"
                    size="lg"
                    flex="1"
                  >
                    Go to {route.name}
                  </Button>
                  
                  <Button
                    onClick={() => testRoute(route.path, route.name)}
                    variant="outline"
                    borderColor="#FCD535"
                    color="#FCD535"
                    _hover={{
                      bg: 'rgba(252, 213, 53, 0.1)',
                    }}
                    size="lg"
                  >
                    Test Navigate
                  </Button>
                </Flex>
              ))}
            </VStack>

            <Box
              w="full"
              p={4}
              bg="rgba(252, 213, 53, 0.1)"
              borderRadius="lg"
              border="1px solid rgba(252, 213, 53, 0.3)"
            >
              <Text color="#FCD535" fontWeight="600" mb={2}>
                ✅ Routes Status:
              </Text>
              <VStack align="start" spacing={1}>
                <Text color="#EAECEF" fontSize="sm">
                  • /privacy → PrivacyPolicy.tsx
                </Text>
                <Text color="#EAECEF" fontSize="sm">
                  • /terms → TermsOfService.tsx
                </Text>
                <Text color="#EAECEF" fontSize="sm">
                  • Both routes are PUBLIC (no authentication required)
                </Text>
                <Text color="#EAECEF" fontSize="sm">
                  • Lazy loaded for performance optimization
                </Text>
              </VStack>
            </Box>
          </VStack>
        </Box>
      </Container>
    </Box>
  );
};

export default RouteTestPage;
