# 💰 Manual Interest Calculation Commands

Khi cronjob daily interest bị lỗi, bạn có thể chạy interest calculation bằng tay với các commands sau:

## 🚀 **Quick Commands**

### **1. Quick Interest Fix (Recommended)**
```bash
# Chạy nhanh, ít logging
npm run quick-interest

# Với Docker environment
npm run quick-interest:docker
```

### **2. Manual Interest (Detailed)**
```bash
# Chạy với logging chi tiết
npm run manual-interest

# Với Docker environment  
npm run manual-interest:docker
```

### **3. Test Interest (Development)**
```bash
# Test với sample data
npm run test-interest

# Với Docker environment
npm run test-interest:docker
```

## 📋 **Khi nào sử dụng**

### **🔥 Emergency - Cronjob Failed**
```bash
npm run quick-interest:docker
```
- ⚡ Chạy nhanh nhất
- 📊 Kết quả ngắn gọn
- 🎯 Dùng khi cần fix gấp

### **🔍 Investigation - Need Details**
```bash
npm run manual-interest:docker
```
- 📝 Logging chi tiết
- 🔍 Hiển thị errors/warnings
- 📊 Statistics đầy đủ
- 🎯 Dùng khi cần debug

### **🧪 Testing - Development**
```bash
npm run test-interest:docker
```
- 🧪 Tạo test data nếu cần
- 📋 Test toàn bộ flow
- 🎯 Dùng khi develop/test

## 📊 **Expected Output**

### **Quick Interest Fix:**
```
⚡ Quick Interest Fix - Starting...
✅ Database connected
🚀 Running interest calculation...

📊 RESULTS:
   Packages: 15
   Success: 15
   Failed: 0
   Interest: 150.000000 USDT
   Duration: 2.34s

🎉 SUCCESS: All packages processed!
✅ Database closed
```

### **Manual Interest (Detailed):**
```
💰 MANUAL DAILY INTEREST CALCULATION
====================================
🔌 Connecting to database...
✅ Database connected successfully

🔍 Checking system status...
📊 System Status:
   Active packages: 15
   Packages needing calculation: 15

🚀 Starting manual daily interest calculation...

📊 MANUAL INTEREST CALCULATION RESULTS
======================================
📦 Total packages processed: 15
✅ Successful calculations: 15
❌ Failed calculations: 0
💰 Total interest paid: 150.000000 USDT
⏱️  Processing duration: 2340ms (2.34s)
📈 Success rate: 100.00%
💵 Average interest per package: 10.000000 USDT

🎉 Manual interest calculation completed successfully!
✅ All packages processed without errors.
```

## 🚨 **Error Handling**

### **If packages fail:**
```
❌ ERRORS ENCOUNTERED:
  1. Package PKG-123456:
     Error: Insufficient balance
     Details: User wallet has 0 USDT

⚠️  Manual interest calculation completed with errors.
❌ 1 packages failed to process.
   Check the error details above and fix the issues.
```

### **Common fixes:**
1. **Insufficient balance:** Check user wallets
2. **Package not found:** Verify package status
3. **Database connection:** Check MongoDB
4. **Permission errors:** Check user permissions

## 🔧 **Troubleshooting**

### **Command not found:**
```bash
cd backend
npm run quick-interest:docker
```

### **Database connection error:**
```bash
# Check .env.docker file
cat .env.docker | grep MONGO_URI

# Test connection
npm run quick-interest:docker
```

### **Permission denied:**
```bash
# Make scripts executable
chmod +x scripts/*.js
```

### **Module not found:**
```bash
# Install dependencies
npm install
```

## 📅 **Scheduling**

### **Manual cron setup:**
```bash
# Edit crontab
crontab -e

# Add daily interest at 00:01
1 0 * * * cd /path/to/backend && npm run quick-interest:docker >> /var/log/interest.log 2>&1
```

### **Docker cron:**
```bash
# Inside container
echo "1 0 * * * cd /app && npm run quick-interest:docker" | crontab -
```

## 🎯 **Best Practices**

### **1. Check before running:**
```bash
# Check active packages first
npm run manual-interest:docker
```

### **2. Monitor results:**
```bash
# Save output to file
npm run quick-interest:docker > interest-$(date +%Y%m%d).log 2>&1
```

### **3. Verify after running:**
```bash
# Check recent transactions
# Check user wallets
# Verify interest distribution
```

### **4. Emergency procedure:**
```bash
# 1. Quick fix
npm run quick-interest:docker

# 2. If errors, investigate
npm run manual-interest:docker

# 3. Fix issues and retry
npm run quick-interest:docker
```

## 📞 **Support**

Nếu gặp vấn đề:
1. 📋 Check logs trong output
2. 🔍 Run manual-interest để có details
3. 🛠️ Fix errors theo hướng dẫn
4. 🔄 Retry với quick-interest
5. 📞 Contact dev team nếu cần

---

**💡 Tip:** Luôn chạy `quick-interest:docker` trước để fix nhanh, sau đó dùng `manual-interest:docker` để investigate nếu có lỗi.
