#!/usr/bin/env npx ts-node

/**
 * <PERSON>ript để kiểm tra trạng thái các crypto jobs và cron jobs
 * 
 * Chạy script: npx ts-node backend/scripts/check-crypto-jobs.ts
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import { cryptoDataCollectionJob } from '../src/jobs/cryptoDataCollectionJob';
import { cryptoDataCollectionService } from '../src/services/cryptoDataCollectionService';
import CryptoPriceHistory from '../src/models/cryptoPriceHistoryModel';
import CryptoCurrency from '../src/models/cryptoCurrencyModel';
import { logger } from '../src/utils/logger';

// Load environment variables
config();

interface JobStatus {
  name: string;
  status: 'RUNNING' | 'STOPPED' | 'ERROR';
  details: any;
}

class CryptoJobChecker {
  private results: JobStatus[] = [];

  private addResult(name: string, status: 'RUNNING' | 'STOPPED' | 'ERROR', details: any) {
    this.results.push({ name, status, details });
  }

  /**
   * Kết nối database
   */
  async connectDatabase(): Promise<void> {
    try {
      const mongoURI = process.env.MONGO_URI || 'mongodb://mongodb:27017/cryptoyield?replicaSet=rs0';
      await mongoose.connect(mongoURI);
      console.log('✅ Kết nối database thành công');
    } catch (error: any) {
      console.error('❌ Lỗi kết nối database:', error.message);
      throw error;
    }
  }

  /**
   * Kiểm tra trạng thái crypto data collection job
   */
  async checkCryptoDataJob(): Promise<void> {
    try {
      const status = cryptoDataCollectionJob.getStatus();
      
      if (status.isScheduled && !status.isRunning) {
        this.addResult('Crypto Data Collection Job', 'RUNNING', {
          scheduled: status.isScheduled,
          running: status.isRunning,
          nextRun: status.nextRun
        });
      } else if (status.isRunning) {
        this.addResult('Crypto Data Collection Job', 'RUNNING', {
          scheduled: status.isScheduled,
          running: status.isRunning,
          note: 'Currently executing'
        });
      } else {
        this.addResult('Crypto Data Collection Job', 'STOPPED', {
          scheduled: status.isScheduled,
          running: status.isRunning
        });
      }
    } catch (error: any) {
      this.addResult('Crypto Data Collection Job', 'ERROR', {
        error: error.message
      });
    }
  }

  /**
   * Kiểm tra collection stats
   */
  async checkCollectionStats(): Promise<void> {
    try {
      const stats = cryptoDataCollectionService.getCollectionStats();
      const cacheStats = cryptoDataCollectionService.getCacheStats();
      
      this.addResult('Collection Statistics', 'RUNNING', {
        totalSymbols: stats.totalSymbols,
        successful: stats.successfulCollections,
        failed: stats.failedCollections,
        savedToDatabase: stats.savedToDatabase,
        lastCollection: stats.lastCollectionTime,
        errors: stats.errors,
        cache: cacheStats
      });
    } catch (error: any) {
      this.addResult('Collection Statistics', 'ERROR', {
        error: error.message
      });
    }
  }

  /**
   * Kiểm tra database status
   */
  async checkDatabaseStatus(): Promise<void> {
    try {
      // Kiểm tra số lượng cryptocurrency
      const cryptoCount = await CryptoCurrency.countDocuments();
      const enabledCryptoCount = await CryptoCurrency.countDocuments({ enabled: true });
      
      // Kiểm tra số lượng price records
      const priceRecordCount = await CryptoPriceHistory.countDocuments();
      
      // Lấy latest prices
      const latestPrices = await CryptoPriceHistory.getAllLatestPrices();
      
      // Kiểm tra price records trong 24h qua
      const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const recentPriceCount = await CryptoPriceHistory.countDocuments({
        timestamp: { $gte: last24h }
      });
      
      this.addResult('Database Status', 'RUNNING', {
        cryptocurrencies: {
          total: cryptoCount,
          enabled: enabledCryptoCount
        },
        priceRecords: {
          total: priceRecordCount,
          last24h: recentPriceCount,
          latestPricesCount: latestPrices.length
        },
        latestPrices: latestPrices.slice(0, 3).map(p => ({
          symbol: p.symbol,
          price: p.price,
          timestamp: p.timestamp,
          source: p.source
        }))
      });
    } catch (error: any) {
      this.addResult('Database Status', 'ERROR', {
        error: error.message
      });
    }
  }

  /**
   * Test manual collection
   */
  async testManualCollection(): Promise<void> {
    try {
      console.log('🔄 Testing manual price collection...');
      const startTime = Date.now();
      
      const results = await cryptoDataCollectionService.collectAllPriceData();
      const duration = Date.now() - startTime;
      
      this.addResult('Manual Collection Test', 'RUNNING', {
        duration: `${duration}ms`,
        collectedCount: results.length,
        symbols: results.map(r => r.symbol),
        samplePrices: results.slice(0, 3).map(r => ({
          symbol: r.symbol,
          price: r.price,
          source: r.source
        }))
      });
    } catch (error: any) {
      this.addResult('Manual Collection Test', 'ERROR', {
        error: error.message
      });
    }
  }

  /**
   * Kiểm tra API endpoints
   */
  async checkAPIEndpoints(): Promise<void> {
    try {
      // Test lấy latest prices từ database
      const latestPrices = await cryptoDataCollectionService.getAllLatestPricesFromDB();
      
      // Test lấy price history
      const btcHistory = await cryptoDataCollectionService.getPriceHistory('BTC', undefined, undefined, 5);
      
      this.addResult('API Endpoints', 'RUNNING', {
        latestPricesAPI: {
          count: latestPrices.length,
          working: latestPrices.length > 0
        },
        priceHistoryAPI: {
          btcRecords: btcHistory.length,
          working: btcHistory.length >= 0
        }
      });
    } catch (error: any) {
      this.addResult('API Endpoints', 'ERROR', {
        error: error.message
      });
    }
  }

  /**
   * Chạy tất cả kiểm tra
   */
  async runAllChecks(): Promise<void> {
    console.log('🔍 Bắt đầu kiểm tra trạng thái crypto jobs...\n');

    try {
      await this.connectDatabase();
      await this.checkCryptoDataJob();
      await this.checkCollectionStats();
      await this.checkDatabaseStatus();
      await this.testManualCollection();
      await this.checkAPIEndpoints();
    } catch (error) {
      console.error('❌ Kiểm tra bị dừng do lỗi nghiêm trọng:', error);
    } finally {
      await mongoose.disconnect();
    }

    this.printResults();
  }

  /**
   * In kết quả kiểm tra
   */
  private printResults(): void {
    console.log('\n📊 KẾT QUẢ KIỂM TRA CRYPTO JOBS');
    console.log('='.repeat(50));

    this.results.forEach(result => {
      const statusIcon = result.status === 'RUNNING' ? '✅' : result.status === 'STOPPED' ? '⏸️' : '❌';
      console.log(`\n${statusIcon} ${result.name.toUpperCase()}: ${result.status}`);
      console.log('-'.repeat(40));
      
      if (result.details) {
        console.log(JSON.stringify(result.details, null, 2));
      }
    });

    // Tổng kết
    const runningCount = this.results.filter(r => r.status === 'RUNNING').length;
    const stoppedCount = this.results.filter(r => r.status === 'STOPPED').length;
    const errorCount = this.results.filter(r => r.status === 'ERROR').length;

    console.log('\n📈 TỔNG KẾT');
    console.log('='.repeat(30));
    console.log(`✅ Đang chạy: ${runningCount}`);
    console.log(`⏸️ Đã dừng: ${stoppedCount}`);
    console.log(`❌ Lỗi: ${errorCount}`);
    console.log(`📊 Tổng cộng: ${this.results.length}`);

    if (errorCount === 0 && stoppedCount === 0) {
      console.log('\n🎉 Tất cả crypto jobs đều hoạt động bình thường!');
    } else if (errorCount > 0) {
      console.log('\n⚠️ Có lỗi trong hệ thống, vui lòng kiểm tra lại.');
    } else {
      console.log('\n⚠️ Có một số job đã dừng.');
    }

    // Recommendations
    console.log('\n💡 KHUYẾN NGHỊ');
    console.log('='.repeat(30));
    console.log('- Crypto data collection job nên chạy mỗi giờ');
    console.log('- Database nên có ít nhất 7 cryptocurrency được enable');
    console.log('- Price records nên được cập nhật trong 24h qua');
    console.log('- API endpoints nên trả về dữ liệu hợp lệ');
  }
}

// Chạy kiểm tra
const checker = new CryptoJobChecker();
checker.runAllChecks().catch(console.error);
